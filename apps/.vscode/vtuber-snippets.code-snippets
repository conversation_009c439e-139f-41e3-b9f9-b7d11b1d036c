{
	// Place your vtuber workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and 
	// description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope 
	// is left empty or omitted, the snippet gets applied to all languages. The prefix is what is 
	// used to trigger the snippet and the body will be expanded and inserted. Possible variables are: 
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. 
	// Placeholders with the same ids are connected.
	// Example:
	// "Print to console": {
	// 	"scope": "javascript,typescript",
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// }
	"SQL Name": {
		"scope": "sql",
		"prefix": "sqlname",
		"body": [
			"-- name: $1 :${2|one,many,exec|}",
		],
		"description": "Write SQL Name"
	},
	"SQL Yaml Assist": {
		"scope": "yaml",
		"prefix": "sqlcyaml",
		"body": [
			"- engine: \"postgresql\"",
			"  queries: \"internal/$1/${2:v1}/${3:repo}/queries.sql\"",
			"  schema: \"internal/db/migrations\"",
			"  gen:",
			"    go:",
			"      package: \"$1${2:v1}${3:repo}\"",
			"      out: \"internal/$1/${2:v1}/${3:repo}\"",
			"      sql_package: \"pgx/v5\"",
			"      omit_unused_structs: true",
			"      emit_pointers_for_null_types: true"
		],
		"description": "Write in SQLC Yaml"
	},
	"Session User": {
		"scope": "go",
		"prefix": "getSessionUser",
		"body": [
			"sessionUser := web.GetUserFromContext(ctx)",
		],
		"description": "Write Session User"
	},
	"Time At": {
		"scope": "go",
		"prefix": "timeAt",
		"body": [
			"$1: timestamppb.New($2.$1)",
		],
		"description": "Write TimeAt"
	},
	"Return Connect Response": {
		"scope": "go",
		"prefix": "return",
		"body": [
			"return connect.NewResponse($1), nil",
		],
		"description": "Write Return Connect Response"
	},
	"Return Generic Response": {
		"scope": "go",
		"prefix": "returnGenericResponse",
		"body": [
			"return connect.NewResponse(&sharedv1.GenericResponse{",
			"\tStatus:  200,",
			"\tMessage: \"$1\",",
			"\tSuccess: true,",
			"}), nil"
		],
		"description": "Write Return Connect Generic Response"
	},
	"Export client": {
		"scope": "typescript",
		"prefix": "exportClient",
		"body": [
			"export const $1 = createClient($2, transport);",
		],
		"description": "Write Export client"
	},
	"New Service": {
		"scope": "go",
		"prefix": "newService",
		"body": [
			"$1Service := ${2:v1}.New$3(${4:connection})",
		],
		"description": "Write New Service"
	},
	"New Service Handler And Path": {
		"scope": "go",
		"prefix": "newPathAndHandler",
		"body": [
			"$1Path, $1Handler := $2v1connect.New$3ServiceHandler($1Service, ${4:withInterceptors})",
		],
		"description": "Write New Service Handler and Path"
	},
	"New Mux Handle": {
		"scope": "go",
		"prefix": "muxHandle",
		"body": [
			"mux.Handle($1Path, withCORS($1Handler))",
		],
		"description": "Write New Mux Handle"
	},
	"Add required gotag in proto": {
		"scope": "proto3",
		"prefix": "gotagRequired",
		"body": [
			"// @gotag: validate:\"required\"",
		],
		"description": "Write required gotag in proto"
	},
	"Add ordering in SQLC": {
		"scope": "sql",
		"prefix": "ordering",
		"body": [
			"CASE WHEN sqlc.arg('sort')::TEXT = '$1' AND sqlc.arg('order')::TEXT = 'ASC' THEN $1 END ASC,",
    		"CASE WHEN sqlc.arg('sort')::TEXT = '$1' AND sqlc.arg('order')::TEXT = 'DESC' THEN $1 END DESC,"
		],
		"description": "Write page ordering in SQLC"
	},
	"Add pagination in SQLC": {
		"scope": "sql",
		"prefix": "pageQuery",
		"body": [
			"WITH FILTERED AS (SELECT *",
			"   FROM $1",
			"   $2",
			"   ),",
			"   COUNTED AS(",
			"      SELECT COUNT(*) AS total FROM FILTERED" ,
			"   )",
			"   SELECT f.*, c.total FROM FILTERED f, COUNTED c",
			"   ORDER BY",
			"      $3",
			"   LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');"
		],
		"description": "Write pagination in SQLC"
	},
	"Add Where like syntax in SQLC": {
		"scope": "sql",
		"prefix": "whereLike",
		"body": [
			"WHERE email LIKE COALESCE('%' || sqlc.narg('email') || '%', email)"
		],
		"description": "Write where like in SQLC"
	},
	"Get PaginationInfo": {
		"scope": "go",
		"prefix": "getPainationRequestInfo",
		"body": [
			"paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{\"$1\", \"created_at\", \"updated_at\"})"
		],
		"description": "Write get Pagination request info in go"
	},
	"Add ListPagination params": {
		"scope": "go",
		"prefix": "pageParams",
		"body": [
			"Limit:  paginationInfo.PageSize,",
			"Offset: paginationInfo.Offset,",
			"Order:  paginationInfo.OrderDirection,",
			"Sort:   paginationInfo.OrderBy,",
		],
		"description": "Write pagination params"
	},
	"Add pagination message in protos": {
		"scope": "proto3",
		"prefix": "paginationMessage",
		"body": [
			" optional types.v1.PaginationRequest pagination = ${1:1};",
		],
		"description": "Add pagination message in protos"
	},
	"Add pagination details message in protos": {
		"scope": "proto3",
		"prefix": "pageDetailsMessage",
		"body": [
  			"types.v1.PaginationDetails pagination_details = ${1:2};"
		],
		"description": "Add pagination details message in protos"
	},
	"Add pagination details response": {
		"scope": "go",
		"prefix": "paginationDetails",
		"body": [
			"PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement($1)),",
		],
		"description": "Add pagination details response"
	},
	"Add count implementation": {
		"scope": "go",
		"prefix": "countImplExtension",
		"body": [
			"func (r $1) Count() int32 {",
			"  return int32(r.Total)",
			"}",
		],
		"description": "Add count implementation"
	},
}


