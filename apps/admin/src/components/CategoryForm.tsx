import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError, OmitTypeName } from "@vtuber/services/client";
import {
  AddCategoryRequest,
  CategoryService,
  UpdateCategoryRequest,
} from "@vtuber/services/taxonomy";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

type CategoryFormProps = {
  mode: "add" | "edit";
  initialData?: {
    id?: string;
    name?: string;
    description?: string;
    image?: string;
  };
  onSuccess?: () => void;
};

export function CategoryForm({
  mode,
  initialData,
  onSuccess,
}: CategoryFormProps) {
  const router = useRouter();
  const isEditMode = mode === "edit";

  type FormValues = OmitTypeName<AddCategoryRequest | UpdateCategoryRequest>;

  const form = useForm<FormValues>({
    defaultValues: {
      id: initialData?.id,
      name: initialData?.name || "",
      description: initialData?.description || "",
      // image: initialData?.image || "",
    },
  });

  const addMutation = useMutation(CategoryService.method.addCategory, {
    onError: (err) => {
      handleConnectError(err, form);
    },
    onSuccess: () => {
      toast.success("Category created successfully");
      router.invalidate();
      if (onSuccess) {
        onSuccess();
      } else {
        router.navigate({
          to: "/categories",
        });
      }
    },
  });

  const updateMutation = useMutation(CategoryService.method.updateCategory, {
    onError: (err) => {
      handleConnectError(err, form);
    },
    onSuccess: () => {
      toast.success("Category updated successfully");
      router.invalidate();
      if (onSuccess) {
        onSuccess();
      } else {
        router.navigate({
          to: "/categories",
        });
      }
    },
  });

  const mutation = isEditMode ? updateMutation : addMutation;

  const onSubmit = form.handleSubmit((val) => {
    if (isEditMode) {
      updateMutation.mutateAsync({
        ...val,
        id: initialData?.id,
      });
    } else {
      addMutation.mutateAsync({
        ...val,
        name: val.name || undefined,
        description: val.description || undefined,
      });
    }
  });

  const { getText } = useLanguage();

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-lg border-0  backdrop-blur-sm">
        <CardHeader className="text-center space-y-2 pb-6">
          <CardTitle className="text-2xl font-bold">
            {isEditMode ? getText("EDIT") : getText("Create")}
          </CardTitle>
        </CardHeader>

        <CardContent>
          <Form {...form}>
            <form
              onSubmit={onSubmit}
              className="space-y-6">
              <div className="space-y-4">
                <TextInput
                  control={form.control}
                  name="name"
                  label={getText("Category_Name")}
                  placeholder={getText("Category_Name")}
                />

                <TextAreaInput
                  control={form.control}
                  name="description"
                  label={getText("description")}
                  placeholder={getText("description")}
                />

                {/* <FileInput
                  control={form.control}
                  name="image"
                  label={getText("Media")}
                  placeholder={getText("Media")}
                  defaultImage={initialData?.image}
                /> */}
              </div>

              <Button
                loading={mutation.isPending}
                variant={isEditMode ? "dark" : "add"}
                className="w-full h-11 font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] shadow-md hover:shadow-lg"
                disabled={mutation.isPending}>
                {isEditMode ? getText("update") : getText("Create")}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
