import { cn } from "@vtuber/ui/lib/utils";

interface Props {
  data?: any;
  className?: string;
  children?: React.ReactNode;
}

export const RouteNotFoundComponent = ({
  data,
  className,
  children,
}: Props) => {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center gap-y-1 h-96 text-3xl font-mplus font-semibold text-destructive/50",
        className,
      )}>
      {children || (
        <p>{data?.data?.message || "Oops! something went wrong"}.</p>
      )}
    </div>
  );
};
