import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation } from "@connectrpc/connect-query";
import { useNavigate, useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { GetAllUsersResponse, UserService } from "@vtuber/services/users";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Image } from "@vtuber/ui/components/image";
import { Ban, Calendar, Mail, ShieldBan, ShieldCheck } from "lucide-react";
import { toast } from "sonner";

export function UserCard({
  user,
  showBanButton,
}: {
  user: GetAllUsersResponse["data"][0];
  showBanButton: boolean;
}) {
  const navigate = useNavigate();
  const router = useRouter();
  const { session: currentUser } = useAuth();

  const mutation = useMutation(UserService.method.banUser, {
    onSuccess: () => {
      toast.success("User banned successfully");
      router.invalidate();
    },
    onError: (error) => {
      toast.error("Failed to ban user: " + error.message);
    },
  });

  const handleBan = (e: React.MouseEvent) => {
    e.stopPropagation();
    const isConfirmed = confirm("Are you sure you want to ban this user?");
    if (!isConfirmed) return;
    mutation.mutateAsync({ id: user.id });
  };

  const handleView = () => {
    navigate({ to: "/users/$id", params: { id: user.id.toString() } });
  };
  const { getText } = useLanguage();
  return (
    <div>
      <Card className="overflow-hidden transition-all hover:shadow-lg hover:border-blue-300">
        <div className="relative h-48 w-full overflow-hidden">
          <Image
            src={user.image}
            alt={user.fullName}
            className="object-cover transition-transform duration-300 hover:scale-105"
          />
          <div className="absolute top-2 right-2">
            {user.isBanned ? (
              <Badge
                variant="destructive"
                className="flex items-center gap-1">
                <ShieldBan className="h-3 w-3" /> {getText("Banned")}
              </Badge>
            ) : (
              <Badge
                variant="success"
                className="flex items-center gap-1">
                <ShieldCheck className="h-3 w-3" /> {getText("Active")}
              </Badge>
            )}
          </div>
        </div>

        <CardHeader className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg">{user.fullName}</CardTitle>
              <CardDescription className="flex items-center mt-1">
                <Mail className="h-4 w-4 mr-2 text-gray-400" />
                {user.email}
              </CardDescription>
            </div>

            <div className="flex gap-1">
              <Badge variant={user.role === "ADMIN" ? "default" : "outline"}>
                {user.role}
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-4 pt-0">
          <div className="flex flex-col gap-2 text-sm">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-gray-400" />
              <span>
                {getText("joined_on")}:
                {user.createdAt &&
                  timestampDate(user.createdAt).toLocaleDateString()}
              </span>
            </div>

            <div className="flex items-center">
              <ShieldCheck className="h-4 w-4 mr-2 text-gray-400" />
              <span>
                {getText("Email")} :
                {user.emailVerified ? (
                  <Badge
                    variant="success"
                    className="ml-1">
                    {getText("verifed")}
                  </Badge>
                ) : (
                  <Badge
                    variant="warning"
                    className="ml-1">
                    {getText("Unverifed")}
                  </Badge>
                )}
              </span>
            </div>
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0 flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={handleView}>
            {getText("view_profile")}
          </Button>

          {showBanButton && !user.isBanned && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleBan}
              disabled={currentUser?.user?.id === user.id}
              className="flex items-center gap-1">
              <Ban className="h-4 w-4" />
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
