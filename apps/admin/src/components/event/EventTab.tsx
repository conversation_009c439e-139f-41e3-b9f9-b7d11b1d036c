import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import {
  Event,
  EventParticipantService,
  GetAllEventParticipationResponse,
} from "@vtuber/services/events";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import { ColumnDef, DataTable } from "@vtuber/ui/components/data-table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@vtuber/ui/components/dialog";
import { PersonIcon } from "@vtuber/ui/components/icons/person-icon";
import { MarkDown } from "@vtuber/ui/components/markdown";
import {
  Tabs,
  Ta<PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import {
  BadgeJapaneseYen,
  Check,
  ListCheck,
  MessageSquare,
  SquareActivity,
  Users,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { RejectionDialog } from "../RejectionModal";
import { VoteDialog } from "../VoteDialogModal";

export const EventTabs = ({
  event,
  eventParticipants,
}: {
  event: Event;
  eventParticipants: any;
}) => {
  const [activeTab, setActiveTab] = useState("rules");
  const { getText } = useLanguage();

  const columns: ColumnDef<GetAllEventParticipationResponse["data"][0]>[] = [
    {
      accessorKey: "vtuber",
      header: "Vtuber",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <Avatar
              src={getCdnUrl(row.original.vtuber?.image)}
              fallback={(row.original.vtuber?.name || "").substring(0, 2)}
              className="h-8 w-8"
            />
            <span>{row.original.vtuber?.name}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        return (
          <Badge
            variant={
              row.original.status === "accepted"
                ? "success"
                : row.original.status === "rejected"
                  ? "destructive"
                  : "outline"
            }
            className="text-sm capitalize">
            {row.original.status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "voteCount",
      header: "Votes",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          {row.original.voteCount || 0}
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const [open, setOpen] = useState(false);
        const [approvalModalOpen, setApprovalModalOpen] = useState(false);
        const [voteModalOpen, setVoteModalOpen] = useState(false);
        const [reason, setReason] = useState("");
        const router = useRouter();
        const { getText } = useLanguage();
        const mutation = useMutation(
          EventParticipantService.method.changeStatus,
          {
            onError: (err) => {
              handleConnectError(err);
            },
            onSuccess: () => {
              toast.success("Event status changed successfully");
              setOpen(false);
              setApprovalModalOpen(false);
              router.invalidate();
            },
          },
        );

        const handleStatusChange = (
          status: "approved" | "rejected",
          reason?: string,
        ) => {
          mutation.mutateAsync({
            eventParticipationId: row.original.id,
            status,
            reason,
          });
        };

        const handleRejectWithReason = () => {
          if (reason.trim()) {
            handleStatusChange("rejected", reason);
          } else {
            toast.error("Please provide a reason for rejection");
          }
        };

        const handleApprove = () => {
          handleStatusChange("approved");
        };

        return (
          <>
            <div className="space-x-2">
              {row.original.status === "pending" && (
                <Button
                  variant="secondary"
                  onClick={() => setApprovalModalOpen(true)}
                  size="icon">
                  <Check />
                </Button>
              )}
              {row.original.status === "pending" && (
                <RejectionDialog
                  open={open}
                  setOpen={setOpen}
                  reason={reason}
                  setReason={setReason}
                  handleRejectWithReason={handleRejectWithReason}
                  isPending={mutation.isPending}
                />
              )}
              {row.original.status === "accepted" && (
                <VoteDialog
                  id={row.original.id}
                  open={voteModalOpen}
                  setOpen={setVoteModalOpen}
                />
              )}
              {row.original.status === "rejected" && (
                <Button
                  variant="destructive"
                  disabled={true}>
                  {getText("You_Cannot_Vote_this_Participant")}
                </Button>
              )}
            </div>

            <Dialog
              open={approvalModalOpen}
              onOpenChange={setApprovalModalOpen}>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>{getText("confirm")}</DialogTitle>
                  <DialogDescription className="p-4">
                    {getText("Are_you_sure_you_want_to_approve")}
                    {row.original.vtuber?.name}
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter className="gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setApprovalModalOpen(false)}
                    disabled={mutation.isPending}>
                    {getText("cancel")}
                  </Button>
                  <Button
                    onClick={handleApprove}
                    disabled={mutation.isPending}
                    className="min-w-[80px]">
                    {mutation.isPending ? (
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-white"></div>
                        Approving...
                      </div>
                    ) : (
                      getText("Approve")
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </>
        );
      },
    },
    {
      accessorKey: "remarks",
      header: "Remarks",
    },
  ];
  return (
    <Tabs
      value={activeTab}
      onValueChange={setActiveTab}
      className="w-full">
      <TabsList className="mb-4">
        <TabsTrigger
          value="rules"
          className="flex items-center">
          <MessageSquare className="h-4 w-4 mr-2" />
          {getText("rules")}
        </TabsTrigger>
        <TabsTrigger
          value="benefits"
          className="flex items-center">
          <BadgeJapaneseYen className="h-4 w-4 mr-2" />
          {getText("Benefits")}
        </TabsTrigger>
        <TabsTrigger
          value="participation flow"
          className="flex items-center">
          <PersonIcon className="h-4 w-4 mr-2" />
          {getText("particiaption_flow")}
        </TabsTrigger>
        <TabsTrigger
          value="overview"
          className="flex items-center">
          <SquareActivity className="h-4 w-4 mr-2" />
          {getText("event_overview")}
        </TabsTrigger>
        <TabsTrigger
          value="requirements"
          className="flex items-center">
          <ListCheck className="h-4 w-4 mr-2" />
          {getText("Requirements")}
        </TabsTrigger>
        <TabsTrigger
          value="participants"
          className="flex items-center">
          <Users className="h-4 w-4 mr-2" />
          {getText("Participants")}
        </TabsTrigger>
      </TabsList>

      <TabsContent
        value="rules"
        className="rounded-md border p-4">
        <div className="prose prose-sm max-w-none">
          <MarkDown markdown={event.rules} />
        </div>
      </TabsContent>
      <TabsContent
        value="benefits"
        className="rounded-md border p-4">
        <div className="prose prose-sm max-w-none">
          <MarkDown markdown={event.benefits} />
        </div>
      </TabsContent>
      <TabsContent
        value="participation flow"
        className="rounded-md border p-4">
        <div className="prose prose-sm max-w-none">
          <MarkDown markdown={event.participationFlow} />
        </div>
      </TabsContent>
      <TabsContent
        value="overview"
        className="rounded-md border p-4">
        <div className="prose prose-sm max-w-none">
          <MarkDown markdown={event.overview} />
        </div>
      </TabsContent>
      <TabsContent
        value="requirements"
        className="rounded-md border p-4">
        <div className="prose prose-sm max-w-none">
          <MarkDown markdown={event.requirements} />
        </div>
      </TabsContent>

      <TabsContent
        value="participants"
        className="rounded-md border p-4">
        <DataTable
          columns={columns}
          data={eventParticipants?.data || []}
          pagination={{
            currentPage: eventParticipants?.paginationDetails?.currentPage,
            totalItems: eventParticipants?.paginationDetails?.totalItems,
            totalPage: eventParticipants?.paginationDetails?.totalPages,
          }}
        />
      </TabsContent>
    </Tabs>
  );
};
