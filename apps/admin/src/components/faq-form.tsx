import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { handleConnectError } from "@vtuber/services/client";
import { Faq, FaqService } from "@vtuber/services/cms";
import { But<PERSON> } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { HtmlInput } from "@vtuber/ui/components/form-inputs/html-input";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { Spinner } from "@vtuber/ui/components/spinner";
import { ENGLISH_KEY, JAPANESE_KEY } from "@vtuber/ui/constants";
import { useGetFaqTags } from "@vtuber/ui/hooks/get-faq-tags";
import { Bookmark, Save } from "lucide-react";

import { useForm } from "react-hook-form";
import { toast } from "sonner";

export function FaqForm({ faq }: { faq?: Faq }) {
  const router = useRouter();

  const form = useForm({
    defaultValues: {
      index: faq?.index,
      question: faq?.question,
      response: faq?.response,
      language: faq?.language,
      tag: faq?.tag,
    },
  });

  const selectedLan = form.watch("language");
  const tags = useGetFaqTags(selectedLan);

  const addMutation = useMutation(FaqService.method.addFaq, {
    onError: (err) => {
      handleConnectError(err, form);
    },

    onSuccess: () => {
      toast.success("Faq created successfully");
      router.invalidate();
      router.navigate({
        to: "/faq",
      });
    },
  });

  const updateMutation = useMutation(FaqService.method.updateFaq, {
    onError: (err) => {
      handleConnectError(err, form);
    },
    onSuccess: (data) => {
      toast.success(data.message);
      router.invalidate();
      router.navigate({
        to: "/faq",
      });
    },
  });

  const onSubmit = form.handleSubmit((val) => {
    if (faq) {
      updateMutation.mutateAsync({
        ...val,
        id: faq.id,
      });
      return;
    }
    addMutation.mutateAsync(val);
  });

  const isPending = addMutation.isPending || updateMutation.isPending;

  return (
    <Card className="md:max-w-2xl w-full mx-auto">
      <CardHeader className="text-center ">
        <CardTitle className="text-xl font-semibold">
          {faq ? "Update" : "Create"} FAQ
        </CardTitle>
        <CardDescription>
          {faq
            ? "Update the details below to update your FAQ"
            : "Enter the details below to create your FAQ"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={onSubmit}
            className="space-y-4">
            <TextInput
              variant={"muted"}
              size={"lg"}
              control={form.control}
              name="index"
              label="Position"
              placeholder="position"
              type="number"
            />
            <TextInput
              variant={"muted"}
              size={"lg"}
              control={form.control}
              name="question"
              label="Question"
              placeholder="Question"
            />
            <SelectInput
              variant={"muted"}
              size={"lg"}
              label="Language"
              control={form.control}
              name="language"
              options={[
                {
                  label: "English",
                  value: ENGLISH_KEY,
                },
                {
                  label: "日本語",
                  value: JAPANESE_KEY,
                },
              ]}
            />
            <SelectInput
              variant={"muted"}
              size={"lg"}
              label="Tag"
              control={form.control}
              name="tag"
              options={tags}
            />
            <HtmlInput
              control={form.control}
              name="response"
              label="Answer"
            />
            <Button
              variant={"success"}
              size={"xl"}
              type="submit"
              disabled={isPending}
              className="w-full">
              {isPending ? (
                <Spinner />
              ) : faq ? (
                <Save className="mr-2" />
              ) : (
                <Bookmark className="mr-2" />
              )}{" "}
              {faq ? "Update" : "Create"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
