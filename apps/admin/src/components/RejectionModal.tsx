import { useLanguage } from "@vtuber/language/hooks";
import { But<PERSON> } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Textarea } from "@vtuber/ui/components/textarea";
import { X } from "lucide-react";

interface RejectionDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  reason: string;
  setReason: (reason: string) => void;
  handleRejectWithReason: () => void;
  isPending: boolean;
}

export const RejectionDialog = ({
  open,
  setOpen,
  reason,
  setReason,
  handleRejectWithReason,
  isPending,
}: RejectionDialogProps) => {
  const { getText } = useLanguage();
  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size={"icon"}>
          <X />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{getText("Reject_Participation")}</DialogTitle>
          <DialogDescription>
            {getText("Please_provide_a_reason_for_rejecting_this_participant")}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Textarea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter reason for rejection..."
            className="min-h-[100px]"
          />
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}>
            {getText("Cancel")}
          </Button>
          <Button
            variant="destructive"
            onClick={handleRejectWithReason}
            disabled={isPending}>
            {isPending ? "Rejecting..." : getText("confirm")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
