import { useLanguage } from "@vtuber/language/hooks";
import { Spinner } from "@vtuber/ui/components/spinner";
import { cn } from "@vtuber/ui/lib/utils";

export const RoutePendingComponent = ({
  className,
}: {
  className?: string;
}) => {
  const { getText } = useLanguage();
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center gap-y-1 h-[calc(100vh-200px)]",
        className,
      )}>
      <Spinner
        type="bounce"
        height={"200"}
        width={"200"}
      />
      <p className="animate-pulse text-3xl font-semibold font-mplus">
        {getText("loading")}...
      </p>
    </div>
  );
};
