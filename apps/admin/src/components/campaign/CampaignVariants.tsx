import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { GetCampaignById } from "@vtuber/services/campaigns";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Badge } from "@vtuber/ui/components/badge";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Image } from "@vtuber/ui/components/image";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Users } from "lucide-react";

export function CampaignVariants({ campaign }: { campaign: GetCampaignById }) {
  const { getText, language } = useLanguage();
  const variants = campaign.variants;

  if (!variants || variants.length === 0) {
    return (
      <div className="flex items-center justify-center p-8 text-center rounded-lg">
        <p className="text-lg">{getText("No_Variants_Available")}.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">
          {getText("Variants")}
        </h2>
        <Badge
          variant="outline"
          className="font-medium">
          {variants.length}
        </Badge>
      </div>
      <div>
        {variants.map((variant) => (
          <Link
            key={variant.id.toString()}
            to="/campaigns/variant/$variantId"
            params={{
              variantId: variant.id.toString(),
            }}
            className="block">
            <Card className="mb-4 rounded-10 border-none shadow-none bg-gradient-3 py-[39px] px-8 space-y-6 h-full transition-all duration-300 hover:shadow-lg">
              <CardHeader className="gap-y-4 p-0">
                <AspectRatio ratio={288 / 208}>
                  <Image
                    className="size-full rounded-[22px] object-cover"
                    src={variant.image}
                    alt={variant.title}
                  />
                </AspectRatio>
                <div className="text-font text-center space-y-1.5">
                  <h6 className="text-xl font-bold capitalize">
                    {variant.title}
                  </h6>
                  <CardTitle className="text-[29px] font-bold">
                    {variant.price.toLocaleString()}{" "}
                    <span className="text-lg font-medium">
                      {" "}
                      {getText("yen")}
                    </span>{" "}
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-6 p-0">
                <MarkDown
                  markdown={variant.description}
                  className="text-font"
                />
              </CardContent>
              <CardFooter className="p-0 flex-row justify-between items-center">
                <p className="text-white font-medium text-sm">
                  {getText("Max_Sub")}: {variant.maxSub}
                </p>
                <p className="text-white font-medium">
                  {getText("no_of_supporters")}{" "}
                  <span className="font-bold text-[32px] text-[#F6C172]">
                    {variant.subCount || 0}
                  </span>{" "}
                  {language === "ja" ? (
                    "人"
                  ) : (
                    <Users
                      size={18}
                      className="inline mb-2 text-font"
                    />
                  )}
                </p>
              </CardFooter>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
