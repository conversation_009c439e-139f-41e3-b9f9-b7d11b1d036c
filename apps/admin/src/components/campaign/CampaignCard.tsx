import { timestampDate } from "@bufbuild/protobuf/wkt";
import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Campaign } from "@vtuber/services/campaigns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { ExpandableText } from "@vtuber/ui/components/expandable-text";
import { Image } from "@vtuber/ui/components/image";
import { RemainingDaysBadge } from "@vtuber/ui/components/remaining-days-badge";
import { getProgress } from "@vtuber/ui/lib/utils";
import { Calendar, Eye } from "lucide-react";
import { motion } from "motion/react";

export function CampaignCard({ campaign }: { campaign: Campaign }) {
  const { getText } = useLanguage();

  const progressPercentage = getProgress(
    campaign.totalBudget,
    campaign.totalRaised,
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -10 }}
      transition={{ duration: 0.3 }}
      className="relative">
      <Card className="overflow-hidden rounded-xl group transition-all duration-300 shadow-lg hover:shadow-2xl border-0">
        <div className="relative h-52 w-full overflow-hidden">
          <Link
            to="/campaigns/$id"
            params={{ id: campaign.id.toString() }}
            className="block h-full">
            <Image
              src={campaign.thumbnail}
              alt={campaign.name}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent" />
          </Link>

          <div className="absolute top-3 right-3">
            <RemainingDaysBadge
              className="py-2"
              endDate={campaign.endDate!}
            />
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            whileHover={{ opacity: 1 }}
            className="absolute inset-0 flex items-center justify-center">
            <Link
              to="/campaigns/$id"
              params={{ id: campaign.id.toString() }}
              className="z-10">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center gap-2 px-6 py-3 font-semibold text-white bg-gradient-2 rounded-full shadow-lg">
                <Eye className="h-5 w-5" />
                {getText("View")}
              </motion.button>
            </Link>
          </motion.div>
        </div>

        <CardHeader className="pb-2 pt-4">
          <CardTitle className="text-xl font-bold capitalize">
            {campaign.name}
          </CardTitle>
          <CardDescription className="text-lg capitalize mt-1">
            <ExpandableText
              text={campaign.shortDescription}
              length={100}
            />
          </CardDescription>
        </CardHeader>

        <CardContent className="pt-0 pb-4">
          <div className="flex flex-col gap-3">
            <div className="flex flex-row items-center justify-between">
              <div className="flex flex-col gap-1">
                <p className="font-semibold">
                  {getText("TOTAL_BUDGET")}:
                  <span className="ml-2">
                    ${campaign.totalBudget.toLocaleString()}
                  </span>
                </p>
                <p className="font-semibold ">
                  {getText("Raised")}:
                  <span className="ml-2">
                    ${campaign.totalRaised.toLocaleString()}
                  </span>
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <div className="text-sm">
                  {campaign.endDate
                    ? timestampDate(campaign.endDate).toLocaleDateString()
                    : "N/A"}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{getText("progress")}</span>
                <span className="font-medium">
                  {progressPercentage.toFixed(1)}%
                </span>
              </div>
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${progressPercentage}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="h-2 rounded-full bg-primary"
              />
            </div>

            <div className="flex items-center gap-2 mt-2">
              <p className="text-sm ">
                {getText("created_by")} :
                <span className="ml-2 font-medium">{campaign.createdBy}</span>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
