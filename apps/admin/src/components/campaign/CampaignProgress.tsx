import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useLanguage } from "@vtuber/language/hooks";
import { GetCampaignById } from "@vtuber/services/campaigns";
import { Badge } from "@vtuber/ui/components/badge";
import { Progress } from "@vtuber/ui/components/progress";
import { Target, TrendingUp } from "lucide-react";

export function CampaignProgress({ campaign }: { campaign: GetCampaignById }) {
  const { getText } = useLanguage();

  const fundingPercentage = Number(
    ((campaign.totalRaised / campaign.totalBudget) * 100).toFixed(2),
  );

  const timeRemaining = campaign.endDate
    ? Math.ceil(
        (timestampDate(campaign.endDate).getTime() - Date.now()) /
          (1000 * 60 * 60 * 24),
      )
    : 0;

  return (
    <div className="border border-gray-800 bg-gradient-3 rounded-lg p-6 shadow-sm space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="font-semibold text-lg flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          {getText("Campaign_Progress")}
        </h3>
      </div>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="font-medium flex items-center gap-2">
            <Target className="h-4 w-4" />
            {getText("Funding_Progress")}
          </span>
          <span className="font-bold text-lg">{fundingPercentage}%</span>
        </div>

        <Progress
          value={Math.min(fundingPercentage, 100)}
          className="h-3 bg-gray-200"
        />

        <div className="flex justify-between text-sm text-muted-foreground">
          <span className="font-medium">
            {getText("Raised")}:{" "}
            <span className="text-foreground font-semibold">
              ¥{campaign.totalRaised.toLocaleString()}
            </span>
          </span>
          <span className="font-medium">
            {getText("Goal")}:{" "}
            <span className="text-foreground font-semibold">
              ¥{campaign.totalBudget.toLocaleString()}
            </span>
          </span>
        </div>
        <Badge variant={"info"}>{timeRemaining} days</Badge>
      </div>
    </div>
  );
}
