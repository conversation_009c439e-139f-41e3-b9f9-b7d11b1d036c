import { useMutation, useQuery } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError, OmitTypeName } from "@vtuber/services/client";
import {
  EventVoteService,
  GivePlatformPointRequest,
} from "@vtuber/services/events";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Form } from "@vtuber/ui/components/form";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

interface VoteDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  id: string;
}

export const VoteDialog = ({ open, setOpen, id }: VoteDialogProps) => {
  const router = useRouter();
  const { getText } = useLanguage();
  const { data: getPlatfromPoint } = useQuery(
    EventVoteService.method.getPlatfromPointOfEventParticipation,
    {
      eventParticipationId: id,
    },
    {
      enabled: open,
    },
  );
  const form = useForm<OmitTypeName<GivePlatformPointRequest>>({
    defaultValues: {
      eventParticipationId: id,
      points: getPlatfromPoint?.points,
      remarks: getPlatfromPoint?.remarks,
    },
  });

  useEffect(() => {
    if (getPlatfromPoint) {
      form.setValue("points", getPlatfromPoint?.points);
      form.setValue("remarks", getPlatfromPoint?.remarks);
    }
  }, [getPlatfromPoint]);

  const givePlatformPoint = useMutation(
    EventVoteService.method.givePlatformPoint,
    {
      onError: (err) => {
        handleConnectError(err, form);
      },
      onSuccess: (data, variables) => {
        toast.success(data.message);
        router.invalidate();
        if (variables) {
          form.setValue("points", variables.points ?? 0n);
          form.setValue("remarks", variables.remarks ?? "");
        }
        setOpen(false);
      },
    },
  );

  const handleGivePlatformPoint = form.handleSubmit((val) => {
    givePlatformPoint.mutateAsync({
      ...val,
    });
  });

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">{getText("Give_Point")}</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{getText("Give_Platform_Points")}</DialogTitle>
          <DialogDescription>
            {getText(
              "Enter_your_remarks_and_points_to_vote_for_this_participation",
            )}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={handleGivePlatformPoint}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {getText("Remarks")}
                </label>
                <TextAreaInput
                  name="remarks"
                  control={form.control}
                  placeholder="Enter your remarks..."
                  className="min-h-[100px]"
                />
              </div>
              <div className="space-y-2">
                <label
                  htmlFor="points"
                  className="text-sm font-medium">
                  {getText("points")}
                </label>
                <TextInput
                  name="points"
                  control={form.control}
                  placeholder="Enter points..."
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}>
                {getText("Cancel")}
              </Button>
              <Button
                type={"submit"}
                disabled={givePlatformPoint.isPending}>
                {givePlatformPoint.isPending
                  ? "Voting..."
                  : getText("Submit_Vote")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
