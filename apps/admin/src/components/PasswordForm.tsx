import { useMutation } from "@connectrpc/connect-query";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService, ChangePasswordRequest } from "@vtuber/services/users";
import { <PERSON><PERSON> } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { Spinner } from "@vtuber/ui/components/spinner";
import { toast } from "@vtuber/ui/components/toaster";
import { Bookmark, LockIcon } from "lucide-react";
import { useForm } from "react-hook-form";

export const PasswordForm = () => {
  const { getText } = useLanguage();
  const form = useForm<ChangePasswordRequest & { confirmPassword: string }>({
    defaultValues: {
      newPassword: "",
      oldPassword: "",
      confirmPassword: "",
    },
  });

  const mutation = useMutation(AuthService.method.changePassword, {
    onSuccess: (data) => {
      toast.success(data.message);
      form.reset();
    },
    onError: (err) => {
      toast.error(err.message);
      handleConnectError(err, form);
    },
  });

  const onSubmit = form.handleSubmit(({ confirmPassword, ...data }) => {
    if (confirmPassword !== data.newPassword) {
      form.setError("confirmPassword", {
        type: "server",
        message: "Password does not match",
      });
      return;
    }
    mutation.mutate(data);
  });

  return (
    <Card className="rounded-xl">
      <CardHeader>
        <CardTitle className="text-2xl font-semibold text-font flex items-center gap-2">
          <LockIcon />
          {getText("change_password")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            className="flex flex-col gap-y-6"
            onSubmit={onSubmit}>
            <TextInput
              id="password"
              control={form.control}
              name="oldPassword"
              type="password"
              placeholder="＊＊＊＊＊＊＊＊＊＊"
              size={"lg"}
              variant={"muted"}
              label={getText("current_password")}
            />
            <section className="grid gap-y-6">
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-3"
                name="newPassword"
                labelClassName="font-medium text-base"
                type="password"
                placeholder="＊＊＊＊＊＊＊＊＊＊"
                size={"lg"}
                variant={"muted"}
                label={getText("new_password")}
              />
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-3"
                name="confirmPassword"
                type="password"
                variant={"muted"}
                label={getText("confirm_password")}
                labelClassName="font-medium text-base"
                placeholder="＊＊＊＊＊＊＊＊＊＊"
                size={"lg"}
              />
              <p className="text-font text-xs">
                {getText("confirm_password_desccription")}
              </p>
              <Button
                type="submit"
                variant={"success"}
                size={"xl"}
                className="w-full capitalize">
                {mutation.isPending ? (
                  <Spinner className="mr-2" />
                ) : (
                  <Bookmark className="mr-2" />
                )}
                {getText("update_password")}
              </Button>
            </section>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
