import { Timestamp } from "@bufbuild/protobuf/wkt";
import { Link } from "@tanstack/react-router";
import { Post } from "@vtuber/services/content";
import { Badge } from "@vtuber/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Media } from "@vtuber/ui/components/media";
import { Eye, Heart, MessageCircle } from "lucide-react";

export const UserPosts = ({ post }: { post: Post }) => {
  return (
    <Link
      to="/posts/$id"
      params={{ id: post.id.toString() }}
      className="block group">
      <Card className="overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
        <div className="relative aspect-video overflow-hidden">
          <Media
            src={post.media}
            alt={post.title}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          />

          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="flex items-center gap-2 px-6 py-3 font-semibold text-white bg-gradient-2 rounded-full shadow-lg">
              <Eye className="w-6 h-6" />
              {"View"}
            </div>
          </div>

          <div className="absolute bottom-3 right-3">
            {post.membershipOnly && (
              <Badge className="bg-gradient-to-r from-amber-500 to-orange-500">
                Members Only
              </Badge>
            )}
          </div>
        </div>

        <CardContent className="p-5">
          <CardTitle className="font-bold text-lg line-clamp-1 mb-2">
            {post.title}
          </CardTitle>
          <CardDescription className="text-muted-foreground text-sm line-clamp-2 mb-4">
            {post.shortDescription}
          </CardDescription>
          <div className="flex justify-between items-center text-sm">
            <div className="flex space-x-4">
              <div className="flex items-center text-foreground/80">
                <Heart className="w-4 h-4 mr-1" />
                <span>{post.postLikes}</span>
              </div>
              <div className="flex items-center text-foreground/80">
                <MessageCircle className="w-4 h-4 mr-1" />
                <span>{post.postComments}</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              {post.createdAt ? formatDate(post.createdAt) : ""}
            </p>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

function formatDate(timestamp: Timestamp): string {
  const date = new Date(Number(timestamp.seconds) * 1000);
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  });
}
