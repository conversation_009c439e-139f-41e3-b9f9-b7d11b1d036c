import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { handleConnectError } from "@vtuber/services/client";
import { Event, EventService } from "@vtuber/services/events";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@vtuber/ui/components/dialog";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Separator } from "@vtuber/ui/components/separator";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { CalendarDays, Info, MessageSquare } from "lucide-react";
import { toast } from "sonner";

export function EventDetailsDialog({
  event,
  open,
  onOpenChange,
}: {
  event: Event;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const router = useRouter();
  const approveMutation = useMutation(
    EventService.method.approveOrRejectEvent,
    {
      onError: (err) => {
        handleConnectError(err);
      },
      onSuccess: () => {
        toast.success("Event status changed successfully");
        router.invalidate();
      },
    },
  );

  const handleApprove = (id: string) => {
    const confirm = window.confirm(
      "Are you sure you want to approve this event?",
    );
    if (confirm) {
      approveMutation.mutateAsync({
        id,
        status: "approved",
      });
    }
  };

  const handleReject = (id: string) => {
    const confirm = window.confirm(
      "Are you sure you want to reject this event?",
    );
    if (confirm) {
      approveMutation.mutateAsync({
        id,
        status: "rejected",
      });
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">
            Event Details
          </DialogTitle>
          <DialogDescription className="text-md">
            {event.title}
          </DialogDescription>
        </DialogHeader>

        <div className="overflow-y-auto flex-1 py-4 space-y-6">
          {event.image && (
            <div className="flex justify-center">
              <div className="rounded-lg overflow-hidden w-full max-h-96 shadow-lg">
                <img
                  src={event.image}
                  alt={event.title}
                  className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold">{event.title}</h3>
            <Badge
              variant={
                event.status === "approved"
                  ? "success"
                  : event.status === "rejected"
                    ? "destructive"
                    : "outline"
              }
              className="text-sm capitalize">
              {event.status}
            </Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start gap-3">
              <CalendarDays className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-1" />
              <div>
                <p className="font-medium text-sm text-muted-foreground">
                  Event Period
                </p>
                <p className="text-sm font-medium">
                  {event.startDate &&
                    timestampDate(event?.startDate).toLocaleDateString()}
                </p>
                <p className="text-sm text-muted-foreground">to</p>
                <p className="text-sm font-medium">
                  {event.endDate &&
                    timestampDate(event.endDate).toLocaleDateString()}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div>
                <p className="font-medium text-sm text-muted-foreground">
                  Created by
                </p>
                {event.user ? (
                  <div className="flex items-center gap-2 mt-1">
                    <Avatar
                      src={getCdnUrl(event.user.image)}
                      fallback={event.user.name.substring(0, 2)}
                      className="h-6 w-6"
                    />
                    <span className="text-sm font-medium">
                      {event.user.name}
                    </span>
                  </div>
                ) : event.vtuber ? (
                  <div className="flex items-center gap-2 mt-1">
                    <Avatar
                      src={getCdnUrl(event.vtuber.image)}
                      fallback={event.vtuber.name.substring(0, 2)}
                      className="h-6 w-6"
                    />
                    <span className="text-sm font-medium">
                      {event.vtuber.name}
                    </span>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">Unknown</p>
                )}
              </div>
            </div>
          </div>
          <Badge
            className="capitalize"
            variant={event.hasParticipated ? "default" : "outline"}>
            {event.hasParticipated ? "Participating" : "Not participating"}
          </Badge>

          <Separator className="my-4" />

          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-3">
              <Info className="h-5 w-5 text-muted-foreground" />
              <h3 className="font-medium text-lg">Description</h3>
            </div>
            <MarkDown markdown={event.description} />

            <div className="flex items-center gap-2 mb-3">
              <MessageSquare className="h-5 w-5 text-muted-foreground" />
              <h3 className="font-medium text-lg">Rules</h3>
            </div>
            <MarkDown markdown={event.rules} />
          </div>
        </div>

        <DialogFooter className="gap-2 sm:gap-0 mt-4">
          <div className="flex items-center justify-between w-full">
            {event.status === "pending" && (
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={() => {
                    handleApprove(event.id);
                    onOpenChange(false);
                  }}>
                  Approve
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => {
                    handleReject(event.id);
                    onOpenChange(false);
                  }}>
                  Reject
                </Button>
              </div>
            )}

            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="w-full sm:w-auto">
              Close
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
