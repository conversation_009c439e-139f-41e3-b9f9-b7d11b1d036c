import { useRouter } from "@tanstack/react-router";
import { VtuberAccessRequest } from "@vtuber/services/vtubers";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@vtuber/ui/components/dialog";

export function CreatorRequestDialog({
  vtuber,
  open,
  onOpenChange,
}: {
  vtuber: VtuberAccessRequest;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const router = useRouter();

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto rounded-2xl shadow-2xl flex flex-col p-6">
        <div className="space-y-6">
          <DialogHeader>
            <DialogTitle className="text-3xl font-bold text-primary flex items-center justify-between">
              <span>{vtuber.name}</span>
            </DialogTitle>

            <DialogDescription className="text-lg mt-2">
              {vtuber.description}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-auto flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="px-6 py-2 rounded-full">
              Close
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default CreatorRequestDialog;
