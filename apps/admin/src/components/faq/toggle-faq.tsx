import { useMutation } from "@connectrpc/connect-query";
import { FaqService } from "@vtuber/services/cms";
import { Switch } from "@vtuber/ui/components/switch";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export const ToggleFaq = ({ id, active }: { id: string; active: boolean }) => {
  const [isActive, setIsActive] = useState(active);

  useEffect(() => {
    setIsActive(active);
  }, [active]);

  const { mutate, isPending } = useMutation(FaqService.method.toogleFaqStatus, {
    onError: () => {
      setIsActive(!isActive);
    },
    onSuccess: (data) => {
      toast.success(data.message);
    },
  });

  return (
    <Switch
      disabled={isPending}
      checked={isActive}
      className="data-[state=checked]:bg-green-500"
      onCheckedChange={(e) => {
        setIsActive(e);
        mutate({
          id,
        });
      }}
    />
  );
};
