import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { FaqService } from "@vtuber/services/cms";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Trash } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export const DeleteFaq = ({ id }: { id: string }) => {
  const router = useRouter();
  const { getText } = useLanguage();
  const [opened, setOpened] = useState(false);
  const mutation = useMutation(FaqService.method.deleteFaq, {
    onSuccess: () => {
      toast.success("FAQ deleted successfully");
      router.invalidate();
      setOpened(false);
    },
  });

  const onDelete = () => {
    mutation.mutateAsync({
      id,
    });
  };
  return (
    <Dialog
      open={opened}
      onOpenChange={setOpened}>
      <DialogTrigger asChild>
        <Button
          variant={"muted-destructive"}
          size={"icon"}>
          <Trash />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete this FAQ?</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete this FAQ.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose>{getText("cancel")}</DialogClose>
          <Button
            onClick={onDelete}
            variant={"destructive"}>
            {getText("confirm")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
