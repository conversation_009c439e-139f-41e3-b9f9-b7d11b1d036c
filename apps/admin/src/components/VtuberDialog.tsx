import { useQuery } from "@connectrpc/connect-query";
import { VtuberProfilesService } from "@vtuber/services/vtubers";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { InstagramIcon } from "@vtuber/ui/components/icons/instagram-icon";
import { TikTokIcon } from "@vtuber/ui/components/icons/tittok-icon";
import { XIcon } from "@vtuber/ui/components/icons/x-icon";
import { YoutubeIcon } from "@vtuber/ui/components/icons/youtube-icon";
import { Image } from "@vtuber/ui/components/image";
import { MarkDown } from "@vtuber/ui/components/markdown";

interface VtuberProfileDialogProps {
  id: string;
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const VtuberProfileDialog = ({
  id,
  open,
  setOpen,
}: VtuberProfileDialogProps) => {
  const { data } = useQuery(VtuberProfilesService.method.getVtuberProfileById, {
    id: id.toString(),
  });

  const vtuber = data?.data;

  const socialLinks = [
    {
      type: "instagram",
      url: vtuber?.socialMediaLinks?.instagram || "",
      icon: <InstagramIcon className="w-6 h-6" />,
      label: "Instagram Profile",
    },
    {
      type: "tiktok",
      url: vtuber?.socialMediaLinks?.tiktok || "",
      icon: <TikTokIcon className="w-6 h-6" />,
      label: "TikTok Profile",
    },
    {
      type: "twitter",
      url: vtuber?.socialMediaLinks?.twitter || "",
      icon: <XIcon className="w-6 h-6" />,
      label: "X/Twitter Profile",
    },
    {
      type: "youtube",
      url: vtuber?.socialMediaLinks?.youtube || "",
      icon: <YoutubeIcon className="w-6 h-6" />,
      label: "YouTube Channel",
    },
  ];

  const handleSocialClick = (url: string) => {
    if (url) {
      window.open(url, "_blank", "noopener,noreferrer");
    }
  };
  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="px-4 py-2  rounded-md hover:bg-gray-50 transition-colors">
          View Vtuber Profile
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Vtuber Profile</DialogTitle>
        </DialogHeader>
        <div className="relative w-full h-40 mb-16">
          {vtuber?.bannerImage ? (
            <AspectRatio ratio={200 / 150}>
              <Image
                src={vtuber.bannerImage}
                alt="Profile Banner"
                className="w-full h-40 object-cover rounded-md"
              />
            </AspectRatio>
          ) : (
            <div className="w-full h-40 bg-gray-200 rounded-md" />
          )}
          <div className="absolute -bottom-12 left-6 border-4 border-white rounded-full shadow-md">
            <Avatar
              src={vtuber?.image}
              alt={vtuber?.displayName}
              className="w-24 h-24 rounded-full object-cover"
            />
          </div>
        </div>
        <div className="px-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold mb-2">{vtuber?.displayName}</h2>
              {vtuber?.furigana && <p className="text-sm">{vtuber.furigana}</p>}
            </div>
          </div>
          <div className="flex flex-wrap mt-3 gap-3">
            {socialLinks
              .filter((link) => link.url)
              .map((social) => (
                <Button
                  variant={"blue-outline"}
                  key={social.type}
                  onClick={() => handleSocialClick(social.url)}
                  className="flex items-center justify-center w-12 h-12  rounded-full shadow-sm hover:shadow-md transition-all transform hover:scale-105"
                  aria-label={social.label}>
                  {social.icon}
                </Button>
              ))}
          </div>

          {vtuber?.description && (
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-2">About</h3>
              <MarkDown markdown={vtuber.description} />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
