import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation } from "@connectrpc/connect-query";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService, GetSessionResponse } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { DOBInput } from "@vtuber/ui/components/form-inputs/date-of-birth-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { Spinner } from "@vtuber/ui/components/spinner";
import { toast } from "@vtuber/ui/components/toaster";
import { checkDob, convertToTimestamp } from "@vtuber/ui/lib/utils";
import { Bookmark, UserCircle } from "lucide-react";
import { useForm } from "react-hook-form";

export const ProfileForm = () => {
  const { session, setSesssion } = useAuth();
  const user = session?.user;
  const { getText } = useLanguage();

  const form = useForm({
    defaultValues: {
      fullName: user?.fullName ?? "",
      dateOfBirth: user?.dob ? timestampDate(user?.dob).toISOString() : "",
    },
  });

  const mutation = useMutation(AuthService.method.updateUserDetails, {
    onSuccess: (data, { fullName, dateOfBirth }) => {
      toast.success(data.message);
      setSesssion(
        (prev) =>
          ({
            ...prev,
            user: {
              ...prev?.user,
              fullName,
              dateOfBirth,
            },
          }) as GetSessionResponse,
      );
    },
    onError: (err) => {
      handleConnectError(err, form);
    },
  });

  const onUpdate = form.handleSubmit((data) => {
    const inValidDob = checkDob(data.dateOfBirth);

    if (inValidDob) {
      form.setError("dateOfBirth", {
        message: getText("invalid_dob"),
      });
      return;
    }

    mutation.mutate({
      dateOfBirth: convertToTimestamp(data.dateOfBirth),
      fullName: data.fullName,
    });
  });

  return (
    <Card className="rounded-xl">
      <CardHeader>
        <CardTitle className="text-2xl font-semibold capitalize flex items-center gap-2 text-font">
          <UserCircle /> {getText("my_profile")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            className="space-y-8"
            onSubmit={onUpdate}>
            <div className="grid gap-y-3">
              <TextInput
                id="fullName"
                control={form.control}
                name="fullName"
                size={"lg"}
                placeholder="vsai"
                label={getText("Name")}
                variant={"muted"}
              />
            </div>
            <DOBInput
              control={form.control}
              variant={"muted"}
              name="dateOfBirth"
              wrapperClassName="gap-y-3"
              size={"lg"}
              label={getText("date_of_birth")}
            />
            <Button
              disabled={mutation.isPending}
              variant={"success"}
              size={"xl"}
              type="submit"
              className="w-full capitalize">
              {mutation.isPending ? (
                <Spinner className="mr-2" />
              ) : (
                <Bookmark className="mr-2" />
              )}{" "}
              {getText("udpate_profile")}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
