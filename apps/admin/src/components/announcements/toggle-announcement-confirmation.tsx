import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { Announcement, AnnouncementsService } from "@vtuber/services/cms";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@vtuber/ui/components/alert-dialog";
import { Spinner } from "@vtuber/ui/components/spinner";
import { cn } from "@vtuber/ui/lib/utils";
import { CheckCircle, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export const ToggleAnnouncementConfirmation = ({
  data,
}: {
  data: Announcement;
}) => {
  const router = useRouter();
  const [opened, setOpened] = useState(false);

  const { mutate, isPending } = useMutation(
    AnnouncementsService.method.toggleAnnouncement,
    {
      onError: (err) => {
        toast.error(err.rawMessage);
      },
      onSuccess: () => {
        setOpened(false);
        toast.success(
          `Announcement ${data?.active ? "deactivated" : "activated"} successfully`,
        );
        router.invalidate();
      },
    },
  );

  if (!data.active)
    return (
      <button
        onClick={() => {
          mutate({
            id: data.id,
          });
        }}
        className={cn(
          "rounded-full flex items-center gap-x-3 py-2 px-4",
          data?.active ? "bg-green-500" : "bg-destructive",
        )}>
        {isPending ? (
          <Spinner className="size-5" />
        ) : data?.active ? (
          <CheckCircle />
        ) : (
          <X className="text-red-200" />
        )}{" "}
        <p className={data?.active ? "text-white" : "text-red-200"}>
          {data?.active ? "Active" : "Inactive"}
        </p>
      </button>
    );

  return (
    <AlertDialog
      open={opened}
      onOpenChange={setOpened}>
      <AlertDialogTrigger asChild>
        <button
          className={cn(
            "rounded-full flex items-center gap-x-3 py-2 px-4",
            data?.active ? "bg-green-500" : "bg-destructive",
          )}>
          {data?.active ? <CheckCircle /> : <X className="text-red-200" />}{" "}
          <p className={data?.active ? "text-white" : "text-red-200"}>
            {data?.active ? "Active" : "Inactive"}
          </p>
        </button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Are you sure you want to make this announcement{" "}
            {data?.active ? "inactive" : "active"}?
          </AlertDialogTitle>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              mutate({
                id: data.id,
              });
            }}
            disabled={isPending}>
            {isPending ? "Toggling..." : "Confirm"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
