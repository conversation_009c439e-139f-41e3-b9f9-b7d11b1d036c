import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { handleConnectError } from "@vtuber/services/client";
import { Announcement, AnnouncementsService } from "@vtuber/services/cms";
import { Button } from "@vtuber/ui/components/button";
import {
  <PERSON>alog,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Form } from "@vtuber/ui/components/form";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { Edit, Plus } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export const AnnouncementForm = ({ data }: { data?: Announcement }) => {
  const router = useRouter();
  const [opened, setOpened] = useState(false);
  const form = useForm({
    defaultValues: {
      image: data?.image,
      description: data?.content,
    },
  });

  const addAnnouncement = useMutation(
    AnnouncementsService.method.addAnnouncement,
    {
      onError: (err) => {
        handleConnectError(err, form);
      },
      onSuccess: () => {
        setOpened(false);
        router.invalidate();
        toast.success("Announcement created successfully");
      },
    },
  );

  const updateAnnouncement = useMutation(
    AnnouncementsService.method.updateAnnouncement,
    {
      onError: (err) => {
        handleConnectError(err, form);
      },
      onSuccess: () => {
        router.invalidate();
        setOpened(false);
        toast.success("Announcement updated successfully");
      },
    },
  );

  const onSubmit = form.handleSubmit(async (val) => {
    if (data) {
      await updateAnnouncement.mutateAsync({
        image: val.image,
        content: val.description,
        id: data.id,
      });
      return;
    }
    await addAnnouncement.mutateAsync(val);
  });

  return (
    <Dialog
      open={opened}
      onOpenChange={setOpened}>
      <DialogTrigger asChild>
        <Button
          variant={"sub-outline"}
          className="rounded-full"
          size={"xl"}>
          {!data ? <Plus className="mr-2" /> : <Edit className="mr-2" />}{" "}
          {!data ? "Add" : "Edit"}
        </Button>
      </DialogTrigger>
      <Form {...form}>
        <form>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Announcement</DialogTitle>
            </DialogHeader>
            <div className="space-y-6">
              <FileInput
                control={form.control}
                name="image"
                label="Announcement Image"
                required
                defaultImage={data?.image}
                fileUploadDescription="Recommended size: 1280x720px"
              />
              <TextAreaInput
                control={form.control}
                name="description"
                label="Description"
                required
              />
            </div>
            <DialogFooter className="gap-x-6">
              <DialogClose>Cancel</DialogClose>
              <Button
                onClick={onSubmit}
                loading={
                  addAnnouncement.isPending || updateAnnouncement.isPending
                }
                type="button">
                {data ? "Update" : "Add"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
      </Form>
    </Dialog>
  );
};
