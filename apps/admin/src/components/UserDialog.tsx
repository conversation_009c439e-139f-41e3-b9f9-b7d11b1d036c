import { useRouter } from "@tanstack/react-router";
import { User } from "@vtuber/services/users";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@vtuber/ui/components/dialog";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { Ban, CheckCircle, Shield } from "lucide-react";

export function UserDialog({
  user,
  open,
  onOpenChange,
}: {
  user: User;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const router = useRouter();

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg p-6 flex flex-col items-center gap-4 rounded-2xl">
        <DialogHeader className="w-full flex flex-col items-center gap-2">
          <Avatar
            src={getCdnUrl(user.image)}
            alt={user.fullName}
            className="w-24 h-24 border-4 border-gray-200 shadow-md rounded-full"
          />
          <DialogTitle className="text-2xl font-semibold text-gray-900">
            {user.fullName}
          </DialogTitle>
          <p className="text-gray-600 text-sm">{user.email}</p>
          <Badge
            variant="success"
            className="text-sm capitalize mt-2">
            {user.role}
          </Badge>
        </DialogHeader>

        <DialogDescription className="w-full flex flex-wrap justify-center gap-2">
          {user.emailVerified && (
            <StatusBadge
              icon={CheckCircle}
              label="Email Verified"
              color="green"
            />
          )}
          {user.isVtuber && (
            <StatusBadge
              icon={Shield}
              label="Creator"
              color="blue"
            />
          )}
          {user.isBanned && (
            <StatusBadge
              icon={Ban}
              label="Banned"
              color="red"
            />
          )}
        </DialogDescription>

        <DialogFooter className="w-full flex justify-end mt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

const StatusBadge = ({
  icon: Icon,
  label,
  color,
}: {
  icon: any;
  label: string;
  color: string;
}) => (
  <div
    className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium bg-${color}-100 text-${color}-700`}>
    <Icon size={16} />
    {label}
  </div>
);
