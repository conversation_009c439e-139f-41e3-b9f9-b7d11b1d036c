import { QueryClient } from "@tanstack/react-query";
import { createRouter as createTanStackRouter } from "@tanstack/react-router";
import { routerWithQueryClient } from "@tanstack/react-router-with-query";
import { DefaultCatchBoundary } from "@vtuber/ui/components/default-catch-boundry";
import { RouteFallback } from "@vtuber/ui/components/route-fallback";
import { routeTree } from "./routeTree.gen";

export function createRouter() {
  const queryClient = new QueryClient();
  const router = routerWithQueryClient(
    createTanStackRouter({
      routeTree,
      context: { queryClient },
      defaultPreload: "intent",
      defaultErrorComponent: DefaultCatchBoundary,
      defaultNotFoundComponent: ({ data }) => (
        <div className="flex items-center justify-center min-h-screen">
          <RouteFallback data={data} />
        </div>
      ),
      scrollRestoration: true,
      defaultViewTransition: true,
      defaultPendingMinMs: 0,
      defaultPendingMs: 0,
    }),
    queryClient,
  );

  return router;
}

declare module "@tanstack/react-router" {
  interface Register {
    router: ReturnType<typeof createRouter>;
  }
}
