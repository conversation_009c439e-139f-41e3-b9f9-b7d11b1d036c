/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AuthRouteImport } from './routes/_auth'
import { Route as AppRouteImport } from './routes/_app'
import { Route as AppIndexRouteImport } from './routes/_app/index'
import { Route as AuthResetPasswordRouteImport } from './routes/_auth/reset-password'
import { Route as AuthLoginRouteImport } from './routes/_auth/login'
import { Route as AuthForgotPasswordRouteImport } from './routes/_auth/forgot-password'
import { Route as AppUserGuidesRouteImport } from './routes/_app/user-guides'
import { Route as AppUpdateGuidesRouteImport } from './routes/_app/update-guides'
import { Route as AppTransactionActRouteImport } from './routes/_app/transaction-act'
import { Route as AppTermsConditionRouteImport } from './routes/_app/terms-condition'
import { Route as AppSettingsRouteImport } from './routes/_app/settings'
import { Route as AppPrivacyPolicyRouteImport } from './routes/_app/privacy-policy'
import { Route as AppOperatingCompanyRouteImport } from './routes/_app/operating-company'
import { Route as AppCrowdfundingGuidelinesRouteImport } from './routes/_app/crowdfunding-guidelines'
import { Route as AppCreatorRequestsRouteImport } from './routes/_app/creator-requests'
import { Route as AppCommunityGuidelinesRouteImport } from './routes/_app/community-guidelines'
import { Route as AppAccountRouteImport } from './routes/_app/account'
import { Route as AppVtuberCategoriesIndexRouteImport } from './routes/_app/vtuber-categories/index'
import { Route as AppSocialLinksIndexRouteImport } from './routes/_app/social-links/index'
import { Route as AppResourceIndexRouteImport } from './routes/_app/resource/index'
import { Route as AppFaqIndexRouteImport } from './routes/_app/faq/index'
import { Route as AppEventsIndexRouteImport } from './routes/_app/events/index'
import { Route as AppCategoriesIndexRouteImport } from './routes/_app/categories/index'
import { Route as AppCampaignsIndexRouteImport } from './routes/_app/campaigns/index'
import { Route as AppAnnouncementsIndexRouteImport } from './routes/_app/announcements/index'
import { Route as AppVtuberCategoriesAddRouteImport } from './routes/_app/vtuber-categories/add'
import { Route as AppVtuberCategoriesEditRouteImport } from './routes/_app/vtuber-categories/$edit'
import { Route as AppPostsIdRouteImport } from './routes/_app/posts/$id'
import { Route as AppFaqAddRouteImport } from './routes/_app/faq/add'
import { Route as AppFaqIdRouteImport } from './routes/_app/faq/$id'
import { Route as AppEventsAddRouteImport } from './routes/_app/events/add'
import { Route as AppCategoriesAddRouteImport } from './routes/_app/categories/add'
import { Route as AppCategoriesIdRouteImport } from './routes/_app/categories/$id'
import { Route as AppCampaignsAddRouteImport } from './routes/_app/campaigns/add'
import { Route as AppUsersIdIndexRouteImport } from './routes/_app/users/$id/index'
import { Route as AppEventsIdIndexRouteImport } from './routes/_app/events/$id/index'
import { Route as AppCampaignsIdIndexRouteImport } from './routes/_app/campaigns/$id/index'
import { Route as AppEventsIdEditRouteImport } from './routes/_app/events/$id/edit'
import { Route as AppCampaignsVariantVariantIdRouteImport } from './routes/_app/campaigns/variant/$variantId'

const AuthRoute = AuthRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any)
const AppRoute = AppRouteImport.update({
  id: '/_app',
  getParentRoute: () => rootRouteImport,
} as any)
const AppIndexRoute = AppIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AppRoute,
} as any)
const AuthResetPasswordRoute = AuthResetPasswordRouteImport.update({
  id: '/reset-password',
  path: '/reset-password',
  getParentRoute: () => AuthRoute,
} as any)
const AuthLoginRoute = AuthLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRoute,
} as any)
const AuthForgotPasswordRoute = AuthForgotPasswordRouteImport.update({
  id: '/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => AuthRoute,
} as any)
const AppUserGuidesRoute = AppUserGuidesRouteImport.update({
  id: '/user-guides',
  path: '/user-guides',
  getParentRoute: () => AppRoute,
} as any)
const AppUpdateGuidesRoute = AppUpdateGuidesRouteImport.update({
  id: '/update-guides',
  path: '/update-guides',
  getParentRoute: () => AppRoute,
} as any)
const AppTransactionActRoute = AppTransactionActRouteImport.update({
  id: '/transaction-act',
  path: '/transaction-act',
  getParentRoute: () => AppRoute,
} as any)
const AppTermsConditionRoute = AppTermsConditionRouteImport.update({
  id: '/terms-condition',
  path: '/terms-condition',
  getParentRoute: () => AppRoute,
} as any)
const AppSettingsRoute = AppSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => AppRoute,
} as any)
const AppPrivacyPolicyRoute = AppPrivacyPolicyRouteImport.update({
  id: '/privacy-policy',
  path: '/privacy-policy',
  getParentRoute: () => AppRoute,
} as any)
const AppOperatingCompanyRoute = AppOperatingCompanyRouteImport.update({
  id: '/operating-company',
  path: '/operating-company',
  getParentRoute: () => AppRoute,
} as any)
const AppCrowdfundingGuidelinesRoute =
  AppCrowdfundingGuidelinesRouteImport.update({
    id: '/crowdfunding-guidelines',
    path: '/crowdfunding-guidelines',
    getParentRoute: () => AppRoute,
  } as any)
const AppCreatorRequestsRoute = AppCreatorRequestsRouteImport.update({
  id: '/creator-requests',
  path: '/creator-requests',
  getParentRoute: () => AppRoute,
} as any)
const AppCommunityGuidelinesRoute = AppCommunityGuidelinesRouteImport.update({
  id: '/community-guidelines',
  path: '/community-guidelines',
  getParentRoute: () => AppRoute,
} as any)
const AppAccountRoute = AppAccountRouteImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => AppRoute,
} as any)
const AppVtuberCategoriesIndexRoute =
  AppVtuberCategoriesIndexRouteImport.update({
    id: '/vtuber-categories/',
    path: '/vtuber-categories/',
    getParentRoute: () => AppRoute,
  } as any)
const AppSocialLinksIndexRoute = AppSocialLinksIndexRouteImport.update({
  id: '/social-links/',
  path: '/social-links/',
  getParentRoute: () => AppRoute,
} as any)
const AppResourceIndexRoute = AppResourceIndexRouteImport.update({
  id: '/resource/',
  path: '/resource/',
  getParentRoute: () => AppRoute,
} as any)
const AppFaqIndexRoute = AppFaqIndexRouteImport.update({
  id: '/faq/',
  path: '/faq/',
  getParentRoute: () => AppRoute,
} as any)
const AppEventsIndexRoute = AppEventsIndexRouteImport.update({
  id: '/events/',
  path: '/events/',
  getParentRoute: () => AppRoute,
} as any)
const AppCategoriesIndexRoute = AppCategoriesIndexRouteImport.update({
  id: '/categories/',
  path: '/categories/',
  getParentRoute: () => AppRoute,
} as any)
const AppCampaignsIndexRoute = AppCampaignsIndexRouteImport.update({
  id: '/campaigns/',
  path: '/campaigns/',
  getParentRoute: () => AppRoute,
} as any)
const AppAnnouncementsIndexRoute = AppAnnouncementsIndexRouteImport.update({
  id: '/announcements/',
  path: '/announcements/',
  getParentRoute: () => AppRoute,
} as any)
const AppVtuberCategoriesAddRoute = AppVtuberCategoriesAddRouteImport.update({
  id: '/vtuber-categories/add',
  path: '/vtuber-categories/add',
  getParentRoute: () => AppRoute,
} as any)
const AppVtuberCategoriesEditRoute = AppVtuberCategoriesEditRouteImport.update({
  id: '/vtuber-categories/$edit',
  path: '/vtuber-categories/$edit',
  getParentRoute: () => AppRoute,
} as any)
const AppPostsIdRoute = AppPostsIdRouteImport.update({
  id: '/posts/$id',
  path: '/posts/$id',
  getParentRoute: () => AppRoute,
} as any)
const AppFaqAddRoute = AppFaqAddRouteImport.update({
  id: '/faq/add',
  path: '/faq/add',
  getParentRoute: () => AppRoute,
} as any)
const AppFaqIdRoute = AppFaqIdRouteImport.update({
  id: '/faq/$id',
  path: '/faq/$id',
  getParentRoute: () => AppRoute,
} as any)
const AppEventsAddRoute = AppEventsAddRouteImport.update({
  id: '/events/add',
  path: '/events/add',
  getParentRoute: () => AppRoute,
} as any)
const AppCategoriesAddRoute = AppCategoriesAddRouteImport.update({
  id: '/categories/add',
  path: '/categories/add',
  getParentRoute: () => AppRoute,
} as any)
const AppCategoriesIdRoute = AppCategoriesIdRouteImport.update({
  id: '/categories/$id',
  path: '/categories/$id',
  getParentRoute: () => AppRoute,
} as any)
const AppCampaignsAddRoute = AppCampaignsAddRouteImport.update({
  id: '/campaigns/add',
  path: '/campaigns/add',
  getParentRoute: () => AppRoute,
} as any)
const AppUsersIdIndexRoute = AppUsersIdIndexRouteImport.update({
  id: '/users/$id/',
  path: '/users/$id/',
  getParentRoute: () => AppRoute,
} as any)
const AppEventsIdIndexRoute = AppEventsIdIndexRouteImport.update({
  id: '/events/$id/',
  path: '/events/$id/',
  getParentRoute: () => AppRoute,
} as any)
const AppCampaignsIdIndexRoute = AppCampaignsIdIndexRouteImport.update({
  id: '/campaigns/$id/',
  path: '/campaigns/$id/',
  getParentRoute: () => AppRoute,
} as any)
const AppEventsIdEditRoute = AppEventsIdEditRouteImport.update({
  id: '/events/$id/edit',
  path: '/events/$id/edit',
  getParentRoute: () => AppRoute,
} as any)
const AppCampaignsVariantVariantIdRoute =
  AppCampaignsVariantVariantIdRouteImport.update({
    id: '/campaigns/variant/$variantId',
    path: '/campaigns/variant/$variantId',
    getParentRoute: () => AppRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/account': typeof AppAccountRoute
  '/community-guidelines': typeof AppCommunityGuidelinesRoute
  '/creator-requests': typeof AppCreatorRequestsRoute
  '/crowdfunding-guidelines': typeof AppCrowdfundingGuidelinesRoute
  '/operating-company': typeof AppOperatingCompanyRoute
  '/privacy-policy': typeof AppPrivacyPolicyRoute
  '/settings': typeof AppSettingsRoute
  '/terms-condition': typeof AppTermsConditionRoute
  '/transaction-act': typeof AppTransactionActRoute
  '/update-guides': typeof AppUpdateGuidesRoute
  '/user-guides': typeof AppUserGuidesRoute
  '/forgot-password': typeof AuthForgotPasswordRoute
  '/login': typeof AuthLoginRoute
  '/reset-password': typeof AuthResetPasswordRoute
  '/': typeof AppIndexRoute
  '/campaigns/add': typeof AppCampaignsAddRoute
  '/categories/$id': typeof AppCategoriesIdRoute
  '/categories/add': typeof AppCategoriesAddRoute
  '/events/add': typeof AppEventsAddRoute
  '/faq/$id': typeof AppFaqIdRoute
  '/faq/add': typeof AppFaqAddRoute
  '/posts/$id': typeof AppPostsIdRoute
  '/vtuber-categories/$edit': typeof AppVtuberCategoriesEditRoute
  '/vtuber-categories/add': typeof AppVtuberCategoriesAddRoute
  '/announcements': typeof AppAnnouncementsIndexRoute
  '/campaigns': typeof AppCampaignsIndexRoute
  '/categories': typeof AppCategoriesIndexRoute
  '/events': typeof AppEventsIndexRoute
  '/faq': typeof AppFaqIndexRoute
  '/resource': typeof AppResourceIndexRoute
  '/social-links': typeof AppSocialLinksIndexRoute
  '/vtuber-categories': typeof AppVtuberCategoriesIndexRoute
  '/campaigns/variant/$variantId': typeof AppCampaignsVariantVariantIdRoute
  '/events/$id/edit': typeof AppEventsIdEditRoute
  '/campaigns/$id': typeof AppCampaignsIdIndexRoute
  '/events/$id': typeof AppEventsIdIndexRoute
  '/users/$id': typeof AppUsersIdIndexRoute
}
export interface FileRoutesByTo {
  '/account': typeof AppAccountRoute
  '/community-guidelines': typeof AppCommunityGuidelinesRoute
  '/creator-requests': typeof AppCreatorRequestsRoute
  '/crowdfunding-guidelines': typeof AppCrowdfundingGuidelinesRoute
  '/operating-company': typeof AppOperatingCompanyRoute
  '/privacy-policy': typeof AppPrivacyPolicyRoute
  '/settings': typeof AppSettingsRoute
  '/terms-condition': typeof AppTermsConditionRoute
  '/transaction-act': typeof AppTransactionActRoute
  '/update-guides': typeof AppUpdateGuidesRoute
  '/user-guides': typeof AppUserGuidesRoute
  '/forgot-password': typeof AuthForgotPasswordRoute
  '/login': typeof AuthLoginRoute
  '/reset-password': typeof AuthResetPasswordRoute
  '/': typeof AppIndexRoute
  '/campaigns/add': typeof AppCampaignsAddRoute
  '/categories/$id': typeof AppCategoriesIdRoute
  '/categories/add': typeof AppCategoriesAddRoute
  '/events/add': typeof AppEventsAddRoute
  '/faq/$id': typeof AppFaqIdRoute
  '/faq/add': typeof AppFaqAddRoute
  '/posts/$id': typeof AppPostsIdRoute
  '/vtuber-categories/$edit': typeof AppVtuberCategoriesEditRoute
  '/vtuber-categories/add': typeof AppVtuberCategoriesAddRoute
  '/announcements': typeof AppAnnouncementsIndexRoute
  '/campaigns': typeof AppCampaignsIndexRoute
  '/categories': typeof AppCategoriesIndexRoute
  '/events': typeof AppEventsIndexRoute
  '/faq': typeof AppFaqIndexRoute
  '/resource': typeof AppResourceIndexRoute
  '/social-links': typeof AppSocialLinksIndexRoute
  '/vtuber-categories': typeof AppVtuberCategoriesIndexRoute
  '/campaigns/variant/$variantId': typeof AppCampaignsVariantVariantIdRoute
  '/events/$id/edit': typeof AppEventsIdEditRoute
  '/campaigns/$id': typeof AppCampaignsIdIndexRoute
  '/events/$id': typeof AppEventsIdIndexRoute
  '/users/$id': typeof AppUsersIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_app': typeof AppRouteWithChildren
  '/_auth': typeof AuthRouteWithChildren
  '/_app/account': typeof AppAccountRoute
  '/_app/community-guidelines': typeof AppCommunityGuidelinesRoute
  '/_app/creator-requests': typeof AppCreatorRequestsRoute
  '/_app/crowdfunding-guidelines': typeof AppCrowdfundingGuidelinesRoute
  '/_app/operating-company': typeof AppOperatingCompanyRoute
  '/_app/privacy-policy': typeof AppPrivacyPolicyRoute
  '/_app/settings': typeof AppSettingsRoute
  '/_app/terms-condition': typeof AppTermsConditionRoute
  '/_app/transaction-act': typeof AppTransactionActRoute
  '/_app/update-guides': typeof AppUpdateGuidesRoute
  '/_app/user-guides': typeof AppUserGuidesRoute
  '/_auth/forgot-password': typeof AuthForgotPasswordRoute
  '/_auth/login': typeof AuthLoginRoute
  '/_auth/reset-password': typeof AuthResetPasswordRoute
  '/_app/': typeof AppIndexRoute
  '/_app/campaigns/add': typeof AppCampaignsAddRoute
  '/_app/categories/$id': typeof AppCategoriesIdRoute
  '/_app/categories/add': typeof AppCategoriesAddRoute
  '/_app/events/add': typeof AppEventsAddRoute
  '/_app/faq/$id': typeof AppFaqIdRoute
  '/_app/faq/add': typeof AppFaqAddRoute
  '/_app/posts/$id': typeof AppPostsIdRoute
  '/_app/vtuber-categories/$edit': typeof AppVtuberCategoriesEditRoute
  '/_app/vtuber-categories/add': typeof AppVtuberCategoriesAddRoute
  '/_app/announcements/': typeof AppAnnouncementsIndexRoute
  '/_app/campaigns/': typeof AppCampaignsIndexRoute
  '/_app/categories/': typeof AppCategoriesIndexRoute
  '/_app/events/': typeof AppEventsIndexRoute
  '/_app/faq/': typeof AppFaqIndexRoute
  '/_app/resource/': typeof AppResourceIndexRoute
  '/_app/social-links/': typeof AppSocialLinksIndexRoute
  '/_app/vtuber-categories/': typeof AppVtuberCategoriesIndexRoute
  '/_app/campaigns/variant/$variantId': typeof AppCampaignsVariantVariantIdRoute
  '/_app/events/$id/edit': typeof AppEventsIdEditRoute
  '/_app/campaigns/$id/': typeof AppCampaignsIdIndexRoute
  '/_app/events/$id/': typeof AppEventsIdIndexRoute
  '/_app/users/$id/': typeof AppUsersIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/account'
    | '/community-guidelines'
    | '/creator-requests'
    | '/crowdfunding-guidelines'
    | '/operating-company'
    | '/privacy-policy'
    | '/settings'
    | '/terms-condition'
    | '/transaction-act'
    | '/update-guides'
    | '/user-guides'
    | '/forgot-password'
    | '/login'
    | '/reset-password'
    | '/'
    | '/campaigns/add'
    | '/categories/$id'
    | '/categories/add'
    | '/events/add'
    | '/faq/$id'
    | '/faq/add'
    | '/posts/$id'
    | '/vtuber-categories/$edit'
    | '/vtuber-categories/add'
    | '/announcements'
    | '/campaigns'
    | '/categories'
    | '/events'
    | '/faq'
    | '/resource'
    | '/social-links'
    | '/vtuber-categories'
    | '/campaigns/variant/$variantId'
    | '/events/$id/edit'
    | '/campaigns/$id'
    | '/events/$id'
    | '/users/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/account'
    | '/community-guidelines'
    | '/creator-requests'
    | '/crowdfunding-guidelines'
    | '/operating-company'
    | '/privacy-policy'
    | '/settings'
    | '/terms-condition'
    | '/transaction-act'
    | '/update-guides'
    | '/user-guides'
    | '/forgot-password'
    | '/login'
    | '/reset-password'
    | '/'
    | '/campaigns/add'
    | '/categories/$id'
    | '/categories/add'
    | '/events/add'
    | '/faq/$id'
    | '/faq/add'
    | '/posts/$id'
    | '/vtuber-categories/$edit'
    | '/vtuber-categories/add'
    | '/announcements'
    | '/campaigns'
    | '/categories'
    | '/events'
    | '/faq'
    | '/resource'
    | '/social-links'
    | '/vtuber-categories'
    | '/campaigns/variant/$variantId'
    | '/events/$id/edit'
    | '/campaigns/$id'
    | '/events/$id'
    | '/users/$id'
  id:
    | '__root__'
    | '/_app'
    | '/_auth'
    | '/_app/account'
    | '/_app/community-guidelines'
    | '/_app/creator-requests'
    | '/_app/crowdfunding-guidelines'
    | '/_app/operating-company'
    | '/_app/privacy-policy'
    | '/_app/settings'
    | '/_app/terms-condition'
    | '/_app/transaction-act'
    | '/_app/update-guides'
    | '/_app/user-guides'
    | '/_auth/forgot-password'
    | '/_auth/login'
    | '/_auth/reset-password'
    | '/_app/'
    | '/_app/campaigns/add'
    | '/_app/categories/$id'
    | '/_app/categories/add'
    | '/_app/events/add'
    | '/_app/faq/$id'
    | '/_app/faq/add'
    | '/_app/posts/$id'
    | '/_app/vtuber-categories/$edit'
    | '/_app/vtuber-categories/add'
    | '/_app/announcements/'
    | '/_app/campaigns/'
    | '/_app/categories/'
    | '/_app/events/'
    | '/_app/faq/'
    | '/_app/resource/'
    | '/_app/social-links/'
    | '/_app/vtuber-categories/'
    | '/_app/campaigns/variant/$variantId'
    | '/_app/events/$id/edit'
    | '/_app/campaigns/$id/'
    | '/_app/events/$id/'
    | '/_app/users/$id/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AppRoute: typeof AppRouteWithChildren
  AuthRoute: typeof AuthRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app': {
      id: '/_app'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AppRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app/': {
      id: '/_app/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AppIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_auth/reset-password': {
      id: '/_auth/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof AuthResetPasswordRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/login': {
      id: '/_auth/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AuthLoginRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/forgot-password': {
      id: '/_auth/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof AuthForgotPasswordRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_app/user-guides': {
      id: '/_app/user-guides'
      path: '/user-guides'
      fullPath: '/user-guides'
      preLoaderRoute: typeof AppUserGuidesRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/update-guides': {
      id: '/_app/update-guides'
      path: '/update-guides'
      fullPath: '/update-guides'
      preLoaderRoute: typeof AppUpdateGuidesRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/transaction-act': {
      id: '/_app/transaction-act'
      path: '/transaction-act'
      fullPath: '/transaction-act'
      preLoaderRoute: typeof AppTransactionActRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/terms-condition': {
      id: '/_app/terms-condition'
      path: '/terms-condition'
      fullPath: '/terms-condition'
      preLoaderRoute: typeof AppTermsConditionRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/settings': {
      id: '/_app/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AppSettingsRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/privacy-policy': {
      id: '/_app/privacy-policy'
      path: '/privacy-policy'
      fullPath: '/privacy-policy'
      preLoaderRoute: typeof AppPrivacyPolicyRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/operating-company': {
      id: '/_app/operating-company'
      path: '/operating-company'
      fullPath: '/operating-company'
      preLoaderRoute: typeof AppOperatingCompanyRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/crowdfunding-guidelines': {
      id: '/_app/crowdfunding-guidelines'
      path: '/crowdfunding-guidelines'
      fullPath: '/crowdfunding-guidelines'
      preLoaderRoute: typeof AppCrowdfundingGuidelinesRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/creator-requests': {
      id: '/_app/creator-requests'
      path: '/creator-requests'
      fullPath: '/creator-requests'
      preLoaderRoute: typeof AppCreatorRequestsRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/community-guidelines': {
      id: '/_app/community-guidelines'
      path: '/community-guidelines'
      fullPath: '/community-guidelines'
      preLoaderRoute: typeof AppCommunityGuidelinesRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/account': {
      id: '/_app/account'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof AppAccountRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/vtuber-categories/': {
      id: '/_app/vtuber-categories/'
      path: '/vtuber-categories'
      fullPath: '/vtuber-categories'
      preLoaderRoute: typeof AppVtuberCategoriesIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/social-links/': {
      id: '/_app/social-links/'
      path: '/social-links'
      fullPath: '/social-links'
      preLoaderRoute: typeof AppSocialLinksIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/resource/': {
      id: '/_app/resource/'
      path: '/resource'
      fullPath: '/resource'
      preLoaderRoute: typeof AppResourceIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/faq/': {
      id: '/_app/faq/'
      path: '/faq'
      fullPath: '/faq'
      preLoaderRoute: typeof AppFaqIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/events/': {
      id: '/_app/events/'
      path: '/events'
      fullPath: '/events'
      preLoaderRoute: typeof AppEventsIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/categories/': {
      id: '/_app/categories/'
      path: '/categories'
      fullPath: '/categories'
      preLoaderRoute: typeof AppCategoriesIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaigns/': {
      id: '/_app/campaigns/'
      path: '/campaigns'
      fullPath: '/campaigns'
      preLoaderRoute: typeof AppCampaignsIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/announcements/': {
      id: '/_app/announcements/'
      path: '/announcements'
      fullPath: '/announcements'
      preLoaderRoute: typeof AppAnnouncementsIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/vtuber-categories/add': {
      id: '/_app/vtuber-categories/add'
      path: '/vtuber-categories/add'
      fullPath: '/vtuber-categories/add'
      preLoaderRoute: typeof AppVtuberCategoriesAddRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/vtuber-categories/$edit': {
      id: '/_app/vtuber-categories/$edit'
      path: '/vtuber-categories/$edit'
      fullPath: '/vtuber-categories/$edit'
      preLoaderRoute: typeof AppVtuberCategoriesEditRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/posts/$id': {
      id: '/_app/posts/$id'
      path: '/posts/$id'
      fullPath: '/posts/$id'
      preLoaderRoute: typeof AppPostsIdRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/faq/add': {
      id: '/_app/faq/add'
      path: '/faq/add'
      fullPath: '/faq/add'
      preLoaderRoute: typeof AppFaqAddRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/faq/$id': {
      id: '/_app/faq/$id'
      path: '/faq/$id'
      fullPath: '/faq/$id'
      preLoaderRoute: typeof AppFaqIdRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/events/add': {
      id: '/_app/events/add'
      path: '/events/add'
      fullPath: '/events/add'
      preLoaderRoute: typeof AppEventsAddRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/categories/add': {
      id: '/_app/categories/add'
      path: '/categories/add'
      fullPath: '/categories/add'
      preLoaderRoute: typeof AppCategoriesAddRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/categories/$id': {
      id: '/_app/categories/$id'
      path: '/categories/$id'
      fullPath: '/categories/$id'
      preLoaderRoute: typeof AppCategoriesIdRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaigns/add': {
      id: '/_app/campaigns/add'
      path: '/campaigns/add'
      fullPath: '/campaigns/add'
      preLoaderRoute: typeof AppCampaignsAddRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/users/$id/': {
      id: '/_app/users/$id/'
      path: '/users/$id'
      fullPath: '/users/$id'
      preLoaderRoute: typeof AppUsersIdIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/events/$id/': {
      id: '/_app/events/$id/'
      path: '/events/$id'
      fullPath: '/events/$id'
      preLoaderRoute: typeof AppEventsIdIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaigns/$id/': {
      id: '/_app/campaigns/$id/'
      path: '/campaigns/$id'
      fullPath: '/campaigns/$id'
      preLoaderRoute: typeof AppCampaignsIdIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/events/$id/edit': {
      id: '/_app/events/$id/edit'
      path: '/events/$id/edit'
      fullPath: '/events/$id/edit'
      preLoaderRoute: typeof AppEventsIdEditRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaigns/variant/$variantId': {
      id: '/_app/campaigns/variant/$variantId'
      path: '/campaigns/variant/$variantId'
      fullPath: '/campaigns/variant/$variantId'
      preLoaderRoute: typeof AppCampaignsVariantVariantIdRouteImport
      parentRoute: typeof AppRoute
    }
  }
}

interface AppRouteChildren {
  AppAccountRoute: typeof AppAccountRoute
  AppCommunityGuidelinesRoute: typeof AppCommunityGuidelinesRoute
  AppCreatorRequestsRoute: typeof AppCreatorRequestsRoute
  AppCrowdfundingGuidelinesRoute: typeof AppCrowdfundingGuidelinesRoute
  AppOperatingCompanyRoute: typeof AppOperatingCompanyRoute
  AppPrivacyPolicyRoute: typeof AppPrivacyPolicyRoute
  AppSettingsRoute: typeof AppSettingsRoute
  AppTermsConditionRoute: typeof AppTermsConditionRoute
  AppTransactionActRoute: typeof AppTransactionActRoute
  AppUpdateGuidesRoute: typeof AppUpdateGuidesRoute
  AppUserGuidesRoute: typeof AppUserGuidesRoute
  AppIndexRoute: typeof AppIndexRoute
  AppCampaignsAddRoute: typeof AppCampaignsAddRoute
  AppCategoriesIdRoute: typeof AppCategoriesIdRoute
  AppCategoriesAddRoute: typeof AppCategoriesAddRoute
  AppEventsAddRoute: typeof AppEventsAddRoute
  AppFaqIdRoute: typeof AppFaqIdRoute
  AppFaqAddRoute: typeof AppFaqAddRoute
  AppPostsIdRoute: typeof AppPostsIdRoute
  AppVtuberCategoriesEditRoute: typeof AppVtuberCategoriesEditRoute
  AppVtuberCategoriesAddRoute: typeof AppVtuberCategoriesAddRoute
  AppAnnouncementsIndexRoute: typeof AppAnnouncementsIndexRoute
  AppCampaignsIndexRoute: typeof AppCampaignsIndexRoute
  AppCategoriesIndexRoute: typeof AppCategoriesIndexRoute
  AppEventsIndexRoute: typeof AppEventsIndexRoute
  AppFaqIndexRoute: typeof AppFaqIndexRoute
  AppResourceIndexRoute: typeof AppResourceIndexRoute
  AppSocialLinksIndexRoute: typeof AppSocialLinksIndexRoute
  AppVtuberCategoriesIndexRoute: typeof AppVtuberCategoriesIndexRoute
  AppCampaignsVariantVariantIdRoute: typeof AppCampaignsVariantVariantIdRoute
  AppEventsIdEditRoute: typeof AppEventsIdEditRoute
  AppCampaignsIdIndexRoute: typeof AppCampaignsIdIndexRoute
  AppEventsIdIndexRoute: typeof AppEventsIdIndexRoute
  AppUsersIdIndexRoute: typeof AppUsersIdIndexRoute
}

const AppRouteChildren: AppRouteChildren = {
  AppAccountRoute: AppAccountRoute,
  AppCommunityGuidelinesRoute: AppCommunityGuidelinesRoute,
  AppCreatorRequestsRoute: AppCreatorRequestsRoute,
  AppCrowdfundingGuidelinesRoute: AppCrowdfundingGuidelinesRoute,
  AppOperatingCompanyRoute: AppOperatingCompanyRoute,
  AppPrivacyPolicyRoute: AppPrivacyPolicyRoute,
  AppSettingsRoute: AppSettingsRoute,
  AppTermsConditionRoute: AppTermsConditionRoute,
  AppTransactionActRoute: AppTransactionActRoute,
  AppUpdateGuidesRoute: AppUpdateGuidesRoute,
  AppUserGuidesRoute: AppUserGuidesRoute,
  AppIndexRoute: AppIndexRoute,
  AppCampaignsAddRoute: AppCampaignsAddRoute,
  AppCategoriesIdRoute: AppCategoriesIdRoute,
  AppCategoriesAddRoute: AppCategoriesAddRoute,
  AppEventsAddRoute: AppEventsAddRoute,
  AppFaqIdRoute: AppFaqIdRoute,
  AppFaqAddRoute: AppFaqAddRoute,
  AppPostsIdRoute: AppPostsIdRoute,
  AppVtuberCategoriesEditRoute: AppVtuberCategoriesEditRoute,
  AppVtuberCategoriesAddRoute: AppVtuberCategoriesAddRoute,
  AppAnnouncementsIndexRoute: AppAnnouncementsIndexRoute,
  AppCampaignsIndexRoute: AppCampaignsIndexRoute,
  AppCategoriesIndexRoute: AppCategoriesIndexRoute,
  AppEventsIndexRoute: AppEventsIndexRoute,
  AppFaqIndexRoute: AppFaqIndexRoute,
  AppResourceIndexRoute: AppResourceIndexRoute,
  AppSocialLinksIndexRoute: AppSocialLinksIndexRoute,
  AppVtuberCategoriesIndexRoute: AppVtuberCategoriesIndexRoute,
  AppCampaignsVariantVariantIdRoute: AppCampaignsVariantVariantIdRoute,
  AppEventsIdEditRoute: AppEventsIdEditRoute,
  AppCampaignsIdIndexRoute: AppCampaignsIdIndexRoute,
  AppEventsIdIndexRoute: AppEventsIdIndexRoute,
  AppUsersIdIndexRoute: AppUsersIdIndexRoute,
}

const AppRouteWithChildren = AppRoute._addFileChildren(AppRouteChildren)

interface AuthRouteChildren {
  AuthForgotPasswordRoute: typeof AuthForgotPasswordRoute
  AuthLoginRoute: typeof AuthLoginRoute
  AuthResetPasswordRoute: typeof AuthResetPasswordRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthForgotPasswordRoute: AuthForgotPasswordRoute,
  AuthLoginRoute: AuthLoginRoute,
  AuthResetPasswordRoute: AuthResetPasswordRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AppRoute: AppRouteWithChildren,
  AuthRoute: AuthRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
