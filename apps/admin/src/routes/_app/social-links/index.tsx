import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, useRouter } from "@tanstack/react-router";
import { OmitTypeName, staticClient } from "@vtuber/services/client";
import { StaticService } from "@vtuber/services/cms";
import { SocialMediaLinks } from "@vtuber/services/shared";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { convertEmptyStringToUndefined } from "@vtuber/ui/lib/convert-empty-string-to-undefined";
import { Link } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export const Route = createFileRoute("/_app/social-links/")({
  component: RouteComponent,
  loader: async () => {
    const [links] = await staticClient.getSocialMediaLinks({});
    return { links };
  },
});

function RouteComponent() {
  const { links } = Route.useLoaderData();
  const router = useRouter();
  const form = useForm<OmitTypeName<SocialMediaLinks>>({
    defaultValues: links ? links.socialMediaLinks : {},
  });

  const mutate = useMutation(StaticService.method.updateSocialMedia, {
    onSuccess: (data) => {
      toast.success(data.message);
      router.invalidate();
    },
    onError: (err) => {
      toast.error(err.rawMessage);
    },
  });

  const onSubmit = form.handleSubmit((data) => {
    mutate.mutate({
      socialMediaLinks: convertEmptyStringToUndefined(data),
    });
  });

  return (
    <Form {...form}>
      <form
        onSubmit={onSubmit}
        className="flex items-center justify-center min-h-[90dvh]">
        <Card className="md:max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Link className="size-5 mt-1" /> Social Links{" "}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid sm:grid-cols-2 items-center gap-6">
              <TextInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                control={form.control}
                label="Youtube Link"
                name="youtube"
                type="url"
                inputMode="url"
                size={"lg"}
              />
              <TextInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                label="Twitter Link"
                size={"lg"}
                control={form.control}
                name="twitter"
                type="url"
                inputMode="url"
              />
              <TextInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                size={"lg"}
                label="Tiktok Link"
                control={form.control}
                name="tiktok"
                type="url"
                inputMode="url"
              />
              <TextInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                label="Twitch Link"
                control={form.control}
                name="twitch"
                type="url"
                size={"lg"}
                inputMode="url"
              />
              <TextInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                label="Discord Link"
                control={form.control}
                name="discord"
                type="url"
                size={"lg"}
                inputMode="url"
              />
              <TextInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                label="Instagram Link"
                size={"lg"}
                control={form.control}
                name="instagram"
                type="url"
                inputMode="url"
              />
            </div>
          </CardContent>
          <CardFooter className="block">
            <Button
              loading={mutate.isPending}
              size={"xl"}
              className="w-full"
              variant={"success"}>
              Update
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
