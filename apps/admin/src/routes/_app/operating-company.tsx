import { createFileRoute, Link, notFound } from "@tanstack/react-router";
import { staticClient } from "@vtuber/services/client";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@vtuber/ui/components/accordion";
import { buttonVariants } from "@vtuber/ui/components/button";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { cn } from "@vtuber/ui/lib/utils";
import { PencilLineIcon, Plus } from "lucide-react";
import { RouteNotFoundComponent } from "~/components/route-not-found-component";
import { RoutePendingComponent } from "~/components/route-pending-component";

export const Route = createFileRoute("/_app/operating-company")({
  component: RouteComponent,
  loader: async ({ location }) => {
    const [res, err] = await staticClient.getAllStaticResource({
      key: location.pathname.replace("/", ""),
    });
    if (err) {
      throw notFound({
        data: {
          message: err.rawMessage,
        },
      });
    }
    return res.data;
  },
  pendingComponent: () => <RoutePendingComponent />,
  notFoundComponent: ({ data }) => <RouteNotFoundComponent data={data} />,
});

function RouteComponent() {
  const staticResources = Route.useLoaderData();
  return (
    <div className="pt-10">
      {staticResources.length === 1 && (
        <div className="flex justify-end pb-10">
          <Link
            search={{
              key: "operating-company",
            }}
            mask={{
              to: "/update-guides",
              search: {
                key: undefined,
              },
            }}
            to="/update-guides"
            className={cn(
              buttonVariants({
                variant: "add",
                size: "xl",
              }),
              "flex items-center gap-x-3",
            )}>
            <Plus /> Add{" "}
          </Link>
        </div>
      )}
      {staticResources.length > 0 ? (
        <Accordion
          collapsible
          className="space-y-3"
          type="single">
          {staticResources.map((sr) => (
            <AccordionItem
              className="border-none"
              value={sr.language}
              key={sr.id.toString()}>
              <AccordionTrigger className="bg-sub rounded-lg px-4 py-8 text-xl font-semibold relative">
                {sr.language === "en-us" ? "English" : "日本語"}

                <Link
                  to="/update-guides"
                  search={{
                    id: sr.id.toString(),
                    content: sr.value,
                    key: sr.key,
                  }}
                  mask={{
                    to: "/update-guides",
                    search: {
                      id: undefined,
                      content: undefined,
                    },
                  }}
                  className={buttonVariants({
                    variant: "ghost",
                    className: "gap-x-3 absolute right-20 z-30",
                  })}>
                  <PencilLineIcon /> Edit
                </Link>
              </AccordionTrigger>
              <AccordionContent className="p-8">
                <MarkDown markdown={sr.value} />
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      ) : (
        <div className="flex items-center justify-center h-[80dvh] flex-col gap-y-6">
          <p className="text-3xl font-semibold">No Operating Company found</p>
          <Link
            search={{
              key: "community-guidelines",
            }}
            mask={{
              to: "/update-guides",
              search: {
                key: undefined,
              },
            }}
            to="/update-guides"
            className={cn(
              buttonVariants({
                variant: "outline-dark",
                size: "xl",
              }),
              "flex items-center gap-x-3",
            )}>
            <Plus /> Add
          </Link>
        </div>
      )}
    </div>
  );
}
