import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, useRouter } from "@tanstack/react-router";
import { vtuberProfilesClient } from "@vtuber/services/client";
import {
  GetAllVtuberProfileAccessResponse,
  VtuberProfilesService,
} from "@vtuber/services/vtubers";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import { ColumnDef, DataTable } from "@vtuber/ui/components/data-table";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Form } from "@vtuber/ui/components/form";
import { TextAreaInput } from "@vtuber/ui/form/text-area-input";
import { useDisclosure } from "@vtuber/ui/hooks/use-disclosure";
import { Check, Eye, X } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { CreatorRequestDialog } from "~/components/CreatorRequestDialog";

const SearchSchema = z.object({
  page: z.number().optional(),
  size: z.number().optional(),
});

export const Route = createFileRoute("/_app/creator-requests")({
  validateSearch: SearchSchema,
  component: RouteComponent,
  loader: async ({ location }) => {
    const search = SearchSchema.parse(location.search);
    const [requests, error] =
      await vtuberProfilesClient.getAllVtuberProfileAccess({
        pagination: {
          sort: "status",
          page: search.page,
        },
      });

    return requests;
  },
});

const Approve = ({ id }: { id: string }) => {
  const router = useRouter();
  const mutation = useMutation(
    VtuberProfilesService.method.verifyVtuberProfile,
    {
      onSuccess() {
        toast.success("Request approved");
        router.invalidate();
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const handleApprove = () => {
    mutation.mutate({
      id,
    });
  };
  return (
    <Button
      loading={mutation.isPending}
      onClick={handleApprove}
      size={"icon"}
      className="bg-green-500 hover:bg-green-600 ">
      <Check />
    </Button>
  );
};

const Reject = ({ id }: { id: string }) => {
  const { close, ...dialogprops } = useDisclosure();
  const router = useRouter();

  const form = useForm({
    defaultValues: {
      reason: "",
    },
  });
  const mutation = useMutation(VtuberProfilesService.method.denyVtuberProfile, {
    onSuccess() {
      toast.success("Request rejected");
      router.invalidate();
      close();
    },
    onError: (err) => {
      toast.error(err.message);
    },
  });

  const onSubmit = form.handleSubmit((data) => {
    mutation.mutate({
      id,
      reason: data.reason,
    });
  });

  return (
    <Dialog {...dialogprops}>
      <DialogTrigger asChild>
        <Button
          size={"icon"}
          className="bg-red-500 hover:bg-red-600 ">
          <X />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Reject Request</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={onSubmit}>
            <div className="grid gap-6">
              <div className="grid gap-6">
                <TextAreaInput
                  control={form.control}
                  name="reason"
                  label="Reason"
                  placeholder="Enter reason for rejection"
                />
                <DialogFooter>
                  <Button
                    loading={mutation.isPending}
                    type="submit"
                    className="w-full">
                    Reject Request
                  </Button>
                </DialogFooter>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

const columns: ColumnDef<GetAllVtuberProfileAccessResponse["data"][0]>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },

  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      return (
        <Badge
          variant={
            row.original.status === "approved"
              ? "success"
              : row.original.status === "rejected"
                ? "destructive"
                : "outline"
          }
          className="text-sm capitalize">
          {row.original.status}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const status = row.original.status;
      const shouldShow = status === "pending" || status == "applied";
      const router = useRouter();
      const [selectedVtuber, setSelectedVtuber] = useState<
        GetAllVtuberProfileAccessResponse["data"][0] | null
      >(null);
      const [dialogOpen, setDialogOpen] = useState(false);

      const handleViewDetails = () => {
        setSelectedVtuber(row.original);
        setDialogOpen(true);
      };

      return (
        <div className="gap-2 flex items-center">
          <Button
            size="icon"
            variant="outline"
            className="snap-normal"
            onClick={handleViewDetails}>
            <Eye className="h-4 w-4" />
          </Button>
          {selectedVtuber && (
            <CreatorRequestDialog
              vtuber={selectedVtuber}
              open={dialogOpen}
              onOpenChange={setDialogOpen}
            />
          )}
          {shouldShow ? (
            <>
              <Approve id={row.original.id} />
              <Reject id={row.original.id} />
            </>
          ) : null}
        </div>
      );
    },
  },
];

function RouteComponent() {
  const requests = Route.useLoaderData();

  return (
    <div>
      <DataTable
        columns={columns}
        data={requests?.data || []}
        pagination={{
          currentPage: requests?.paginationDetails?.currentPage,
          isLast:
            requests?.paginationDetails?.currentPage ===
            requests?.paginationDetails?.totalPages,
          totalItems: requests?.paginationDetails?.totalItems,
          totalPage: requests?.paginationDetails?.totalPages,
        }}
      />
    </div>
  );
}
