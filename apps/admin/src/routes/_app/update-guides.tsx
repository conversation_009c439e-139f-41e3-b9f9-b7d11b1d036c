import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, useRouter } from "@tanstack/react-router";
import { StaticService } from "@vtuber/services/cms";
import { Button } from "@vtuber/ui/components/button";
import { Form } from "@vtuber/ui/components/form";
import { HtmlInput } from "@vtuber/ui/components/form-inputs/html-input";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

export const Route = createFileRoute("/_app/update-guides")({
  component: RouteComponent,
  validateSearch: z.object({
    key: z.string().optional(),
    id: z.string().optional(),
    content: z.string().optional(),
  }),
});

function RouteComponent() {
  const router = useRouter();
  const { key, id, content } = Route.useSearch();
  const form = useForm({
    defaultValues: {
      key,
      value: content,
      language: "en-us",
    },
  });

  const isAddMode = !id && !content;
  const { mutate: updateMutation, isPending: isUpdating } = useMutation(
    StaticService.method.updateStaticResource,
    {
      onSuccess: () => {
        toast.success(key?.replace(/[^\w\s]/gi, "") + " updated successfully");
        router.history.back();
        form.reset();
      },
      onError: (err) => {
        toast.error(err.rawMessage);
      },
    },
  );
  const { mutate: addMutation, isPending: isAdding } = useMutation(
    StaticService.method.addStaticResource,
    {
      onSuccess: () => {
        toast.success(key?.replace(/[^\w\s]/gi, "") + " added successfully");
        router.history.back();
        form.reset();
      },
      onError: (err) => {
        toast.error(err.rawMessage);
      },
    },
  );

  const onSubmit = form.handleSubmit((data) => {
    if (isAddMode) {
      addMutation({
        key: data.key,
        value: data.value,
        language: data.language,
      });
      return;
    }

    updateMutation({
      id: String(id ?? 0n),
      value: data.value,
    });
  });

  return (
    <div>
      <Form {...form}>
        <form
          onSubmit={onSubmit}
          className="grid gap-y-6">
          {isAddMode && (
            <SelectInput
              label="Select Language"
              control={form.control}
              name="language"
              options={[
                {
                  label: "English",
                  value: "en-us",
                },
                {
                  label: "Japanese",
                  value: "ja-jp",
                },
              ]}
            />
          )}
          <HtmlInput
            control={form.control}
            name="value"
          />
          <Button
            loading={isUpdating || isAdding}
            className="w-full">
            {isAddMode ? "Add" : "Update"}
          </Button>
        </form>
      </Form>
    </div>
  );
}
