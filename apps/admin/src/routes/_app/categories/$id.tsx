import { createFileRoute } from "@tanstack/react-router";
import { categoryClient } from "@vtuber/services/client";
import { CategoryForm } from "~/components/CategoryForm";

export const Route = createFileRoute("/_app/categories/$id")({
  component: EditCategoryPage,
  loader: async ({ params }) => {
    const [category] = await categoryClient.getCategory({
      id: String(params.id),
    });
    return category;
  },
});

function EditCategoryPage() {
  const category = Route.useLoaderData();

  if (!category?.data) return null;

  return (
    <CategoryForm
      mode="edit"
      initialData={{
        id: category.data.id,
        name: category.data.name,
        description: category.data.description,
        // image: category.data.,
      }}
    />
  );
}
