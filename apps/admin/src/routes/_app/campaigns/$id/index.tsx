import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { campaignClient } from "@vtuber/services/client";
import { Avatar } from "@vtuber/ui/components/avatar";
import { CampaignTab } from "@vtuber/ui/components/campaigns/campaign-tab";
import { ExternalLinks } from "@vtuber/ui/components/external-links";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { z } from "zod";
import { CampaignDetailsBanner } from "~/components/campaign/CampaignDetailsBanner";
import { CampaignProgress } from "~/components/campaign/CampaignProgress";
import { CampaignVariants } from "~/components/campaign/CampaignVariants";

export const Route = createFileRoute("/_app/campaigns/$id/")({
  component: RouteComponent,
  validateSearch: z.object({
    page: z.number().optional(),
  }),
  loader: async ({ params }) => {
    const [campaign] = await campaignClient.getCampaignById({
      id: params.id,
    });
    return { campaign };
  },
});

function RouteComponent() {
  const { campaign } = Route.useLoaderData();
  const { getText } = useLanguage();
  const { page } = Route.useSearch();
  if (!campaign || !campaign.data) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center p-8">
          <h2 className="text-2xl font-semibold text-gray-700 mb-2">
            {getText("Campaign_Not_Found")}
          </h2>
          <p className="text-gray-500">{getText("Campaign_Not_Found")} </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="relative overflow-hidden">
        <CampaignDetailsBanner banners={campaign.data.banners} />
      </div>
      <div className="relative -mt-16 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6  lg:px-8">
          <div className="rounded-2xl shadow-xl border bg-gradient-3 p-6 sm:p-8 mb-8">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-4">
                  <Avatar src={getCdnUrl(campaign.data.thumbnail)} />
                  <div>
                    <h1 className="text-3xl sm:text-4xl font-bold capitalize leading-tight">
                      {campaign.data?.name}
                    </h1>
                    <div className="flex items-center gap-2 mt-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      <p className="text-lg ">
                        {getText("created_by")} :
                        <span className="ml-2 font-semibold">
                          {campaign.data.vtuber?.displayName}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex-shrink-0">
                <ExternalLinks
                  socialMediaLinks={campaign.data.socialMediaLinks}
                />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 pb-12">
            <div className="lg:col-span-8">
              <CampaignTab
                campaign={campaign.data}
                page={page}
              />
            </div>
            <div className="lg:col-span-4 space-y-6">
              <CampaignProgress campaign={campaign.data} />
              <CampaignVariants campaign={campaign.data} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RouteComponent;
