import { createFileRoute } from "@tanstack/react-router";
import { campaignClient } from "@vtuber/services/client";
import { Paginator } from "@vtuber/ui/components/paginator";
import { z } from "zod";
import { CampaignCard } from "~/components/campaign/CampaignCard";
import { NoAvailableMessage } from "~/components/NoAvailableMessage";
import { validatePagination } from "~/data/constatns";

export const Route = createFileRoute("/_app/campaigns/")({
  component: RouteComponent,
  validateSearch: validatePagination,
  loader: async ({ deps }) => {
    const params = deps as z.infer<typeof validatePagination>;
    const page = !!params?.page ? Number(params.page) - 1 : 0;
    const [campaigns] = await campaignClient.getAllCampaigns({
      pagination: {
        page,
        size: params?.size || 20,
        sort: "created_at",
        order: "desc",
        ...params,
      },
    });
    return campaigns;
  },
  loaderDeps: ({ search }) => search,
});

function RouteComponent() {
  const campaigns = Route.useLoaderData();

  if (!campaigns?.data || campaigns.data.length === 0)
    return <NoAvailableMessage message="NO Campaign Found" />;

  return (
    <div className="p-6 space-y-10">
      {campaigns?.data && campaigns.data.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {campaigns?.data.map((campaign) => (
            <CampaignCard
              key={campaign.id.toString()}
              campaign={campaign}
            />
          ))}
        </div>
      )}

      <Paginator
        currentPage={campaigns?.paginationDetails?.currentPage}
        totalPages={campaigns?.paginationDetails?.totalPages}
      />
    </div>
  );
}
