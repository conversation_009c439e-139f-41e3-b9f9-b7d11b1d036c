import { timestampDate } from "@bufbuild/protobuf/wkt";
import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { SubDetails } from "@vtuber/services/campaigns";
import { campaignVariantClient } from "@vtuber/services/client";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Image } from "@vtuber/ui/components/image";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Progress } from "@vtuber/ui/components/progress";
import { Skeleton } from "@vtuber/ui/components/skeleton";
import {
  Calendar,
  Clock,
  Japanese<PERSON>en,
  <PERSON>rk<PERSON>,
  Target,
  TrendingUp,
  Users,
} from "lucide-react";
import { useState } from "react";

export const Route = createFileRoute("/_app/campaigns/variant/$variantId")({
  component: RouteComponent,
  loader: async ({ params }) => {
    const [variant] = await campaignVariantClient.getCampaignVariantById({
      id: String(params.variantId),
    });
    const [subscribers] = await campaignVariantClient.getCampaignVariantSubs({
      id: String(params.variantId),
    });
    return { variant, subscribers, isLoading: false, error: null };
  },
});

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(date);
};

function SubscriberCard({ subscriber }: { subscriber: SubDetails }) {
  return (
    <Card className="group relative overflow-hidden rounded-2xl  bg-gradient-3 transition-all duration-500 hover:shadow-2xl hover:bg-gradient-2 hover:-translate-y-2">
      <CardContent className="relative p-6">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Avatar
              src={subscriber.image}
              className="relative z-10 h-16 w-16 ring-4 ring-white shadow-lg group-hover:ring-blue-500/30 transition-all duration-500"
            />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold duration-300">
                {subscriber.name}
              </h3>
            </div>
            <div className="flex items-center gap-2 mt-2">
              <JapaneseYen className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
              <span className="text-2xl font-bold">{subscriber.amount}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function CampaignSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <Skeleton className="h-80 w-full rounded-2xl" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Skeleton className="h-64 w-full rounded-2xl" />
          <Skeleton className="h-96 w-full rounded-2xl" />
        </div>
        <div className="space-y-6">
          <Skeleton className="h-80 w-full rounded-2xl" />
        </div>
      </div>
    </div>
  );
}

function StatsCard({
  icon: Icon,
  title,
  value,
  subtitle,
}: {
  icon: React.ElementType;
  title: string;
  value: string | number;
  subtitle?: string;
}) {
  return (
    <Card className="group relative  overflow-hidden rounded-2xl  hover:border-gray-300 transition-all duration-500 hover:shadow-xl hover:-translate-y-1">
      <div className="relative p-6">
        <div className="flex items-center gap-4">
          <div
            className={`
            p-3 rounded-xl bg-gradient-to-r
            group-hover:scale-110 transition-transform duration-300 shadow-lg
          `}>
            <Icon
              className="text-green-800"
              size={24}
            />
          </div>

          <div className="flex-1">
            <p className="text-sm font-medium  mb-1">{title}</p>
            <p className="text-2xl font-bold ">{value}</p>
            {subtitle && <p className="text-xs  mt-1">{subtitle}</p>}
          </div>
        </div>
      </div>
    </Card>
  );
}

function RouteComponent() {
  const { variant, subscribers, isLoading, error } = Route.useLoaderData();
  const { getText } = useLanguage();
  const [isImageLoading, setIsImageLoading] = useState(true);

  const maxSubs = variant?.data?.maxSub || 0;
  const currentSubs = variant?.data?.subCount || 0;
  const progressPercentage =
    maxSubs > 0 ? (Number(currentSubs) / maxSubs) * 100 : 0;
  const hasSubscribers = subscribers?.subs && subscribers.subs.length > 0;
  if (isLoading) return <CampaignSkeleton />;

  if (error) {
    return (
      <div className="min-h-96 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
              <span className="text-2xl">😞</span>
            </div>
            <h3 className="text-xl font-bold text-red-600 mb-2">
              Oops! Something went wrong
            </h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!variant?.data) {
    return (
      <div className="min-h-96 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
              <span className="text-2xl">🔍</span>
            </div>
            <h3 className="text-xl font-bold mb-2">Variant Not Found</h3>
            <p className="text-gray-600">{getText("No_Variants_Available")} </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {variant?.data?.image && (
        <div className="relative overflow-hidden rounded-2xl mb-8 shadow-2xl">
          {isImageLoading && (
            <Skeleton className="w-full h-80 absolute top-0 left-0 rounded-2xl" />
          )}
          <AspectRatio ratio={200 / 100}>
            <Image
              className="size-full rounded-[22px] object-cover"
              src={variant.data.image}
              alt={variant.data.title}
            />
          </AspectRatio>

          <div className="absolute bottom-0 left-0 right-0 z-20 p-8 ">
            <h1 className="text-4xl font-bold mb-2 drop-shadow-lg">
              {variant.data.title}
            </h1>
            <div className="flex items-center gap-4">
              <Badge
                variant={currentSubs < maxSubs ? "success" : "destructive"}
                className="text-sm px-3 py-1">
                {currentSubs < maxSubs ? "🟢 Open" : "🔴 Filled"}
              </Badge>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <StatsCard
          icon={Users}
          title="Subscribers"
          value={currentSubs.toString()}
          subtitle={`of ${maxSubs} maximum`}
        />
        <StatsCard
          icon={JapaneseYen}
          title="Price"
          value={variant?.data?.price || "Free"}
        />
        <StatsCard
          icon={TrendingUp}
          title="Progress"
          value={`${Math.round(progressPercentage)}%`}
          subtitle="completion"
        />
        <StatsCard
          icon={Target}
          title="Status"
          value={currentSubs < maxSubs ? "Open" : "Filled"}
          subtitle={currentSubs < maxSubs ? "accepting subs" : "completed"}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-8">
          <Card className="overflow-hidden rounded-2xl border-0 shadow-xl">
            <CardHeader className="bg-gradient-3">
              <CardTitle className="text-2xl flex items-center gap-3">
                <Sparkles size={24} />
                {getText("About")}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <div className="prose max-w-none">
                <MarkDown
                  markdown={
                    variant?.data?.description || "No description available."
                  }
                />
              </div>
            </CardContent>
          </Card>

          <Card className="overflow-hidden rounded-2xl border-0 shadow-xl">
            <CardHeader className="bg-gradient-3">
              <CardTitle className="text-2xl flex items-center gap-3">
                <Users size={24} />
                <span>{getText("supporters")}</span>
                <Badge
                  variant="outline"
                  className="bg-white/20 text-white border-white/30">
                  {subscribers?.subs?.length || 0}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              {!hasSubscribers ? (
                <div className="text-center py-16">
                  <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br flex items-center justify-center">
                    <Users
                      size={32}
                      className="text-blue-500"
                    />
                  </div>
                  <h3 className="text-xl font-bold text-gray-700 mb-2">
                    {getText("No_Subscribers_Yet")}
                  </h3>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {subscribers.subs.map((subscriber, index) => (
                      <SubscriberCard
                        key={index}
                        subscriber={subscriber}
                      />
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card className="overflow-hidden rounded-2xl border-0 shadow-xl">
            <CardHeader className="bg-gradient-3">
              <CardTitle className="flex items-center gap-3">
                <Target size={20} />
                {getText("Campaign_Progress")}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-sm font-medium ">
                      {getText("subscription_progress")}
                    </span>
                    <span className="text-sm font-bold">
                      {Math.round(progressPercentage)}%
                    </span>
                  </div>
                  <Progress
                    value={progressPercentage}
                    className="h-3 bg-gray-100"
                  />
                  <div className="flex justify-between mt-2 text-sm">
                    <span>
                      {currentSubs} {getText("supporters")}
                    </span>
                    <span>
                      {maxSubs} {getText("maximum")}
                    </span>
                  </div>
                </div>

                <div className="space-y-4 pt-4 border-t ">
                  <div className="flex items-center gap-3 p-3 rounded-xl">
                    <Calendar className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium">
                        {getText("Created_At")}
                      </p>
                      <p className="text-sm">
                        {variant?.data?.createdAt
                          ? formatDate(timestampDate(variant.data.createdAt))
                          : "N/A"}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 rounded-xl ">
                    <Clock className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-sm font-medium ">
                        {getText("Status")}
                      </p>
                      <Badge
                        variant={
                          currentSubs < maxSubs ? "success" : "destructive"
                        }
                        className="mt-1">
                        {currentSubs < maxSubs
                          ? getText("Open_for_Subscriptions")
                          : getText("subscription_filled")}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Custom Styles */}
    </div>
  );
}
