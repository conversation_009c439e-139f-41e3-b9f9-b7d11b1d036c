import {
  createFileRoute,
  <PERSON>,
  notFound,
  useRouter,
} from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { vtuberCategoryClient } from "@vtuber/services/client";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { DeleteDialog } from "@vtuber/ui/components/DeleteDialog";
import { Edit, Plus, Trash } from "lucide-react";
import { toast } from "sonner";

export const Route = createFileRoute("/_app/vtuber-categories/")({
  component: RouteComponent,
  loader: async () => {
    const [res, err] = await vtuberCategoryClient.getAllVtuberCategories({});
    if (err) {
      throw notFound({
        data: {
          message: err.rawMessage,
        },
      });
    }
    return res.categories;
  },
});

function RouteComponent() {
  const vtuberCategories = Route.useLoaderData();
  const { getText } = useLanguage();
  const router = useRouter();
  return (
    <div className="space-y-6 pt-10">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{getText("vtuber_categories")}</h1>

        <Link to="/vtuber-categories/add">
          <Button className="gap-2 shadow-lg">
            <Plus className="h-4 w-4" />
            {getText("Add_Category")}
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        {vtuberCategories?.map((category) => (
          <Card
            key={category.id}
            className="h-full flex flex-col overflow-hidden border-0 shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 ease-in-out group cursor-pointer">
            <CardHeader className="group-hover:bg-gradient-to-r  transition-all duration-300">
              <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors duration-300">
                {category.name}
              </CardTitle>
            </CardHeader>

            <CardContent className="flex-grow  transition-all duration-300">
              <CardDescription className="transition-colors duration-300">
                {category.description.length > 100
                  ? `${category.description.slice(0, 100)}...`
                  : category.description}
              </CardDescription>
            </CardContent>

            <CardFooter className="mt-auto transition-all gap-3 duration-300">
              <Link
                params={{ edit: category.id.toString() }}
                to="/vtuber-categories/$edit"
                className="w-full">
                <Button
                  variant="outline"
                  className="w-full border">
                  <Edit className="h-4 w-4 hover:rotate-12 mr-2 transition-transform duration-300" />
                  {getText("EDIT")}
                </Button>
              </Link>
              <DeleteDialog
                inValidate={false}
                name="Category"
                onDelete={async () => {
                  const [res, err] = await vtuberCategoryClient.deleteCategory({
                    id: category.id,
                  });

                  if (err) return toast.error(err.message);

                  if (res) {
                    router.invalidate();
                  }
                }}>
                <Button
                  variant="destructive"
                  className="w-full border capitalize">
                  <Trash className="h-4 w-4 hover:rotate-12 transition-transform duration-300 mr-2" />
                  {getText("delete")}
                </Button>
              </DeleteDialog>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
