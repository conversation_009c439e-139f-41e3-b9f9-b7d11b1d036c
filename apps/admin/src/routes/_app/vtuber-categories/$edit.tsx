import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, notFound, useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import {
  handleConnectError,
  vtuberCategoryClient,
} from "@vtuber/services/client";
import {
  UpdateVtuberCategoryRequest,
  VtuberCategoryService,
} from "@vtuber/services/taxonomy";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { useForm } from "react-hook-form";

export const Route = createFileRoute("/_app/vtuber-categories/$edit")({
  component: RouteComponent,
  loader: async ({ params }) => {
    const [category, err] = await vtuberCategoryClient.getVtuberCategory({
      id: String(params.edit),
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    return category.data;
  },
});

function RouteComponent() {
  const category = Route.useLoaderData();
  const { getText } = useLanguage();
  const router = useRouter();
  const form = useForm<UpdateVtuberCategoryRequest>({
    defaultValues: {
      description: category?.description,
      name: category?.name,
      id: category?.id,
    },
  });

  const { mutateAsync, isPending } = useMutation(
    VtuberCategoryService.method.updateCategory,
    {
      onSuccess: () => {
        router.invalidate();
        router.history.back();
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const onUpdate = form.handleSubmit(async (val) => {
    await mutateAsync(val);
  });
  return (
    <div className="pt-20">
      <Card className="md:w-[50%] w-full mx-auto">
        <CardHeader>
          <CardTitle>Edit Vtuber Category</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form
              className="space-y-6"
              onSubmit={onUpdate}>
              <TextInput
                control={form.control}
                name="name"
                size="lg"
                variant="muted"
                label={getText("Category_Name")}
                placeholder={getText("Category_Name")}
              />
              <TextAreaInput
                variant={"muted"}
                control={form.control}
                name="description"
                label={getText("CATEGORY_DESCRIPTION")}
                placeholder={getText("CATEGORY_DESCRIPTION")}
              />
              <Button
                loading={isPending}
                variant={"success"}
                size={"lg"}
                className="w-full"
                type="submit">
                {getText("update")}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
