import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import {
  AddVtuberCategoryRequest,
  VtuberCategoryService,
} from "@vtuber/services/taxonomy";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { useForm } from "react-hook-form";

export const Route = createFileRoute("/_app/vtuber-categories/add")({
  component: RouteComponent,
});

function RouteComponent() {
  const { getText } = useLanguage();
  const router = useRouter();
  const form = useForm<AddVtuberCategoryRequest>({
    defaultValues: {},
  });

  const { mutateAsync, isPending } = useMutation(
    VtuberCategoryService.method.addCategory,
    {
      onSuccess: () => {
        router.invalidate();
        router.history.back();
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const onAdd = form.handleSubmit(async (val) => {
    await mutateAsync(val);
  });
  return (
    <div className="pt-20">
      <Card className="md:w-[50%] w-full mx-auto">
        <CardHeader>
          <CardTitle>Edit Vtuber Category</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form
              className="space-y-6"
              onSubmit={onAdd}>
              <TextInput
                control={form.control}
                name="name"
                size="lg"
                variant="muted"
                label={getText("Category_Name")}
                placeholder={getText("Category_Name")}
              />
              <TextAreaInput
                variant={"muted"}
                control={form.control}
                name="description"
                label={getText("CATEGORY_DESCRIPTION")}
                placeholder={getText("CATEGORY_DESCRIPTION")}
              />
              <Button
                loading={isPending}
                variant={"success"}
                size={"lg"}
                className="w-full"
                type="submit">
                {getText("update")}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
