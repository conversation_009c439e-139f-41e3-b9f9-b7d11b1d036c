import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation } from "@connectrpc/connect-query";
import {
  createFileRoute,
  <PERSON>,
  useNavigate,
  useRouter,
} from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { eventClient, handleConnectError } from "@vtuber/services/client";
import { EventService, GetAllEventsResponse } from "@vtuber/services/events";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Image } from "@vtuber/ui/components/image";
import { Paginator } from "@vtuber/ui/components/paginator";
import { Calendar, Eye, Pencil, Plus, Trash, User } from "lucide-react";
import { toast } from "sonner";
import { z } from "zod";
import { NoAvailableMessage } from "~/components/NoAvailableMessage";

const SearchSchema = z.object({
  page: z.number().optional(),
  size: z.number().optional(),
});

export const Route = createFileRoute("/_app/events/")({
  component: RouteComponent,
  loader: async ({ location }) => {
    const search = SearchSchema.parse(location.search);
    const [events] = await eventClient.getAllEvents({
      pagination: {
        page: search.page,
        size: search.size,
        sort: "created_at",
        order: "desc",
      },
    });
    return events;
  },
});

function RouteComponent() {
  const { session: user } = useAuth();
  const events = Route.useLoaderData();
  const { getText } = useLanguage();
  const navigate = useNavigate();
  const router = useRouter();
  const mutation = useMutation(EventService.method.deleteEventById, {
    onError: (err) => {
      handleConnectError(err);
    },
    onSuccess: () => {
      toast.success("Event deleted successfully");
      router.invalidate();
    },
  });

  const handleDelete = (id: string) => {
    const confirm = window.confirm(
      "Are you sure you want to delete this event?",
    );
    if (confirm) {
      mutation.mutateAsync({ id });
    }
  };

  const isEventCreatedByCurrentUser = (
    event: GetAllEventsResponse["data"][0],
  ) => {
    return event.user?.id === user?.user?.id || user?.user?.role == "admin";
  };

  const hasEvents = events?.data && events.data.length > 0;

  return (
    <div className="p-4 min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-transparent">
          {getText("events")}
        </h1>
        <Link to="/events/add">
          <Button className="transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
            <Plus className="mr-2 h-4 w-4" /> {getText("add_event")}
          </Button>
        </Link>
      </div>

      {!hasEvents ? (
        <div className="flex flex-col items-center space-y-4">
          <NoAvailableMessage message="NO Event Found" />
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {events?.data?.map((event) => {
              const canEdit = isEventCreatedByCurrentUser(event);
              return (
                <Card
                  key={event.id.toString()}
                  className="group relative overflow-hidden  border-0 shadow-md duration-300 ease-out rounded-xl">
                  <div className="relative overflow-hidden">
                    {event.image && (
                      <Image
                        src={event.image}
                        alt={event.title}
                        className="h-48 w-full object-cover transition-transform duration-500 group-hover:scale-110"
                      />
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    <div className="absolute top-3 right-3">
                      <Badge
                        variant={
                          event.status === "approved"
                            ? "success"
                            : event.status === "rejected"
                              ? "destructive"
                              : event.status === "pending"
                                ? "default"
                                : "secondary"
                        }
                        className="capitalize   backdrop-blur-sm  transform group-hover:scale-105 transition-transform duration-200">
                        {event.status}
                      </Badge>
                    </div>
                  </div>

                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg font-semibold line-clamp-2">
                      {event.title}
                    </CardTitle>
                  </CardHeader>

                  <CardContent className="space-y-3 pb-4">
                    <div className="space-y-2">
                      <div className="flex items-center text-sm ">
                        <Calendar className="mr-2 h-4 w-4 text-green-500" />
                        <span className="font-medium">
                          {getText("start_date")}:{" "}
                        </span>
                        <span className="ml-2">
                          {timestampDate(event.startDate!).toDateString()}
                        </span>
                      </div>
                      <div className="flex items-center text-sm ">
                        <Calendar className="mr-2 h-4 w-4 text-red-500" />
                        <span className="font-medium">
                          {getText("end_date")}:
                        </span>
                        <span className="ml-2">
                          {timestampDate(event.endDate!).toDateString()}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center text-sm">
                      <User className="mr-2 h-4 w-4 text-blue-500" />
                      <span className="font-medium ">
                        {getText("created_by")}:
                      </span>
                      <span className="ml-1 font-medium">
                        {event.user?.name}
                      </span>
                    </div>
                  </CardContent>

                  <CardFooter className="pt-0 pb-4">
                    <div className="flex flex-wrap gap-2 w-full">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          navigate({
                            to: "/events/$id",
                            params: { id: event.id.toString() },
                          })
                        }
                        className="flex-1 min-w-fit  transition-all duration-200 transform hover:scale-105">
                        <Eye className="mr-1 h-3 w-3" /> {getText("view")}
                      </Button>

                      {canEdit && (
                        <>
                          <Link
                            to="/events/$id/edit"
                            params={{ id: event.id.toString() }}
                            className="flex-1 min-w-fit">
                            <Button
                              size="sm"
                              className="w-full bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 transform hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg">
                              <Pencil className="mr-1 h-3 w-3" />{" "}
                              {getText("EDIT")}
                            </Button>
                          </Link>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDelete(event.id)}
                            className="flex-1 min-w-fit bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 transform hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg">
                            <Trash className="mr-1 h-3 w-3" />{" "}
                            {getText("delete")}
                          </Button>
                        </>
                      )}
                    </div>
                  </CardFooter>

                  <div className="absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-r from-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none -z-10" />
                </Card>
              );
            })}
          </div>

          <Paginator
            currentPage={events?.paginationDetails?.currentPage}
            totalPages={events?.paginationDetails?.totalPages}
          />
        </>
      )}
    </div>
  );
}
