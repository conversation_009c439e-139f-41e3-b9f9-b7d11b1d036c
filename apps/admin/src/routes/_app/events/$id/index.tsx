import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import {
  eventClient,
  eventParticipantClient,
  handleConnectError,
} from "@vtuber/services/client";
import { EventService } from "@vtuber/services/events";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import { Image } from "@vtuber/ui/components/image";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { Calendar, CalendarX, Check, X } from "lucide-react";
import { toast } from "sonner";
import { z } from "zod";
import { EventTabs } from "~/components/event/EventTab";

const SearchSchema = z.object({
  page: z.number().optional(),
  size: z.number().optional(),
});

export const Route = createFileRoute("/_app/events/$id/")({
  component: RouteComponent,
  validateSearch: SearchSchema,
  loader: async ({ params, location }) => {
    const [event] = await eventClient.getEventById({
      id: params.id,
    });
    const search = SearchSchema.parse(location.search);
    const [eventParticipants] =
      await eventParticipantClient.getAllEventParticipantsByEventId({
        eventId: params.id,
        pagination: {
          page: search.page,
          size: search.size,
        },
      });
    return { event, eventParticipants };
  },
});

function RouteComponent() {
  const { event, eventParticipants } = Route.useLoaderData();
  const router = useRouter();
  const { getText } = useLanguage();
  const approveMutation = useMutation(
    EventService.method.approveOrRejectEvent,
    {
      onError: (err) => {
        handleConnectError(err);
      },
      onSuccess: () => {
        toast.success("Event status changed successfully");
        router.invalidate();
      },
    },
  );

  const handleApprove = (id: string) => {
    const confirm = window.confirm(
      "Are you sure you want to approve this event?",
    );
    if (confirm) {
      approveMutation.mutateAsync({
        id,
        status: "approved",
      });
    }
  };

  const handleReject = (id: string) => {
    const confirm = window.confirm(
      "Are you sure you want to reject this event?",
    );
    if (confirm) {
      approveMutation.mutateAsync({
        id,
        status: "rejected",
      });
    }
  };

  if (!event?.data) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center p-8">
          <h2 className="text-2xl font-semibold text-gray-700 mb-2">
            Event Not Found
          </h2>
          <p className="text-gray-500">
            The event you're looking for doesn't exist or has been removed.
          </p>
        </div>
      </div>
    );
  }

  if (!eventParticipants?.data) return null;

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="relative overflow-hidden">
        {event.data.image && (
          <AspectRatio ratio={200 / 100}>
            <Image
              src={event.data.image}
              alt={event.data.title}
              className="w-full h-full object-cover"
            />
          </AspectRatio>
        )}
      </div>
      <div className="relative -mt-16 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="rounded-2xl shadow-xl border bg-gradient-3 p-6 sm:p-8 mb-8">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
              <div className="flex-1">
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-8 mb-6">
                  <div className="flex items-center gap-3 flex-1">
                    <Avatar
                      src={getCdnUrl(
                        event.data.user?.image || event.data.vtuber?.image,
                      )}
                      fallback={(
                        event.data.user?.name ||
                        event.data.vtuber?.name ||
                        "E"
                      ).substring(0, 2)}
                    />
                    <div className="flex-1">
                      <h1 className="text-3xl sm:text-4xl font-bold capitalize leading-tight mb-2">
                        {event.data.title}
                      </h1>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                        <p className="text-lg">
                          {getText("created_by")}
                          {":"}
                          <span className="font-semibold">
                            {event.data.user?.name ||
                              event.data.vtuber?.name ||
                              "Unknown"}
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col gap-3 lg:min-w-[300px]">
                    <div className="flex items-center gap-2 p-3 rounded-lg ">
                      <Calendar className="h-5 w-5 text-green-600" />
                      <p className="text-sm font-medium  uppercase tracking-wide">
                        {getText("start_date")} :
                      </p>
                      <p className="text-lg font-semibold">
                        {event.data.startDate
                          ? timestampDate(
                              event.data.startDate,
                            ).toLocaleDateString()
                          : "N/A"}
                      </p>
                    </div>

                    <div className="flex items-center gap-2 p-3 rounded-lg ">
                      <CalendarX className="h-5 w-5 text-red-600" />
                      <p className="text-sm font-medium  uppercase tracking-wide">
                        {getText("end_date")} :
                      </p>
                      <p className="text-lg font-semibold">
                        {event.data.endDate
                          ? timestampDate(
                              event.data.endDate,
                            ).toLocaleDateString()
                          : "N/A"}
                      </p>
                    </div>
                  </div>
                </div>
                <p className="mb-4">{event.data.shortDescription}</p>

                <div className="flex flex-wrap items-center gap-3">
                  <Badge
                    variant={
                      event.data.status === "approved"
                        ? "success"
                        : event.data.status === "rejected"
                          ? "destructive"
                          : "outline"
                    }
                    className="text-sm capitalize">
                    {event.data.status}
                  </Badge>
                  {event.data.status === "pending" && (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-green-500 text-green-600 hover:bg-green-50 hover:text-green-700"
                        onClick={() => handleApprove(event.data?.id!)}>
                        <Check className="mr-1 h-4 w-4" />
                        {getText("Approve")}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-red-500 text-red-600 hover:bg-red-50 hover:text-red-700"
                        onClick={() => handleReject(event.data?.id!)}>
                        <X className="mr-1 h-4 w-4" />
                        {getText("Reject")}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <EventTabs
            event={event.data}
            eventParticipants={eventParticipants}
          />
        </div>
      </div>
    </div>
  );
}

export default RouteComponent;
