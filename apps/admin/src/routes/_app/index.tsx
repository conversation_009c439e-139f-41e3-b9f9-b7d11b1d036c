import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { usersClient } from "@vtuber/services/client";
import { Paginator } from "@vtuber/ui/components/paginator";
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { z } from "zod";
import { UserCard } from "~/components/user/UserCard";

const SearchSchema = z.object({
  page: z.number().optional(),
  size: z.number().optional(),
});

export const Route = createFileRoute("/_app/")({
  validateSearch: SearchSchema,
  component: RouteComponent,
  loader: async ({ location }) => {
    const search = SearchSchema.parse(location.search);
    const [users] = await usersClient.getAllUsers({
      pagination: {
        page: search.page,
      },
    });
    const [bannedUsers] = await usersClient.getAllBannedUsers({
      pagination: {
        page: search.page,
      },
    });
    return { users, bannedUsers };
  },
});

function RouteComponent() {
  const { users, bannedUsers } = Route.useLoaderData();
  const { getText } = useLanguage();
  return (
    <div className="p-2 md:p-4">
      <Tabs
        defaultValue="users"
        className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="users">{getText("User")}</TabsTrigger>
          <TabsTrigger value="bannedUsers">
            {getText("Banned_User")}
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="users"
          className="mt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {users?.data.map((user) => (
              <UserCard
                key={user.id.toString()}
                user={user}
                showBanButton={true}
              />
            ))}
          </div>
          <Paginator
            currentPage={users?.paginationDetails?.currentPage}
            totalPages={users?.paginationDetails?.totalPages}
          />
        </TabsContent>

        <TabsContent
          value="bannedUsers"
          className="mt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {bannedUsers?.data.map((user) => (
              <UserCard
                key={user.id.toString()}
                user={user}
                showBanButton={false}
              />
            ))}
          </div>
          <Paginator
            currentPage={bannedUsers?.paginationDetails?.currentPage}
            totalPages={bannedUsers?.paginationDetails?.totalPages}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
