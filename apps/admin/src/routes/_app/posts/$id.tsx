import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { postsClient } from "@vtuber/services/client";

import { Badge } from "@vtuber/ui/components/badge";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Media } from "@vtuber/ui/components/media";
import { Heart, MessageCircle } from "lucide-react";

export const Route = createFileRoute("/_app/posts/$id")({
  component: RouteComponent,
  loader: async ({ params }) => {
    const [post] = await postsClient.getPostById({
      id: String(params.id).toString(),
    });
    return { post };
  },
});

function RouteComponent() {
  const { post } = Route.useLoaderData();

  const { getText } = useLanguage();
  if (!post?.data)
    return <div className="text-center py-20">{getText("post_not_found")}</div>;

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="relative rounded-3xl overflow-hidden shadow-2xl mb-12 group">
        <Media
          src={post.data.media}
          type={post.data.mediaType}
        />
        <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-wrap items-center gap-4 mb-4">
              {post.data.membershipOnly && (
                <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white">
                  {getText("Membership_Only")}
                </Badge>
              )}
              <div className="flex items-center bg-gradient-3 backdrop-blur-sm px-3 py-1 rounded-full">
                <span className="text-sm">{post.data.name}</span>
              </div>
            </div>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 drop-shadow-xl">
              {post.data.title}
            </h1>
            <p className="text-lg md:text-xl opacity-90 max-w-3xl">
              {post.data.shortDescription}
            </p>
          </div>
        </div>
      </div>
      <Card className="border-none shadow-xl rounded-2xl overflow-hidden">
        <CardContent className="p-8">
          <div className="">
            <MarkDown markdown={post.data.description} />
            <div className="flex items-center gap-8 my-10">
              <div className="flex items-center gap-2">
                <Heart className="w-6 h-6 text-red-800" />
                <div>
                  <div className="font-bold text-lg">
                    <p>{post.data.postLikes}</p>
                  </div>
                  <div className="text-sm text-gray-500">{getText("like")}</div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <MessageCircle className="w-6 h-6 text-green-500" />
                <div>
                  <div className="font-bold text-lg">
                    <p>{post.data.postComments}</p>
                  </div>
                  <div className="text-sm text-gray-500">
                    {getText("comments")}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* <div className="mt-16">
        <h2 className="text-3xl font-bold mb-8">
          {getText("comments")} ({post.data.postComments})
        </h2>
      </div> */}
    </div>
  );
}
