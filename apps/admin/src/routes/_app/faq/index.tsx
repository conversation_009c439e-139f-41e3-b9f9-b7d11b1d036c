import { createFile<PERSON>out<PERSON>, <PERSON>, useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { faqClient } from "@vtuber/services/client";
import { buttonVariants } from "@vtuber/ui/components/button";
import { DataTable } from "@vtuber/ui/components/data-table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@vtuber/ui/components/select";
import { ENGLISH_KEY, JAPANESE_KEY } from "@vtuber/ui/constants";
import { Plus } from "lucide-react";
import { useState } from "react";
import { z } from "zod";
import { faqColumns } from "~/components/faq/faq-columns";

const SearchSchema = z.object({
  page: z.number().optional(),
  size: z.number().optional(),
  language: z.string().optional(),
});

export const Route = createFileRoute("/_app/faq/")({
  component: RouteComponent,
  validateSearch: SearchSchema,
  loader: async ({ location }) => {
    const search = SearchSchema.parse(location.search);
    const [faqs] = await faqClient.getAllFaqs({
      language: search.language,
    });
    return faqs;
  },
});

function RouteComponent() {
  const faqs = Route.useLoaderData();
  const { language } = Route.useSearch();
  const { getText } = useLanguage();
  const navigate = useNavigate({ from: Route.fullPath });
  const [lan, setLan] = useState<string | undefined>(language || "all");

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-xl font-semibold">FAQ'S</h1>
        <div className="flex items-center gap-3">
          <Select
            defaultValue={lan}
            onValueChange={(l) => {
              if (l === "all") {
                setLan(undefined);
                navigate({
                  to: "/faq",
                  search: {
                    language: undefined,
                  },
                });
              } else {
                setLan(l);
                navigate({
                  to: "/faq",
                  search: {
                    language: l,
                  },
                });
              }
            }}>
            <SelectTrigger size={"lg"}>
              <SelectValue placeholder={getText("Select_Language")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={"all"}>{getText("all")}</SelectItem>
              <SelectItem value={ENGLISH_KEY}>English</SelectItem>
              <SelectItem value={JAPANESE_KEY}>日本語</SelectItem>
            </SelectContent>
          </Select>
          <Link
            to="/faq/add"
            className={buttonVariants({
              variant: "success",
              size: "xl",
            })}>
            <Plus /> {getText("Add")}
          </Link>
        </div>
      </div>
      <DataTable
        columns={
          !!language
            ? faqColumns.filter((c) => c.header !== "Language")
            : faqColumns
        }
        data={faqs?.data || []}
      />
    </div>
  );
}
