import { createFileRoute, Navigate, Outlet } from "@tanstack/react-router";
import { useAuth, useLogout } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { AppSidebar, AppSidebarLinks } from "@vtuber/ui/components/app-sidebar";
import { Button } from "@vtuber/ui/components/button";
import { HeaderBreadCrumb } from "@vtuber/ui/components/header-bread-crumb";
import { Separator } from "@vtuber/ui/components/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@vtuber/ui/components/sidebar";
import {
  Edit3,
  FolderOpen,
  Folders,
  HelpCircle,
  Megaphone,
  Tags,
  UserPlus,
  Users2,
  Videotape,
} from "lucide-react";
import { staticResources } from "~/utils/constants";

export const Route = createFileRoute("/_app")({
  component: RouteComponent,
});

function RouteComponent() {
  const { getText } = useLanguage();
  const { session, isAdmin } = useAuth();
  const logout = useLogout();

  const links: AppSidebarLinks[] = [
    {
      title: "Users",
      icon: Users2,
      url: "/",
    },
    {
      title: "Categories",
      icon: Folders,
      url: "/categories",
    },
    {
      title: "vtuber_categories",
      icon: Tags,
      url: "/vtuber-categories",
    },
    {
      title: "CAMPAIGNS",
      icon: FolderOpen,
      url: "/campaigns",
    },
    {
      title: "Events",
      icon: Videotape,
      url: "/events",
    },
    {
      title: "Craetor_Request",
      icon: UserPlus,
      url: "/creator-requests",
    },
    {
      title: "FAQ",
      icon: Edit3,
      url: "/faq",
    },
    {
      title: "announcement",
      icon: Megaphone,
      url: "/announcements",
    },
    ...staticResources,
    {
      title: "user_guides",
      icon: HelpCircle,
      url: "/user-guides",
    },
  ];

  if (!session) {
    return <Navigate to="/login" />;
  }

  if (!isAdmin) {
    return (
      <div className="w-full h-screen flex justify-center items-center space-y-4 flex-col">
        <div className="text-primary text-2xl font-bold">
          UNAUTHORISED ACCESS
        </div>
        <div>
          <Button onClick={() => logout()}>Log out</Button>
        </div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar
        links={links}
        logout={logout}
        session={session}
      />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 h-4"
            />
            <HeaderBreadCrumb homeText={getText("Users")} />
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <Outlet />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
