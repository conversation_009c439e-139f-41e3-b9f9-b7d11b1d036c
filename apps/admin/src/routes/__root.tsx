import { TransportProvider } from "@connectrpc/connect-query";
import type { QueryClient } from "@tanstack/react-query";
import {
  createRootRouteWithContext,
  HeadContent,
  Outlet,
  Scripts,
} from "@tanstack/react-router";
import {
  AuthProvider,
  CategoriesProvider,
  userPromise,
} from "@vtuber/auth/provider";
import { LanguageProvider } from "@vtuber/language/provider";
import { authClient, categoryClient, transport } from "@vtuber/services/client";
import { DefaultCatchBoundary } from "@vtuber/ui/components/default-catch-boundry";
import css from "@vtuber/ui/globals.css?url";
import * as React from "react";
import { Toaster } from "sonner";
import { seo } from "~/utils/seo";

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient;
}>()({
  head: () => ({
    meta: [
      {
        charSet: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      ...seo({
        title: "V-Sai | Admin",
        description: "Admin Panel for V-Sai",
      }),
    ],
    links: [
      { rel: "stylesheet", href: css },
      {
        rel: "apple-touch-icon",
        sizes: "180x180",
        href: "/apple-touch-icon.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "32x32",
        href: "/favicon-32x32.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "16x16",
        href: "/favicon-16x16.png",
      },
      { rel: "manifest", href: "/site.webmanifest", color: "#fffff" },
      { rel: "icon", href: "/favicon.ico" },
    ],
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    );
  },

  component: RootComponent,
  loader: async () => {
    const [user] = await authClient.getSession({});
    userPromise.resolve(user);
    const [categories] = await categoryClient.getAllCategories({});
    const language = getCookie("language");
    return {
      categories,
      user,
      language: (["en", "ja"].includes(language) ? language : "en") as
        | "en"
        | "ja",
    };
  },
  beforeLoad: async () => {
    return {
      user: userPromise.promise,
    };
  },
  shouldReload: false,
});

function RootComponent() {
  const { user, language, categories } = Route.useLoaderData();

  return (
    <RootDocument>
      <TransportProvider transport={transport}>
        <AuthProvider user={user}>
          <LanguageProvider defaultLanguage={language}>
            <CategoriesProvider categories={categories?.categories || null}>
              <Toaster
                richColors
                position="top-center"
              />
              <Outlet />
            </CategoriesProvider>
          </LanguageProvider>
        </AuthProvider>
      </TransportProvider>
    </RootDocument>
  );
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html className="light">
      <head>
        <HeadContent />
      </head>
      <body>
        {children}
        <Scripts />
      </body>
    </html>
  );
}
