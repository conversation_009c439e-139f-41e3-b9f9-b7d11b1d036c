import { useMutation } from "@tanstack/react-query";
import { createFileRoute, <PERSON>, useRouter } from "@tanstack/react-router";
import {
  authClient,
  handleConnectError,
  NoAuthContextValues,
} from "@vtuber/services/client";
import { SendForgotPasswordEmailRequest } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/form/text-input";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
export const Route = createFileRoute("/_auth/forgot-password")({
  component: RouteComponent,
});
function RouteComponent() {
  const { navigate } = useRouter();

  const form = useForm<SendForgotPasswordEmailRequest>({
    defaultValues: {
      email: "",
    },
  });

  const forgotPassword = useMutation({
    mutationFn: async (data: SendForgotPasswordEmailRequest) => {
      return authClient.sendForgotPasswordEmail(data, {
        contextValues: NoAuthContextValues(),
      });
    },

    onSuccess: ([res, err]) => {
      if (res) {
        toast.success(
          "Password reset email sent. Check your email to reset your password",
        );
        navigate({ to: "/reset-password" });
      } else if (err) {
        handleConnectError(err, form);
      }
    },
  });

  const onSubmit = form.handleSubmit((data) => forgotPassword.mutate(data));
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-xl">Forgot Password</CardTitle>
        <CardDescription>
          Enter your email to reset your password
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={onSubmit}>
            <div className="grid gap-6">
              <div className="grid gap-6">
                <TextInput
                  control={form.control}
                  name="email"
                  label="Email"
                  placeholder="Enter your email"
                />
                <Button
                  loading={forgotPassword.isPending}
                  type="submit"
                  className="w-full">
                  Send Password Reset Email
                </Button>
              </div>
              <div className="text-center text-sm">
                Already received a code?{" "}
                <Link
                  to="/reset-password"
                  className="underline underline-offset-4">
                  Reset
                </Link>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
