import { createFile<PERSON>out<PERSON>, <PERSON> } from "@tanstack/react-router";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";

import { useMutation } from "@tanstack/react-query";
import {
  JWT_COOKIE_NAME,
  JWT_REFRESH_COOKIE_NAME,
} from "@vtuber/cookie/constants";
import {
  authClient,
  handleConnectError,
  NoAuthContextValues,
} from "@vtuber/services/client";
import { SignInWithEmailRequest } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/form/text-input";
import { useForm } from "react-hook-form";

export const Route = createFileRoute("/_auth/login")({
  component: RouteComponent,
});

function RouteComponent() {
  const form = useForm<SignInWithEmailRequest>({
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const login = useMutation({
    mutationFn: (data: SignInWithEmailRequest) => {
      return authClient.signInWithEmail(data, {
        contextValues: NoAuthContextValues(),
      });
    },

    onSuccess: ([res, err]) => {
      if (res) {
        setCookie(JWT_COOKIE_NAME, res.accessToken);
        setCookie(JWT_REFRESH_COOKIE_NAME, res.refreshToken);
      } else if (err) {
        handleConnectError(err, form);
      }
    },
  });

  const onSubmit = form.handleSubmit((data) => login.mutate(data));

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-xl">Welcome back</CardTitle>
        <CardDescription>Sign in to your account to continue</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={onSubmit}>
            <div className="grid gap-6">
              <div className="grid gap-6">
                <TextInput
                  control={form.control}
                  name="email"
                  label="Email"
                  placeholder="Enter your email"
                />

                <TextInput
                  control={form.control}
                  labelright={
                    <Link
                      className="text-sm text-primary underline"
                      to="/forgot-password">
                      Forgot password ?
                    </Link>
                  }
                  name="password"
                  label="Password"
                  type="password"
                  placeholder="Enter your password"
                />

                <Button
                  loading={login.isPending}
                  type="submit"
                  className="w-full">
                  Login
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
