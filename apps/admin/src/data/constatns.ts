import { Variants } from "motion/react";
import { z } from "zod";

export const validatePagination = z.object({
  page: z.number().optional(),
  order: z.string().optional(),
  size: z.number().optional(),
  sort: z.string().optional(),
});

export const floatingVariants: Variants = {
  initial: { y: 0 },
  animate: {
    y: [0, -10, 0],
    transition: {
      duration: 3,
      repeat: Number.POSITIVE_INFINITY,
      ease: "easeInOut",
    },
  },
};
