{"include": ["**/*.ts", "**/*.tsx", "index.d.ts"], "compilerOptions": {"strict": true, "esModuleInterop": true, "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "lib": ["DOM", "DOM.Iterable", "ES2024"], "isolatedModules": true, "resolveJsonModule": true, "skipLibCheck": true, "target": "ES2022", "allowJs": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"~/*": ["./src/*"]}, "noEmit": true}}