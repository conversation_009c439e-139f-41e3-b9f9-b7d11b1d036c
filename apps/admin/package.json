{"name": "@vtuber/admin", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev --port 3002", "build": "tsc --noEmit && vite build", "start": "node .output/server/index.mjs", "ts-check": "tsc --noEmit"}, "dependencies": {"@bufbuild/protobuf": "^2.5.2", "@connectrpc/connect": "^2.0.2", "@connectrpc/connect-query": "latest", "@tanstack/react-query": "latest", "@tanstack/react-router": "latest", "@tanstack/react-router-with-query": "^1.121.34", "@tanstack/react-start": "latest", "@vtuber/auth": "workspace:*", "@vtuber/cookie": "workspace:*", "@vtuber/language": "workspace:*", "@vtuber/services": "workspace:*", "@vtuber/ui": "workspace:*", "dotenv": "^16.5.0", "lucide-react": "0.522.0", "motion": "^12.23.12", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "recharts": "^2.15.1", "sonner": "^2.0.5", "vite": "^6.3.5", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.14", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4"}}