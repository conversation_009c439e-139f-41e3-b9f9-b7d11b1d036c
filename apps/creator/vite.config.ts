/// <reference types="vite/client" />

import { tanstackStart } from "@tanstack/react-start/plugin/vite";
import "dotenv/config";
import { defineConfig } from "vite";
import tsConfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  esbuild: {
    target: "esnext",
  },
  plugins: [
    tsConfigPaths({
      projects: ["./tsconfig.json"],
    }),
    tanstackStart({
      target: "vercel",
    }),
  ],
  define: {
    "process.env": {
      NODE_ENV: process.env.NODE_ENV,
      BASE_URL: process.env.BASE_URL,
      CDN_URL: process.env.CDN_URL,
      SOCKET_URL: process.env.SOCKET_URL,
    },
  },
});
