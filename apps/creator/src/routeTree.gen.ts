/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as SearchRouteImport } from './routes/search'
import { Route as AuthRouteImport } from './routes/_auth'
import { Route as AppRouteImport } from './routes/_app'
import { Route as AppIndexRouteImport } from './routes/_app/index'
import { Route as AuthWelcomeRouteImport } from './routes/_auth/welcome'
import { Route as AuthVerifyemailRouteImport } from './routes/_auth/verifyemail'
import { Route as AuthResetPasswordRouteImport } from './routes/_auth/reset-password'
import { Route as AuthRegisterRouteImport } from './routes/_auth/register'
import { Route as AuthLoginRouteImport } from './routes/_auth/login'
import { Route as AuthForgotPasswordRouteImport } from './routes/_auth/forgot-password'
import { Route as AuthChangePasswordRouteImport } from './routes/_auth/change-password'
import { Route as AppTermsOfServiceRouteImport } from './routes/_app/terms-of-service'
import { Route as AppProfileRouteImport } from './routes/_app/profile'
import { Route as AppPrivacyPolicyRouteImport } from './routes/_app/privacy-policy'
import { Route as AppNotificationsRouteImport } from './routes/_app/notifications'
import { Route as AppAccountRouteImport } from './routes/_app/account'
import { Route as AppPostsIndexRouteImport } from './routes/_app/posts/index'
import { Route as AppPlansIndexRouteImport } from './routes/_app/plans/index'
import { Route as AppEventIndexRouteImport } from './routes/_app/event/index'
import { Route as AppCampaignsIndexRouteImport } from './routes/_app/campaigns/index'
import { Route as ProtectedEmailVerifyRouteImport } from './routes/_protected/email/verify'
import { Route as AppPostsAddRouteImport } from './routes/_app/posts/add'
import { Route as AppPlansAddRouteImport } from './routes/_app/plans/add'
import { Route as AppEventAddRouteImport } from './routes/_app/event/add'
import { Route as AppEmailVerifyNewEmailRouteImport } from './routes/_app/email/verify-new-email'
import { Route as AppEmailSuccessRouteImport } from './routes/_app/email/success'
import { Route as AppEmailNewEmailRouteImport } from './routes/_app/email/new-email'
import { Route as AppCampaignsAddRouteImport } from './routes/_app/campaigns/add'
import { Route as AppPostsIdIndexRouteImport } from './routes/_app/posts/$id/index'
import { Route as AppPlansIdIndexRouteImport } from './routes/_app/plans/$id/index'
import { Route as AppEventIdIndexRouteImport } from './routes/_app/event/$id/index'
import { Route as AppCampaignsIdIndexRouteImport } from './routes/_app/campaigns/$id/index'
import { Route as AppPostsIdEditRouteImport } from './routes/_app/posts/$id/edit'
import { Route as AppPlansIdEditRouteImport } from './routes/_app/plans/$id/edit'
import { Route as AppEventIdEditRouteImport } from './routes/_app/event/$id/edit'
import { Route as AppCampaignsVariantVariantIdRouteImport } from './routes/_app/campaigns/variant/$variantId'
import { Route as AppCampaignsIdEditRouteImport } from './routes/_app/campaigns/$id/edit'

const SearchRoute = SearchRouteImport.update({
  id: '/search',
  path: '/search',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRoute = AuthRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any)
const AppRoute = AppRouteImport.update({
  id: '/_app',
  getParentRoute: () => rootRouteImport,
} as any)
const AppIndexRoute = AppIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AppRoute,
} as any)
const AuthWelcomeRoute = AuthWelcomeRouteImport.update({
  id: '/welcome',
  path: '/welcome',
  getParentRoute: () => AuthRoute,
} as any)
const AuthVerifyemailRoute = AuthVerifyemailRouteImport.update({
  id: '/verifyemail',
  path: '/verifyemail',
  getParentRoute: () => AuthRoute,
} as any)
const AuthResetPasswordRoute = AuthResetPasswordRouteImport.update({
  id: '/reset-password',
  path: '/reset-password',
  getParentRoute: () => AuthRoute,
} as any)
const AuthRegisterRoute = AuthRegisterRouteImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => AuthRoute,
} as any)
const AuthLoginRoute = AuthLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRoute,
} as any)
const AuthForgotPasswordRoute = AuthForgotPasswordRouteImport.update({
  id: '/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => AuthRoute,
} as any)
const AuthChangePasswordRoute = AuthChangePasswordRouteImport.update({
  id: '/change-password',
  path: '/change-password',
  getParentRoute: () => AuthRoute,
} as any)
const AppTermsOfServiceRoute = AppTermsOfServiceRouteImport.update({
  id: '/terms-of-service',
  path: '/terms-of-service',
  getParentRoute: () => AppRoute,
} as any)
const AppProfileRoute = AppProfileRouteImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => AppRoute,
} as any)
const AppPrivacyPolicyRoute = AppPrivacyPolicyRouteImport.update({
  id: '/privacy-policy',
  path: '/privacy-policy',
  getParentRoute: () => AppRoute,
} as any)
const AppNotificationsRoute = AppNotificationsRouteImport.update({
  id: '/notifications',
  path: '/notifications',
  getParentRoute: () => AppRoute,
} as any)
const AppAccountRoute = AppAccountRouteImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => AppRoute,
} as any)
const AppPostsIndexRoute = AppPostsIndexRouteImport.update({
  id: '/posts/',
  path: '/posts/',
  getParentRoute: () => AppRoute,
} as any)
const AppPlansIndexRoute = AppPlansIndexRouteImport.update({
  id: '/plans/',
  path: '/plans/',
  getParentRoute: () => AppRoute,
} as any)
const AppEventIndexRoute = AppEventIndexRouteImport.update({
  id: '/event/',
  path: '/event/',
  getParentRoute: () => AppRoute,
} as any)
const AppCampaignsIndexRoute = AppCampaignsIndexRouteImport.update({
  id: '/campaigns/',
  path: '/campaigns/',
  getParentRoute: () => AppRoute,
} as any)
const ProtectedEmailVerifyRoute = ProtectedEmailVerifyRouteImport.update({
  id: '/_protected/email/verify',
  path: '/email/verify',
  getParentRoute: () => rootRouteImport,
} as any)
const AppPostsAddRoute = AppPostsAddRouteImport.update({
  id: '/posts/add',
  path: '/posts/add',
  getParentRoute: () => AppRoute,
} as any)
const AppPlansAddRoute = AppPlansAddRouteImport.update({
  id: '/plans/add',
  path: '/plans/add',
  getParentRoute: () => AppRoute,
} as any)
const AppEventAddRoute = AppEventAddRouteImport.update({
  id: '/event/add',
  path: '/event/add',
  getParentRoute: () => AppRoute,
} as any)
const AppEmailVerifyNewEmailRoute = AppEmailVerifyNewEmailRouteImport.update({
  id: '/email/verify-new-email',
  path: '/email/verify-new-email',
  getParentRoute: () => AppRoute,
} as any)
const AppEmailSuccessRoute = AppEmailSuccessRouteImport.update({
  id: '/email/success',
  path: '/email/success',
  getParentRoute: () => AppRoute,
} as any)
const AppEmailNewEmailRoute = AppEmailNewEmailRouteImport.update({
  id: '/email/new-email',
  path: '/email/new-email',
  getParentRoute: () => AppRoute,
} as any)
const AppCampaignsAddRoute = AppCampaignsAddRouteImport.update({
  id: '/campaigns/add',
  path: '/campaigns/add',
  getParentRoute: () => AppRoute,
} as any)
const AppPostsIdIndexRoute = AppPostsIdIndexRouteImport.update({
  id: '/posts/$id/',
  path: '/posts/$id/',
  getParentRoute: () => AppRoute,
} as any)
const AppPlansIdIndexRoute = AppPlansIdIndexRouteImport.update({
  id: '/plans/$id/',
  path: '/plans/$id/',
  getParentRoute: () => AppRoute,
} as any)
const AppEventIdIndexRoute = AppEventIdIndexRouteImport.update({
  id: '/event/$id/',
  path: '/event/$id/',
  getParentRoute: () => AppRoute,
} as any)
const AppCampaignsIdIndexRoute = AppCampaignsIdIndexRouteImport.update({
  id: '/campaigns/$id/',
  path: '/campaigns/$id/',
  getParentRoute: () => AppRoute,
} as any)
const AppPostsIdEditRoute = AppPostsIdEditRouteImport.update({
  id: '/posts/$id/edit',
  path: '/posts/$id/edit',
  getParentRoute: () => AppRoute,
} as any)
const AppPlansIdEditRoute = AppPlansIdEditRouteImport.update({
  id: '/plans/$id/edit',
  path: '/plans/$id/edit',
  getParentRoute: () => AppRoute,
} as any)
const AppEventIdEditRoute = AppEventIdEditRouteImport.update({
  id: '/event/$id/edit',
  path: '/event/$id/edit',
  getParentRoute: () => AppRoute,
} as any)
const AppCampaignsVariantVariantIdRoute =
  AppCampaignsVariantVariantIdRouteImport.update({
    id: '/campaigns/variant/$variantId',
    path: '/campaigns/variant/$variantId',
    getParentRoute: () => AppRoute,
  } as any)
const AppCampaignsIdEditRoute = AppCampaignsIdEditRouteImport.update({
  id: '/campaigns/$id/edit',
  path: '/campaigns/$id/edit',
  getParentRoute: () => AppRoute,
} as any)

export interface FileRoutesByFullPath {
  '/search': typeof SearchRoute
  '/account': typeof AppAccountRoute
  '/notifications': typeof AppNotificationsRoute
  '/privacy-policy': typeof AppPrivacyPolicyRoute
  '/profile': typeof AppProfileRoute
  '/terms-of-service': typeof AppTermsOfServiceRoute
  '/change-password': typeof AuthChangePasswordRoute
  '/forgot-password': typeof AuthForgotPasswordRoute
  '/login': typeof AuthLoginRoute
  '/register': typeof AuthRegisterRoute
  '/reset-password': typeof AuthResetPasswordRoute
  '/verifyemail': typeof AuthVerifyemailRoute
  '/welcome': typeof AuthWelcomeRoute
  '/': typeof AppIndexRoute
  '/campaigns/add': typeof AppCampaignsAddRoute
  '/email/new-email': typeof AppEmailNewEmailRoute
  '/email/success': typeof AppEmailSuccessRoute
  '/email/verify-new-email': typeof AppEmailVerifyNewEmailRoute
  '/event/add': typeof AppEventAddRoute
  '/plans/add': typeof AppPlansAddRoute
  '/posts/add': typeof AppPostsAddRoute
  '/email/verify': typeof ProtectedEmailVerifyRoute
  '/campaigns': typeof AppCampaignsIndexRoute
  '/event': typeof AppEventIndexRoute
  '/plans': typeof AppPlansIndexRoute
  '/posts': typeof AppPostsIndexRoute
  '/campaigns/$id/edit': typeof AppCampaignsIdEditRoute
  '/campaigns/variant/$variantId': typeof AppCampaignsVariantVariantIdRoute
  '/event/$id/edit': typeof AppEventIdEditRoute
  '/plans/$id/edit': typeof AppPlansIdEditRoute
  '/posts/$id/edit': typeof AppPostsIdEditRoute
  '/campaigns/$id': typeof AppCampaignsIdIndexRoute
  '/event/$id': typeof AppEventIdIndexRoute
  '/plans/$id': typeof AppPlansIdIndexRoute
  '/posts/$id': typeof AppPostsIdIndexRoute
}
export interface FileRoutesByTo {
  '/search': typeof SearchRoute
  '/account': typeof AppAccountRoute
  '/notifications': typeof AppNotificationsRoute
  '/privacy-policy': typeof AppPrivacyPolicyRoute
  '/profile': typeof AppProfileRoute
  '/terms-of-service': typeof AppTermsOfServiceRoute
  '/change-password': typeof AuthChangePasswordRoute
  '/forgot-password': typeof AuthForgotPasswordRoute
  '/login': typeof AuthLoginRoute
  '/register': typeof AuthRegisterRoute
  '/reset-password': typeof AuthResetPasswordRoute
  '/verifyemail': typeof AuthVerifyemailRoute
  '/welcome': typeof AuthWelcomeRoute
  '/': typeof AppIndexRoute
  '/campaigns/add': typeof AppCampaignsAddRoute
  '/email/new-email': typeof AppEmailNewEmailRoute
  '/email/success': typeof AppEmailSuccessRoute
  '/email/verify-new-email': typeof AppEmailVerifyNewEmailRoute
  '/event/add': typeof AppEventAddRoute
  '/plans/add': typeof AppPlansAddRoute
  '/posts/add': typeof AppPostsAddRoute
  '/email/verify': typeof ProtectedEmailVerifyRoute
  '/campaigns': typeof AppCampaignsIndexRoute
  '/event': typeof AppEventIndexRoute
  '/plans': typeof AppPlansIndexRoute
  '/posts': typeof AppPostsIndexRoute
  '/campaigns/$id/edit': typeof AppCampaignsIdEditRoute
  '/campaigns/variant/$variantId': typeof AppCampaignsVariantVariantIdRoute
  '/event/$id/edit': typeof AppEventIdEditRoute
  '/plans/$id/edit': typeof AppPlansIdEditRoute
  '/posts/$id/edit': typeof AppPostsIdEditRoute
  '/campaigns/$id': typeof AppCampaignsIdIndexRoute
  '/event/$id': typeof AppEventIdIndexRoute
  '/plans/$id': typeof AppPlansIdIndexRoute
  '/posts/$id': typeof AppPostsIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_app': typeof AppRouteWithChildren
  '/_auth': typeof AuthRouteWithChildren
  '/search': typeof SearchRoute
  '/_app/account': typeof AppAccountRoute
  '/_app/notifications': typeof AppNotificationsRoute
  '/_app/privacy-policy': typeof AppPrivacyPolicyRoute
  '/_app/profile': typeof AppProfileRoute
  '/_app/terms-of-service': typeof AppTermsOfServiceRoute
  '/_auth/change-password': typeof AuthChangePasswordRoute
  '/_auth/forgot-password': typeof AuthForgotPasswordRoute
  '/_auth/login': typeof AuthLoginRoute
  '/_auth/register': typeof AuthRegisterRoute
  '/_auth/reset-password': typeof AuthResetPasswordRoute
  '/_auth/verifyemail': typeof AuthVerifyemailRoute
  '/_auth/welcome': typeof AuthWelcomeRoute
  '/_app/': typeof AppIndexRoute
  '/_app/campaigns/add': typeof AppCampaignsAddRoute
  '/_app/email/new-email': typeof AppEmailNewEmailRoute
  '/_app/email/success': typeof AppEmailSuccessRoute
  '/_app/email/verify-new-email': typeof AppEmailVerifyNewEmailRoute
  '/_app/event/add': typeof AppEventAddRoute
  '/_app/plans/add': typeof AppPlansAddRoute
  '/_app/posts/add': typeof AppPostsAddRoute
  '/_protected/email/verify': typeof ProtectedEmailVerifyRoute
  '/_app/campaigns/': typeof AppCampaignsIndexRoute
  '/_app/event/': typeof AppEventIndexRoute
  '/_app/plans/': typeof AppPlansIndexRoute
  '/_app/posts/': typeof AppPostsIndexRoute
  '/_app/campaigns/$id/edit': typeof AppCampaignsIdEditRoute
  '/_app/campaigns/variant/$variantId': typeof AppCampaignsVariantVariantIdRoute
  '/_app/event/$id/edit': typeof AppEventIdEditRoute
  '/_app/plans/$id/edit': typeof AppPlansIdEditRoute
  '/_app/posts/$id/edit': typeof AppPostsIdEditRoute
  '/_app/campaigns/$id/': typeof AppCampaignsIdIndexRoute
  '/_app/event/$id/': typeof AppEventIdIndexRoute
  '/_app/plans/$id/': typeof AppPlansIdIndexRoute
  '/_app/posts/$id/': typeof AppPostsIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/search'
    | '/account'
    | '/notifications'
    | '/privacy-policy'
    | '/profile'
    | '/terms-of-service'
    | '/change-password'
    | '/forgot-password'
    | '/login'
    | '/register'
    | '/reset-password'
    | '/verifyemail'
    | '/welcome'
    | '/'
    | '/campaigns/add'
    | '/email/new-email'
    | '/email/success'
    | '/email/verify-new-email'
    | '/event/add'
    | '/plans/add'
    | '/posts/add'
    | '/email/verify'
    | '/campaigns'
    | '/event'
    | '/plans'
    | '/posts'
    | '/campaigns/$id/edit'
    | '/campaigns/variant/$variantId'
    | '/event/$id/edit'
    | '/plans/$id/edit'
    | '/posts/$id/edit'
    | '/campaigns/$id'
    | '/event/$id'
    | '/plans/$id'
    | '/posts/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/search'
    | '/account'
    | '/notifications'
    | '/privacy-policy'
    | '/profile'
    | '/terms-of-service'
    | '/change-password'
    | '/forgot-password'
    | '/login'
    | '/register'
    | '/reset-password'
    | '/verifyemail'
    | '/welcome'
    | '/'
    | '/campaigns/add'
    | '/email/new-email'
    | '/email/success'
    | '/email/verify-new-email'
    | '/event/add'
    | '/plans/add'
    | '/posts/add'
    | '/email/verify'
    | '/campaigns'
    | '/event'
    | '/plans'
    | '/posts'
    | '/campaigns/$id/edit'
    | '/campaigns/variant/$variantId'
    | '/event/$id/edit'
    | '/plans/$id/edit'
    | '/posts/$id/edit'
    | '/campaigns/$id'
    | '/event/$id'
    | '/plans/$id'
    | '/posts/$id'
  id:
    | '__root__'
    | '/_app'
    | '/_auth'
    | '/search'
    | '/_app/account'
    | '/_app/notifications'
    | '/_app/privacy-policy'
    | '/_app/profile'
    | '/_app/terms-of-service'
    | '/_auth/change-password'
    | '/_auth/forgot-password'
    | '/_auth/login'
    | '/_auth/register'
    | '/_auth/reset-password'
    | '/_auth/verifyemail'
    | '/_auth/welcome'
    | '/_app/'
    | '/_app/campaigns/add'
    | '/_app/email/new-email'
    | '/_app/email/success'
    | '/_app/email/verify-new-email'
    | '/_app/event/add'
    | '/_app/plans/add'
    | '/_app/posts/add'
    | '/_protected/email/verify'
    | '/_app/campaigns/'
    | '/_app/event/'
    | '/_app/plans/'
    | '/_app/posts/'
    | '/_app/campaigns/$id/edit'
    | '/_app/campaigns/variant/$variantId'
    | '/_app/event/$id/edit'
    | '/_app/plans/$id/edit'
    | '/_app/posts/$id/edit'
    | '/_app/campaigns/$id/'
    | '/_app/event/$id/'
    | '/_app/plans/$id/'
    | '/_app/posts/$id/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AppRoute: typeof AppRouteWithChildren
  AuthRoute: typeof AuthRouteWithChildren
  SearchRoute: typeof SearchRoute
  ProtectedEmailVerifyRoute: typeof ProtectedEmailVerifyRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/search': {
      id: '/search'
      path: '/search'
      fullPath: '/search'
      preLoaderRoute: typeof SearchRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app': {
      id: '/_app'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AppRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app/': {
      id: '/_app/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AppIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_auth/welcome': {
      id: '/_auth/welcome'
      path: '/welcome'
      fullPath: '/welcome'
      preLoaderRoute: typeof AuthWelcomeRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/verifyemail': {
      id: '/_auth/verifyemail'
      path: '/verifyemail'
      fullPath: '/verifyemail'
      preLoaderRoute: typeof AuthVerifyemailRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/reset-password': {
      id: '/_auth/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof AuthResetPasswordRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/register': {
      id: '/_auth/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof AuthRegisterRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/login': {
      id: '/_auth/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AuthLoginRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/forgot-password': {
      id: '/_auth/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof AuthForgotPasswordRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/change-password': {
      id: '/_auth/change-password'
      path: '/change-password'
      fullPath: '/change-password'
      preLoaderRoute: typeof AuthChangePasswordRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_app/terms-of-service': {
      id: '/_app/terms-of-service'
      path: '/terms-of-service'
      fullPath: '/terms-of-service'
      preLoaderRoute: typeof AppTermsOfServiceRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/profile': {
      id: '/_app/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof AppProfileRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/privacy-policy': {
      id: '/_app/privacy-policy'
      path: '/privacy-policy'
      fullPath: '/privacy-policy'
      preLoaderRoute: typeof AppPrivacyPolicyRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/notifications': {
      id: '/_app/notifications'
      path: '/notifications'
      fullPath: '/notifications'
      preLoaderRoute: typeof AppNotificationsRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/account': {
      id: '/_app/account'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof AppAccountRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/posts/': {
      id: '/_app/posts/'
      path: '/posts'
      fullPath: '/posts'
      preLoaderRoute: typeof AppPostsIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/plans/': {
      id: '/_app/plans/'
      path: '/plans'
      fullPath: '/plans'
      preLoaderRoute: typeof AppPlansIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/event/': {
      id: '/_app/event/'
      path: '/event'
      fullPath: '/event'
      preLoaderRoute: typeof AppEventIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaigns/': {
      id: '/_app/campaigns/'
      path: '/campaigns'
      fullPath: '/campaigns'
      preLoaderRoute: typeof AppCampaignsIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_protected/email/verify': {
      id: '/_protected/email/verify'
      path: '/email/verify'
      fullPath: '/email/verify'
      preLoaderRoute: typeof ProtectedEmailVerifyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app/posts/add': {
      id: '/_app/posts/add'
      path: '/posts/add'
      fullPath: '/posts/add'
      preLoaderRoute: typeof AppPostsAddRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/plans/add': {
      id: '/_app/plans/add'
      path: '/plans/add'
      fullPath: '/plans/add'
      preLoaderRoute: typeof AppPlansAddRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/event/add': {
      id: '/_app/event/add'
      path: '/event/add'
      fullPath: '/event/add'
      preLoaderRoute: typeof AppEventAddRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/email/verify-new-email': {
      id: '/_app/email/verify-new-email'
      path: '/email/verify-new-email'
      fullPath: '/email/verify-new-email'
      preLoaderRoute: typeof AppEmailVerifyNewEmailRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/email/success': {
      id: '/_app/email/success'
      path: '/email/success'
      fullPath: '/email/success'
      preLoaderRoute: typeof AppEmailSuccessRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/email/new-email': {
      id: '/_app/email/new-email'
      path: '/email/new-email'
      fullPath: '/email/new-email'
      preLoaderRoute: typeof AppEmailNewEmailRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaigns/add': {
      id: '/_app/campaigns/add'
      path: '/campaigns/add'
      fullPath: '/campaigns/add'
      preLoaderRoute: typeof AppCampaignsAddRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/posts/$id/': {
      id: '/_app/posts/$id/'
      path: '/posts/$id'
      fullPath: '/posts/$id'
      preLoaderRoute: typeof AppPostsIdIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/plans/$id/': {
      id: '/_app/plans/$id/'
      path: '/plans/$id'
      fullPath: '/plans/$id'
      preLoaderRoute: typeof AppPlansIdIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/event/$id/': {
      id: '/_app/event/$id/'
      path: '/event/$id'
      fullPath: '/event/$id'
      preLoaderRoute: typeof AppEventIdIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaigns/$id/': {
      id: '/_app/campaigns/$id/'
      path: '/campaigns/$id'
      fullPath: '/campaigns/$id'
      preLoaderRoute: typeof AppCampaignsIdIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/posts/$id/edit': {
      id: '/_app/posts/$id/edit'
      path: '/posts/$id/edit'
      fullPath: '/posts/$id/edit'
      preLoaderRoute: typeof AppPostsIdEditRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/plans/$id/edit': {
      id: '/_app/plans/$id/edit'
      path: '/plans/$id/edit'
      fullPath: '/plans/$id/edit'
      preLoaderRoute: typeof AppPlansIdEditRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/event/$id/edit': {
      id: '/_app/event/$id/edit'
      path: '/event/$id/edit'
      fullPath: '/event/$id/edit'
      preLoaderRoute: typeof AppEventIdEditRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaigns/variant/$variantId': {
      id: '/_app/campaigns/variant/$variantId'
      path: '/campaigns/variant/$variantId'
      fullPath: '/campaigns/variant/$variantId'
      preLoaderRoute: typeof AppCampaignsVariantVariantIdRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaigns/$id/edit': {
      id: '/_app/campaigns/$id/edit'
      path: '/campaigns/$id/edit'
      fullPath: '/campaigns/$id/edit'
      preLoaderRoute: typeof AppCampaignsIdEditRouteImport
      parentRoute: typeof AppRoute
    }
  }
}

interface AppRouteChildren {
  AppAccountRoute: typeof AppAccountRoute
  AppNotificationsRoute: typeof AppNotificationsRoute
  AppPrivacyPolicyRoute: typeof AppPrivacyPolicyRoute
  AppProfileRoute: typeof AppProfileRoute
  AppTermsOfServiceRoute: typeof AppTermsOfServiceRoute
  AppIndexRoute: typeof AppIndexRoute
  AppCampaignsAddRoute: typeof AppCampaignsAddRoute
  AppEmailNewEmailRoute: typeof AppEmailNewEmailRoute
  AppEmailSuccessRoute: typeof AppEmailSuccessRoute
  AppEmailVerifyNewEmailRoute: typeof AppEmailVerifyNewEmailRoute
  AppEventAddRoute: typeof AppEventAddRoute
  AppPlansAddRoute: typeof AppPlansAddRoute
  AppPostsAddRoute: typeof AppPostsAddRoute
  AppCampaignsIndexRoute: typeof AppCampaignsIndexRoute
  AppEventIndexRoute: typeof AppEventIndexRoute
  AppPlansIndexRoute: typeof AppPlansIndexRoute
  AppPostsIndexRoute: typeof AppPostsIndexRoute
  AppCampaignsIdEditRoute: typeof AppCampaignsIdEditRoute
  AppCampaignsVariantVariantIdRoute: typeof AppCampaignsVariantVariantIdRoute
  AppEventIdEditRoute: typeof AppEventIdEditRoute
  AppPlansIdEditRoute: typeof AppPlansIdEditRoute
  AppPostsIdEditRoute: typeof AppPostsIdEditRoute
  AppCampaignsIdIndexRoute: typeof AppCampaignsIdIndexRoute
  AppEventIdIndexRoute: typeof AppEventIdIndexRoute
  AppPlansIdIndexRoute: typeof AppPlansIdIndexRoute
  AppPostsIdIndexRoute: typeof AppPostsIdIndexRoute
}

const AppRouteChildren: AppRouteChildren = {
  AppAccountRoute: AppAccountRoute,
  AppNotificationsRoute: AppNotificationsRoute,
  AppPrivacyPolicyRoute: AppPrivacyPolicyRoute,
  AppProfileRoute: AppProfileRoute,
  AppTermsOfServiceRoute: AppTermsOfServiceRoute,
  AppIndexRoute: AppIndexRoute,
  AppCampaignsAddRoute: AppCampaignsAddRoute,
  AppEmailNewEmailRoute: AppEmailNewEmailRoute,
  AppEmailSuccessRoute: AppEmailSuccessRoute,
  AppEmailVerifyNewEmailRoute: AppEmailVerifyNewEmailRoute,
  AppEventAddRoute: AppEventAddRoute,
  AppPlansAddRoute: AppPlansAddRoute,
  AppPostsAddRoute: AppPostsAddRoute,
  AppCampaignsIndexRoute: AppCampaignsIndexRoute,
  AppEventIndexRoute: AppEventIndexRoute,
  AppPlansIndexRoute: AppPlansIndexRoute,
  AppPostsIndexRoute: AppPostsIndexRoute,
  AppCampaignsIdEditRoute: AppCampaignsIdEditRoute,
  AppCampaignsVariantVariantIdRoute: AppCampaignsVariantVariantIdRoute,
  AppEventIdEditRoute: AppEventIdEditRoute,
  AppPlansIdEditRoute: AppPlansIdEditRoute,
  AppPostsIdEditRoute: AppPostsIdEditRoute,
  AppCampaignsIdIndexRoute: AppCampaignsIdIndexRoute,
  AppEventIdIndexRoute: AppEventIdIndexRoute,
  AppPlansIdIndexRoute: AppPlansIdIndexRoute,
  AppPostsIdIndexRoute: AppPostsIdIndexRoute,
}

const AppRouteWithChildren = AppRoute._addFileChildren(AppRouteChildren)

interface AuthRouteChildren {
  AuthChangePasswordRoute: typeof AuthChangePasswordRoute
  AuthForgotPasswordRoute: typeof AuthForgotPasswordRoute
  AuthLoginRoute: typeof AuthLoginRoute
  AuthRegisterRoute: typeof AuthRegisterRoute
  AuthResetPasswordRoute: typeof AuthResetPasswordRoute
  AuthVerifyemailRoute: typeof AuthVerifyemailRoute
  AuthWelcomeRoute: typeof AuthWelcomeRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthChangePasswordRoute: AuthChangePasswordRoute,
  AuthForgotPasswordRoute: AuthForgotPasswordRoute,
  AuthLoginRoute: AuthLoginRoute,
  AuthRegisterRoute: AuthRegisterRoute,
  AuthResetPasswordRoute: AuthResetPasswordRoute,
  AuthVerifyemailRoute: AuthVerifyemailRoute,
  AuthWelcomeRoute: AuthWelcomeRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AppRoute: AppRouteWithChildren,
  AuthRoute: AuthRouteWithChildren,
  SearchRoute: SearchRoute,
  ProtectedEmailVerifyRoute: ProtectedEmailVerifyRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
