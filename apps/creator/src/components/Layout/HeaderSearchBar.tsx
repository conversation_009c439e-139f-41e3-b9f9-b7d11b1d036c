import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Input } from "@vtuber/ui/components/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@vtuber/ui/components/popover";
import { cn } from "@vtuber/ui/lib/utils";
import { useState } from "react";

type Props = {
  className?: string;
  onSearch?: (s: string) => void;
};

export const HeaderSearchBar = ({ className, onSearch }: Props) => {
  const { getText } = useLanguage();
  const [opened, setOpened] = useState(false);
  const [value, setValue] = useState("");
  const router = useRouter();
  return (
    <Popover
      open={opened}
      onOpenChange={setOpened}>
      <PopoverTrigger asChild>
        <button
          className={cn(
            "px-4 py-2 rounded-[40px] border border-white",
            className,
            opened ? "bg-primary" : "bg-transparent",
          )}>
          {/* // className={`${opened ? "bg-primary" : "`}> */}
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M3.27344 8.58594C4.0026 9.3151 4.88802 9.67969 5.92969 9.67969C6.97135 9.67969 7.85677 9.3151 8.58594 8.58594C9.3151 7.85677 9.67969 6.97135 9.67969 5.92969C9.67969 4.88802 9.3151 4.0026 8.58594 3.27344C7.85677 2.54427 6.97135 2.17969 5.92969 2.17969C4.88802 2.17969 4.0026 2.54427 3.27344 3.27344C2.54427 4.0026 2.17969 4.88802 2.17969 5.92969C2.17969 6.97135 2.54427 7.85677 3.27344 8.58594ZM10.9297 9.67969L15.0703 13.8203L13.8203 15.0703L9.67969 10.9297V10.2656L9.44531 10.0312C8.45573 10.8906 7.28385 11.3203 5.92969 11.3203C4.41927 11.3203 3.13021 10.7995 2.0625 9.75781C1.02083 8.71615 0.5 7.4401 0.5 5.92969C0.5 4.41927 1.02083 3.14323 2.0625 2.10156C3.13021 1.03385 4.41927 0.5 5.92969 0.5C7.4401 0.5 8.71615 1.03385 9.75781 2.10156C10.7995 3.14323 11.3203 4.41927 11.3203 5.92969C11.3203 6.47656 11.1901 7.10156 10.9297 7.80469C10.6693 8.48177 10.3698 9.02865 10.0312 9.44531L10.2656 9.67969H10.9297Z"
              fill="white"
            />
          </svg>
        </button>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="bg-background border-none rounded-[5px] space-y-1.5"
        style={{
          filter: "drop-shadow(0px 0px 8px #fff)",
        }}>
        <p>フリーワード検索</p>
        <div className="relative flex items-stretch justify-between bg-white rounded-[5px] h-[29px] border border-white overflow-hidden">
          <Input
            onKeyDownCapture={(e) => {
              const key = e.key;
              if (key === "Enter") {
                router.navigate({
                  to: "/search",
                  search: {
                    query: value,
                  },
                });
              }
            }}
            onChange={(e) => {
              setValue(e.target.value);
            }}
            value={value}
            className="bg-white rounded-[5px] text-background flex-1 shadow-none border-none focus-visible:ring-0 h-full"
            autoFocus
          />
          {/* change to search */}
          <button
            onClick={() => router.navigate({ to: "/event" })}
            className="bg-background flex items-center gap-x-3 h-full px-3">
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M3.27344 8.58594C4.0026 9.3151 4.88802 9.67969 5.92969 9.67969C6.97135 9.67969 7.85677 9.3151 8.58594 8.58594C9.3151 7.85677 9.67969 6.97135 9.67969 5.92969C9.67969 4.88802 9.3151 4.0026 8.58594 3.27344C7.85677 2.54427 6.97135 2.17969 5.92969 2.17969C4.88802 2.17969 4.0026 2.54427 3.27344 3.27344C2.54427 4.0026 2.17969 4.88802 2.17969 5.92969C2.17969 6.97135 2.54427 7.85677 3.27344 8.58594ZM10.9297 9.67969L15.0703 13.8203L13.8203 15.0703L9.67969 10.9297V10.2656L9.44531 10.0312C8.45573 10.8906 7.28385 11.3203 5.92969 11.3203C4.41927 11.3203 3.13021 10.7995 2.0625 9.75781C1.02083 8.71615 0.5 7.4401 0.5 5.92969C0.5 4.41927 1.02083 3.14323 2.0625 2.10156C3.13021 1.03385 4.41927 0.5 5.92969 0.5C7.4401 0.5 8.71615 1.03385 9.75781 2.10156C10.7995 3.14323 11.3203 4.41927 11.3203 5.92969C11.3203 6.47656 11.1901 7.10156 10.9297 7.80469C10.6693 8.48177 10.3698 9.02865 10.0312 9.44531L10.2656 9.67969H10.9297Z"
                fill="#B7B7B7"
              />
            </svg>
            <p className="text-xs whitespace-nowrap text-[#B7B7B7]">検索</p>
          </button>
        </div>
      </PopoverContent>
    </Popover>
  );
};
