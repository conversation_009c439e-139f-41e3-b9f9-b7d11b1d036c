import { Link, useLocation } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { Logo } from "@vtuber/ui/components/logo";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { cn } from "@vtuber/ui/lib/utils";
import { useEffect, useState } from "react";
import { HeaderSearchBar } from "./HeaderSearchBar";

export const Header = ({ className }: { className?: string }) => {
  const { pathname } = useLocation();
  const { session: user } = useAuth();
  const { isDesktop } = useIsMobile();
  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    const handleScroll = (e: Event) => {
      const scrollPosition = (e.target as Document).scrollingElement?.scrollTop;
      setScrollPosition(scrollPosition || 0);
    };

    document.addEventListener("scroll", handleScroll);

    return () => {
      document.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <header
      className={cn(
        "w-full flex items-center justify-between sm:px-10 px-4 h-20 lg:bg-transparent lg:shadow-none shadow-lg",
        pathname === "/" &&
          scrollPosition > 831 &&
          "bg-background/30 backdrop-blur top-0 z-50 fixed",
        pathname !== "/" &&
          "sticky top-0 z-50 bg-background/30 backdrop-blur-[8px]",
        !isDesktop && "bg-background/30 backdrop-blur-[8px]",
        className,
      )}>
      <section className="flex items-center xl:gap-x-6 sm:gap-x-32 gap-x-8">
        <Link to="/">
          <Logo className="sm:w-[112px] sm:h-9 w-[96px] h-[30px]" />
        </Link>
        <div className="sm:gap-x-6 gap-x-3 flex items-center">
          <HeaderSearchBar />
        </div>
      </section>
    </header>
  );
};
