import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@vtuber/ui/components/dropdown-menu";
import { useNotificationNavigation } from "@vtuber/ui/hooks/use-notification-navigation";
import { cn } from "@vtuber/ui/lib/utils";
import { CheckSquare, EllipsisVertical, ExternalLink, X } from "lucide-react";
import { useState } from "react";
import { useNotification } from "./notification-provider";

type Props = {
  id: string;
  className?: string;
};
export const NotificationActions = ({ id, className }: Props) => {
  const { getText } = useLanguage();
  const { navigate } = useNotificationNavigation();
  const { deleteNotificationById, getNotificationById, onMarkNotification } =
    useNotification();
  const isRead = getNotificationById(id)?.isRead;
  const [open, setOpen] = useState(false);

  return (
    <DropdownMenu
      open={open}
      onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          className={cn(
            "bg-gray-100 rounded-full hover:bg-gray-200 absolute right-2 top-1/2 -translate-y-1/2 transition-all duration-150 ease-in-out group-hover:opacity-100 opacity-0",
            className,
          )}
          size={"icon"}>
          <EllipsisVertical className="text-gray-500" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent side="bottom">
        <DropdownMenuItem
          onClick={(e) => {
            e.preventDefault();
            onMarkNotification(id, isRead || false);
            setOpen(false);
          }}>
          <CheckSquare /> {getText(isRead ? "mark_as_unread" : "mark_read")}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => {
            e.preventDefault();
            deleteNotificationById(id);
            setOpen(false);
          }}>
          <X /> {getText("delete")}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => {
            e.preventDefault();
            navigate({
              to: getNotificationById(id)?.deepLink,
              resetscroll: false,
            });
          }}>
          <ExternalLink /> {getText("view")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
