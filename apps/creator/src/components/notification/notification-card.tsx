import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useLanguage } from "@vtuber/language/hooks";
import { Notification } from "@vtuber/services/notifications";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import { Card, CardContent } from "@vtuber/ui/components/card";

import { useNotificationNavigation } from "@vtuber/ui/hooks/use-notification-navigation";
import { getTimeAgo } from "@vtuber/ui/lib/get-time-ago";
import { cn } from "@vtuber/ui/lib/utils";
import { Bell, CheckCircle, ExternalLink, Trash } from "lucide-react";
import { NotificationActions } from "./notification-actions";
import { useNotification } from "./notification-provider";

export const NotificationCard = ({ data }: { data: Notification }) => {
  const { language, getText } = useLanguage();
  const { onMarkNotification, deleteNotificationById } = useNotification();

  const isRead = data.isRead;

  const { navigate } = useNotificationNavigation();
  const desc = {
    ja: data.descriptionJp,
    en: data.description,
  };

  return (
    <Card
      className={cn(
        "mb-4 transition-all duration-300 border-l-8 group relative overflow-hidden",
        isRead ? "bg-card/50" : "bg-card shadow-lg",
        isRead ? "border-gray-200" : "border-primary",
      )}>
      <CardContent className="p-0 overflow-hidden">
        <div className="flex items-start">
          <div className="flex-1 p-4">
            <div className="flex items-center justify-between mb-2">
              <div
                className="flex items-center gap-2"
                onClick={() => {
                  navigate({ to: data.deepLink, resetscroll: false });
                }}>
                <div className="p-2 rounded-full bg-primary/10">
                  <Bell className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <h3
                      className={`font-semibold sm:text-lg text-sm ${isRead ? "text-foreground/70" : "text-foreground"}`}>
                      {desc[language as "en" | "ja"] || data.descriptionJp}
                    </h3>

                    {!isRead && (
                      <Badge
                        variant={"success"}
                        className="text-xs capitalize sm:rounded-full rounded-none sm:rounded-bl-full rounded-bl-lg sm:static absolute top-0 right-0">
                        {getText("new")}
                      </Badge>
                    )}
                  </div>

                  {data.createdAt && (
                    <span className="text-xs text-muted-foreground">
                      {typeof data.createdAt === "string"
                        ? getTimeAgo(new Date(data.createdAt))
                        : getTimeAgo(timestampDate(data.createdAt!))}
                    </span>
                  )}
                </div>
              </div>
              <NotificationActions
                id={data.id}
                className="!static opacity-100 shadow-none bg-card hover:bg-muted sm:flex hidden"
              />
            </div>

            <div className="sm:ml-10">
              <div className="flex items-center sm:gap-x-2 gap-x-1 sm:justify-start justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    navigate({ to: data.deepLink, resetscroll: false });
                  }}
                  className="h-7 text-xs gap-1">
                  <ExternalLink className="h-3 w-3" />
                  {getText("view")}
                </Button>
                <Button
                  variant={"ghost"}
                  size={"sm"}
                  onClick={() => {
                    deleteNotificationById(data.id);
                  }}
                  className="h-7 text-xs gap-1">
                  <Trash className="h-3 w-3" />
                  {getText("delete")}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    onMarkNotification(data.id, isRead);
                  }}
                  className="h-7 text-xs gap-1">
                  <CheckCircle className="h-3 w-3" />
                  {getText(isRead ? "mark_as_unread" : "mark_read")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
