import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useLanguage } from "@vtuber/language/hooks";
import { Notification } from "@vtuber/services/notifications";
import { useNotificationNavigation } from "@vtuber/ui/hooks/use-notification-navigation";
import { getTimeAgo } from "@vtuber/ui/lib/get-time-ago";
import { cn } from "@vtuber/ui/lib/utils";
import { NotificationActions } from "./notification-actions";

type Props = {
  notification: Notification;
};

export const NotificationItem = ({ notification }: Props) => {
  const { language } = useLanguage();
  const isRead = notification.isRead;

  const { navigate } = useNotificationNavigation();
  const desc = {
    ja: notification.descriptionJp,
    en: notification.description,
  };

  return (
    <div
      className={cn(
        "flex items-center justify-between border-b border-b-border hover:bg-muted group relative last:border-b-0 last:border-b-transparent rounded-none py-3 px-2",
        !isRead ? "bg-muted" : "",
      )}>
      <div className="flex flex-1 gap-2 items-start cursor-pointer">
        <span
          className={cn(
            "flex h-2 w-2 translate-y-1 rounded-full",
            isRead ? "bg-transparent" : "bg-blue-500",
          )}
        />
        <div
          onClick={(e) => {
            e.preventDefault();
            navigate({ to: notification.deepLink, resetscroll: false });
          }}
          className="space-y-1 flex-1">
          <p className="text-sm font-medium leading-none">
            {desc[language as "en" | "ja"] || notification.descriptionJp}
          </p>
          <p className="text-xs italic text-muted-foreground">
            {typeof notification.createdAt === "string"
              ? getTimeAgo(new Date(notification.createdAt))
              : getTimeAgo(timestampDate(notification.createdAt!))}
          </p>
        </div>
        <NotificationActions id={notification.id} />
      </div>
    </div>
  );
};
