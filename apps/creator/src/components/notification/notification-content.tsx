import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { InfiniteScroll } from "@vtuber/ui/components/infinite-scroll";
import { Separator } from "@vtuber/ui/components/separator";
import { ArrowRight, BellRingIcon } from "lucide-react";
import { NotificationItem } from "./notification-item";
import { useNotification } from "./notification-provider";

export const NotificationContent = () => {
  const { getText } = useLanguage();
  const {
    notifications,
    loadingNotifications: isLoading,
    isRefetchingNotifications: isRefetching,
    fetchMoreNotifications,
  } = useNotification();
  if (isLoading)
    return (
      <NotificationContentWrapper>
        <div className="h-[60dvh] max-h-[400px] flex items-center justify-center animate-pulse bg-muted">
          <p className="text-sm text-muted-foreground">
            {getText("loading_notifications")}...
          </p>
        </div>
      </NotificationContentWrapper>
    );
  if (notifications.length === 0)
    return (
      <NotificationContentWrapper>
        <div className="h-[60dvh] max-h-[400px] flex items-center justify-center bg-muted">
          <div className="text-muted-foreground flex flex-col items-center gap-3">
            <BellRingIcon size={50} />
            <p className="text-sm text-muted-foreground">
              {getText("no_notifications_message")}
            </p>
          </div>
        </div>
      </NotificationContentWrapper>
    );

  return (
    <NotificationContentWrapper>
      <InfiniteScroll
        loaderClassName="absolute bottom-0 py-0.5 left-1/2 -translate-x-1/2 bg-background w-full"
        isFetching={isRefetching}
        className="h-[60dvh] max-h-[400px] overflow-y-auto"
        onIntersecting={fetchMoreNotifications}>
        {notifications.map((n) => (
          <NotificationItem
            key={n.id}
            notification={n}
          />
        ))}
      </InfiniteScroll>
    </NotificationContentWrapper>
  );
};

const NotificationContentWrapper = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { getText, language = "ja" } = useLanguage();
  const { unreadCount } = useNotification();
  const description = {
    en: `You have ${unreadCount} unread notifications.`,
    ja: `未読通知は ${unreadCount} 件です。`,
  };
  return (
    <Card>
      <CardHeader className="py-3 rounded-none">
        <CardTitle className="capitalize">{getText("notifications")}</CardTitle>
        <CardDescription>{description[language]}</CardDescription>
      </CardHeader>
      <Separator />
      <CardContent className="p-0 relative">{children}</CardContent>
      <Separator />
      <CardFooter className="block text-center pb-0 py-3 hover:bg-muted">
        <Link
          to="/notifications"
          className="text-muted-foreground text-sm flex items-center gap-x-1 justify-center">
          {getText("view_all")} <ArrowRight size={18} />
        </Link>
      </CardFooter>
    </Card>
  );
};
