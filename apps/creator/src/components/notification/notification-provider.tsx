import { useMutation, useQuery } from "@connectrpc/connect-query";
import { useLanguage } from "@vtuber/language/hooks";
import {
  Notification,
  NotificationsService,
} from "@vtuber/services/notifications";
import { useNotificationNavigation } from "@vtuber/ui/hooks/use-notification-navigation";
import { useWebSocket } from "@vtuber/ui/hooks/use-web-socket";
import { Bell } from "lucide-react";
import { createContext, useContext, useEffect, useState } from "react";
import { toast } from "sonner";

type Props = {
  enabled?: boolean;
  children?: React.ReactNode;
};

type NotificationContextType = {
  notifications: Notification[];
  isRefetchingNotifications: boolean;
  isDeleting: boolean;
  deleteNotificationById: (id: string) => void;
  unreadCount?: number;
  setUnreadCount: (id: string) => void;
  getNotificationById: (id: string) => Notification | undefined;
  setNotifications: React.Dispatch<React.SetStateAction<Notification[]>>;
  toggleNotificationStatus: (id: string) => void;
  fetchMoreNotifications: (interSecting: boolean) => void;
  loadingNotifications: boolean;
  onMarkNotification: (id: string, isRead: boolean) => void;
};

const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  isRefetchingNotifications: false,
  isDeleting: false,
  deleteNotificationById: () => {},
  unreadCount: 0,
  setUnreadCount: () => {},
  getNotificationById: () => undefined,
  setNotifications: () => {},
  toggleNotificationStatus: () => {},
  fetchMoreNotifications: () => {},
  loadingNotifications: false,
  onMarkNotification: () => {},
});

export const NotificationProvider = ({ children, enabled = true }: Props) => {
  const [size, setSize] = useState(20);
  const { navigate } = useNotificationNavigation();
  const { data: count } = useQuery(
    NotificationsService.method.getNotificationCount,
    {},
  );

  const { data, isPending, isRefetching } = useQuery(
    NotificationsService.method.getUserNotifications,
    {
      pagination: {
        size,
        page: 0,
      },
    },
    {
      enabled,
    },
  );

  const fetchMoreNotifications = (interSecting: boolean) => {
    if (
      interSecting &&
      !isRefetching &&
      data?.paginationDetails &&
      data?.paginationDetails?.totalItems > size
    ) {
      setSize((prev) => prev + 20);
    }
  };

  const { language } = useLanguage();
  const [initialNotification, setNotifications] = useState<Notification[]>(
    data?.data || [],
  );
  const [notificationCount, setNotificationCount] = useState(
    !!count?.count ? Number(count.count) : 0,
  );
  const getNotificationById = (id: string) => {
    return initialNotification.find((n) => n.id === id);
  };

  const toggleNotificationStatus = (id: string) => {
    setNotifications((prev) => {
      return prev.map((n) => {
        if (n.id === id) {
          return {
            ...n,
            isRead: !n.isRead,
          };
        }
        return n;
      });
    });
  };

  const setUnreadCount = (id: string) => {
    const notification = initialNotification.find((n) => n.id === id);
    if (!notification?.isRead) {
      setNotificationCount((prev) => prev - 1);
    } else {
      setNotificationCount((prev) => prev + 1);
    }
  };

  useEffect(() => {
    setNotifications(data?.data || []);
  }, [data]);

  useEffect(() => {
    setNotificationCount(!!count?.count ? Number(count.count) : 0);
  }, [count]);

  useWebSocket({
    initialData: initialNotification,
    onSuccess: (data) => {
      const desc = {
        ja: data?.descriptionJp,
        en: data?.description,
      };

      toast.info("New Notification", {
        description: desc[language as "en" | "ja"],
        position: "bottom-left",
        icon: <Bell className="h-4 w-4 animate-tada" />,
        // action: {
        //   label: (
        //     <div className="flex items-center gap-x-2">
        //       <ExternalLink className="h-4 w-4" /> View
        //     </div>
        //   ),
        //   onClick: (e) => {
        //     e.preventDefault();
        //     e.stopPropagation();
        //     navigate({ to: data?.deepLink, resetscroll: false });
        //   },
        // },
      });
      data && setNotifications((prev) => [data, ...prev]);
      setNotificationCount((prev) => prev + 1);
    },
  });

  const { mutateAsync: deleteNotification, isPending: isDeleting } =
    useMutation(NotificationsService.method.deleteNotificationById);

  const deleteNotificationById = (id: string) => {
    deleteNotification({ id })
      .then(() => {
        setNotifications((prev) => prev.filter((n) => n.id !== id));
        setUnreadCount(id);
      })
      .catch((err) => {
        toast.error(err.message);
      });
  };

  const { mutate: markAsRead } = useMutation(
    NotificationsService.method.markNotificationAsRead,
    {
      onSuccess: (_, { id }) => {
        // setUnreadCount( || 0n);
        // toggleNotificationStatus(id || 0n);
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );
  const { mutate: markAsUnread } = useMutation(
    NotificationsService.method.markNotificationAsUnRead,
    {
      onSuccess: (_, { id }) => {
        // toggleNotificationStatus(id || 0n);
        // setUnreadCount(id || 0n);
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const onMarkNotification = (id: string, isRead: boolean) => {
    if (isRead) {
      markAsUnread({ id });
    } else {
      markAsRead({ id });
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications: initialNotification,
        isRefetchingNotifications: isRefetching,
        deleteNotificationById,
        isDeleting,
        unreadCount: notificationCount,
        setUnreadCount,
        getNotificationById,
        setNotifications,
        toggleNotificationStatus,
        fetchMoreNotifications,
        loadingNotifications: isPending,
        onMarkNotification,
      }}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      "useNotificationContext must be used within a NotificationProvider",
    );
  }
  return context;
};
