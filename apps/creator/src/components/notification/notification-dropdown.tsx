import { useRouter } from "@tanstack/react-router";
import { Indicator } from "@vtuber/ui/components/indicator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@vtuber/ui/components/popover";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { useState } from "react";
import { NotificationContent } from "./notification-content";
import { NotificationProvider, useNotification } from "./notification-provider";

export const NotificationDropdown = () => {
  const router = useRouter();
  const { isMobile } = useIsMobile();
  const [opened, setOpened] = useState(false);

  return (
    <NotificationProvider enabled={opened}>
      {isMobile ? (
        <NotificationTrigger
          open={() => {
            setOpened(true);
            router.navigate({ to: "/notifications" });
          }}
        />
      ) : (
        <Popover>
          <PopoverTrigger asChild>
            <NotificationTrigger
              open={() => {
                setOpened(true);
              }}
            />
          </PopoverTrigger>
          <PopoverContent
            align="end"
            className="max-w-[23rem] min-w-[20rem] p-0 ">
            <NotificationContent />
          </PopoverContent>
        </Popover>
      )}
    </NotificationProvider>
  );
};

const NotificationTrigger = ({ open }: { open: () => void }) => {
  const { unreadCount } = useNotification();
  return (
    <Indicator
      onClick={open}
      indicatorLabel={unreadCount}
      size={"default"}
      className="rounded-full p-0 h-10 w-10"></Indicator>
  );
};
