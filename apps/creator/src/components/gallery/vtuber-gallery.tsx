import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSearch } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { vtuberGalleryClient } from "@vtuber/services/client";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Button } from "@vtuber/ui/components/button";
import { DeleteDialog } from "@vtuber/ui/components/DeleteDialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@vtuber/ui/components/dropdown-menu";
import { Media } from "@vtuber/ui/components/media";
import { MorphingDialogImage } from "@vtuber/ui/components/morphing-dialog";
import { Paginator } from "@vtuber/ui/components/paginator";
import { Spinner } from "@vtuber/ui/components/spinner";
import { VtuberGalleryCard } from "@vtuber/ui/components/vtuber-gallery-card";
import { cn } from "@vtuber/ui/lib/utils";
import { Edit, MoreVertical, Plus, Trash2 } from "lucide-react";
import { EmptyGallery } from "./empty-gallery";
import { GalleryFormDialog } from "./gallery-form-dialog";

export const VtuberGallery = ({ tabValue }: { tabValue: string }) => {
  const { getText } = useLanguage();
  const { session } = useAuth();
  const { page } = useSearch({ from: "/_app/profile" });
  const vtuberId = session?.vtuber?.id;
  const queryClient = useQueryClient();
  const { data, isPending, isFetching } = useQuery({
    queryKey: ["vtuber_gallery", page],
    queryFn: async () => {
      const [data] = await vtuberGalleryClient.getVtuberGalleries({
        vtuberId: vtuberId!,
        pagination: {
          size: 18,
          sort: "created_at",
          order: "DESC",
          page: page || 0,
        },
      });
      return data;
    },
    enabled: !!vtuberId && tabValue === "gallery",
  });
  const galleries = data?.data;
  if (isPending || isFetching)
    return (
      <div className="h-56 flex items-center justify-center">
        <Spinner className="!size-20" />
      </div>
    );
  if (!galleries || galleries.length === 0) return <EmptyGallery />;

  // const calculateLayoutReliable = () => {
  //   const layout: string[] = [];
  //   let i = 0;

  //   while (i < galleries.length) {
  //     const remaining = galleries.length - i;

  //     if (remaining >= 4 && i % 6 === 0) {
  //       layout.push("col-span-2 row-span-2"); // Large
  //       layout.push("col-span-1 row-span-1"); // Small
  //       layout.push("col-span-1 row-span-1"); // Small
  //       layout.push("col-span-1 row-span-1"); // Small
  //       i += 4;
  //     } else if (remaining >= 2 && i % 6 === 4) {
  //       layout.push("col-span-2 row-span-1"); // Wide
  //       layout.push("col-span-1 row-span-1"); // Small
  //       i += 2;
  //     } else {
  //       layout.push("col-span-1 row-span-1"); // Regular
  //       i += 1;
  //     }
  //   }

  //   return layout;
  // };

  // const layout = calculateLayoutReliable();
  return (
    <div className="space-y-10">
      <div className="flex justify-end">
        <GalleryFormDialog asChild>
          <Button
            size={"lg"}
            variant={"minimal-dark"}
            className="rounded-lg">
            <Plus className="size-10 mr-2" />
            Add Media
          </Button>
        </GalleryFormDialog>
      </div>
      <div
        className="grid grid-cols-2 sm:grid-cols-3 gap-1"
        // className="grid grid-cols-3 md:grid-cols-6 auto-rows-[120px] md:auto-rows-[160px] gap-1"
        // style={{
        //   gridAutoFlow: "row dense",
        // }}
      >
        {galleries.map((g, index) => (
          <div
            key={g.id.toString()}
            className={cn(
              "relative overflow-hidden group cursor-pointer",
              // g.mediaType === "picture"
              //   ? layout[index]
              //   : "col-span-2 row-span-2",
            )}>
            <VtuberGalleryCard
              gallery={g}
              className="size-full relative overflow-hidden">
              <div className="group-hover:bg-black/50 transition-opacity duration-300 ease-in-out z-30 absolute inset-0" />
              <AspectRatio ratio={16 / 9}>
                {g.mediaType === "video" ? (
                  <Media
                    src={g.media}
                    alt={g.description || "gallery"}
                    type={"video"}
                    isPreview
                    canEnableFullScreen={false}
                    showControls={false}
                    className="size-full object-cover transition-transform rounded-none group-hover:scale-105 aspect-auto"
                  />
                ) : (
                  <MorphingDialogImage
                    src={g.media}
                    alt={g.description || "gallery"}
                    className="size-full object-cover transition-transform rounded-none group-hover:scale-105"
                  />
                )}
              </AspectRatio>
            </VtuberGalleryCard>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant={"accent"}
                  className="absolute top-2 right-2 p-0 z-40 group-hover:opacity-100 opacity-0 transition-opacity size-12 rounded-full">
                  <MoreVertical className="size-10" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  onSelect={(e) => {
                    e.preventDefault();
                  }}
                  asChild>
                  <GalleryFormDialog
                    gallery={g}
                    className="w-full">
                    <Edit /> {getText("EDIT")}
                  </GalleryFormDialog>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="hover:bg-destructive/10"
                  onSelect={(e) => {
                    e.preventDefault();
                  }}
                  asChild>
                  <DeleteDialog
                    className="w-full text-destructive capitalize"
                    name={"Gallery"}
                    asChild={false}
                    onDelete={() => {
                      vtuberGalleryClient
                        .deleteVtuberGalleryById({
                          id: g.id,
                        })
                        .then(() => {
                          queryClient.invalidateQueries({
                            queryKey: ["vtuber_gallery"],
                          });
                        });
                    }}>
                    <Trash2 /> {getText("delete")}
                  </DeleteDialog>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        ))}
      </div>
      <Paginator
        currentPage={data.paginationDetails?.currentPage}
        totalPages={data.paginationDetails?.totalPages}
      />
    </div>
  );
};
