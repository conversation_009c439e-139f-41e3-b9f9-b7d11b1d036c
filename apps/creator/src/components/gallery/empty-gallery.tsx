import { ImageIcon, Plus, Upload, Video } from "lucide-react";
import { GalleryFormDialog } from "./gallery-form-dialog";

export const EmptyGallery = () => {
  return (
    <GalleryFormDialog className="pt-12 justify-center flex flex-row w-full">
      <div className="border-2 border-dashed rounded-2xl p-8 transition-all duration-300 border-slate-600 hover:border-slate-500 hover:bg-slate-800/50 cursor-pointer group">
        <div className="relative mb-6">
          <div className="w-20 h-20 mx-auto bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <Upload className="w-10 h-10 text-font group-hover:text-purple-400 transition-colors duration-300" />
          </div>
          <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full flex items-center justify-center animate-pulse">
            <Plus className="w-3 h-3 text-white" />
          </div>
        </div>

        <h3 className="text-2xl font-bold mb-3 text-center text-font group-hover:text-white transition-colors duration-300">
          Create Your First Gallery
        </h3>

        <p className="text-muted-foreground mb-6 text-lg text-center group-hover:text-slate-300 transition-colors duration-300">
          Click to create a new gallery and start organizing your content
        </p>

        <div className="flex items-center justify-center gap-6 mb-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground group-hover:text-slate-300 transition-colors duration-300">
            <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <ImageIcon className="w-4 h-4 text-blue-400" />
            </div>
            <span>Images</span>
          </div>
          <div className="w-1 h-1 bg-slate-600 rounded-full"></div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground group-hover:text-slate-300 transition-colors duration-300">
            <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
              <Video className="w-4 h-4 text-green-400" />
            </div>
            <span>Videos</span>
          </div>
        </div>
      </div>
    </GalleryFormDialog>
  );
};
