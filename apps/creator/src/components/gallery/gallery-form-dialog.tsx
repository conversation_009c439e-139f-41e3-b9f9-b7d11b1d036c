import { useMutation } from "@connectrpc/connect-query";
import { useQueryClient } from "@tanstack/react-query";
import { handleConnectError } from "@vtuber/services/client";
import { VtuberGallery, VtuberGalleryService } from "@vtuber/services/vtubers";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Form } from "@vtuber/ui/components/form";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { HtmlInput } from "@vtuber/ui/components/form-inputs/html-input";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

type Props = {
  className?: string;
  gallery?: VtuberGallery;
  asChild?: boolean;
  children?: React.ReactNode;
};

export const GalleryFormDialog = ({
  gallery,
  asChild = false,
  children,
  className,
}: Props) => {
  const [isUploading, setIsUploading] = useState(false);
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const form = useForm({
    defaultValues: {
      media: gallery?.media,
      mediaType: gallery?.mediaType || "picture",
      description: gallery?.description,
    },
  });
  const mediaType = form.watch("mediaType");
  const addMutation = useMutation(
    VtuberGalleryService.method.addVtuberGallery,
    {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["vtuber_gallery"],
        });
        setOpen(false);
        toast.success("Gallery created successfully");
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );
  const updateMutation = useMutation(
    VtuberGalleryService.method.updateVtuberGalleryById,
    {
      onSuccess: (data) => {
        queryClient.invalidateQueries({
          queryKey: ["vtuber_gallery"],
        });
        toast.success(data.message);
        setOpen(false);
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const onSubmit = form.handleSubmit((values) => {
    if (gallery) {
      updateMutation.mutate({
        id: gallery.id,
        ...values,
      });
      return;
    }
    addMutation.mutate(values);
  });

  const isPending = addMutation.isPending || updateMutation.isPending;

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}>
      <DialogTrigger
        className={className}
        asChild={asChild}>
        {children}
      </DialogTrigger>
      <Form {...form}>
        <form>
          <DialogContent
            withCloseButton={false}
            className="p-0 md:max-w-2xl">
            <DialogHeader className="sr-only">
              <DialogTitle>Create Gallery</DialogTitle>
            </DialogHeader>
            <ScrollArea className="flex flex-col max-h-[80dvh]">
              <section className="space-y-6 p-6">
                <SelectInput
                  variant={"muted"}
                  size={"lg"}
                  control={form.control}
                  name="mediaType"
                  label="Media Type"
                  options={[
                    {
                      label: "Image",
                      value: "picture",
                    },
                    {
                      label: "Video",
                      value: "video",
                    },
                  ]}
                />
                <FileInput
                  control={form.control}
                  name="media"
                  label="Media"
                  mediaType={mediaType}
                  required
                  onUploadingChange={setIsUploading}
                  defaultImage={gallery?.media}
                  fileUploadDescription="Recommended size: 1280x720px"
                />
                <HtmlInput
                  control={form.control}
                  name="description"
                  label="Description"
                />
              </section>
            </ScrollArea>
            <DialogFooter className="p-6 pt-0">
              <DialogClose
                type="button"
                disabled={isPending || isUploading}>
                Cancel
              </DialogClose>
              <Button
                onClick={onSubmit}
                disabled={isUploading || isPending}
                loading={isPending}
                variant={"muted"}>
                Save
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
      </Form>
    </Dialog>
  );
};
