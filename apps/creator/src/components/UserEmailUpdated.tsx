import { useMutation } from "@connectrpc/connect-query";
import { useNavigate } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { AuthService } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import { Input } from "@vtuber/ui/components/input";
import { Label } from "@vtuber/ui/components/label";
import { toast } from "sonner";

export const UserEmailUpdater = () => {
  const { getText } = useLanguage();
  const navigate = useNavigate();
  const { session } = useAuth();

  const { mutateAsync, isPending } = useMutation(
    AuthService.method.changeEmailVerification,
    {
      onSuccess: (data) => {
        toast.success(data.message);
        navigate({
          to: "/email/verify",
        });
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const onEmailUpdate = async () => {
    await mutateAsync({});
  };

  return (
    <div className="py-14 bg-sub md:px-14 px-8 rounded-[24px] grid gap-y-20">
      <h3 className="text-[32px] font-mplus font-bold capitalize">
        {getText("email_address")}
      </h3>
      <div className="flex flex-col gap-y-3">
        <div className=" flex items-center justify-between">
          <Label
            htmlFor="email"
            className="text-font font-medium text-2xl capitalize">
            {getText("registered_email_address")}
          </Label>
          <Button
            onClick={onEmailUpdate}
            loading={isPending}
            type="submit"
            variant={"success"}
            className="rounded-full">
            {getText("change")}
          </Button>
        </div>
        <Input
          disabled
          id="email"
          value={session?.user?.email}
          name="email"
          size={"lg"}
          variant={"material"}
          placeholder="<EMAIL>"
        />
      </div>
    </div>
  );
};
