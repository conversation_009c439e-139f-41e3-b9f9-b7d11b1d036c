import { useMutation, useQuery } from "@connectrpc/connect-query";
import { VtuberProfilesService } from "@vtuber/services/vtubers";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { Spinner } from "@vtuber/ui/components/spinner";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export function CreatorAccessCard() {
  const { data, isPending, refetch } = useQuery(
    VtuberProfilesService.method.getMyVtuberAccessRequests,
  );

  const form = useForm({
    defaultValues: {
      description: data?.description || "",
    },
  });

  const mutation = useMutation(VtuberProfilesService.method.applyVtuberAccess, {
    onSuccess: () => {
      toast.success(
        (!data ? "Application submitted" : "Request updated") + " successfully",
      );
      refetch();
    },

    onError: (err) => {
      toast.error(err.message);
    },
  });

  const updateVtuberAccessRequestMutation = useMutation(
    VtuberProfilesService.method.updateVtuberAccessRequest,
    {
      onSuccess: () => {
        toast.success("Request updated successfully");
        refetch();
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const onSubmit = form.handleSubmit((val) => {
    if (!data) {
      mutation.mutateAsync(val);
    } else {
      updateVtuberAccessRequestMutation.mutateAsync(val);
    }
  });

  if (isPending) {
    return <Spinner className="w-14 h-14 text-primary" />;
  }

  const renderForm = !data || data.status === "rejected";
  return (
    <Card className="max-w-3xl w-full">
      <CardHeader className="text-center">
        <CardTitle className="text-xl">
          {renderForm
            ? "Apply For Creator Access"
            : "Application waiting for Approval"}
        </CardTitle>
        <CardDescription>
          {data
            ? data.status != "rejected"
              ? "Your application is pending approval. We will get back to you soon. Please do not submit multiple applications."
              : "Your application was rejected. Please update your application to retry."
            : "You have not applied for creator access yet. Apply now to get started."}
        </CardDescription>
        {data?.status === "rejected" && (
          <CardDescription className="text-red-500">
            Reason: {data.reason}
          </CardDescription>
        )}
      </CardHeader>
      {renderForm && (
        <CardContent>
          <Form {...form}>
            <form
              onSubmit={onSubmit}
              className="space-y-4">
              <TextAreaInput
                control={form.control}
                name="description"
                label="Description"
                rows={4}
                placeholder="Tell us about your channel and why you want to become a creator"
              />

              <Button
                loading={mutation.isPending}
                className="w-full">
                {data ? "Update Application" : "Apply Now"}
              </Button>
            </form>
          </Form>
        </CardContent>
      )}
    </Card>
  );
}
