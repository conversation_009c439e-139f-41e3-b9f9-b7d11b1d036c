import { useMutation } from "@connectrpc/connect-query";
import { useQueryClient } from "@tanstack/react-query";
import {
  AddVtuberBannerRequest,
  VtuberBannerService,
} from "@vtuber/services/vtubers";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Form } from "@vtuber/ui/components/form";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { Plus } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export const BannerPicker = ({ length }: { length: number }) => {
  const [opened, setOpened] = useState(false);
  const queryClient = useQueryClient();
  const form = useForm<AddVtuberBannerRequest>({
    defaultValues: {},
  });

  const mutation = useMutation(VtuberBannerService.method.addVtuberBanner, {
    onError: (err) => {
      toast.error(err.message);
    },
    onSuccess: () => {
      toast.success("Banner added successfully");
      queryClient.invalidateQueries({
        queryKey: ["vtuber_banner"],
      });
      setOpened(false);
    },
  });

  const onAdd = form.handleSubmit((values) => {
    if (length >= 5) {
      toast.error("You can only have 5 banners");
      return;
    }
    mutation.mutateAsync(values);
  });

  return (
    <Dialog
      open={opened}
      onOpenChange={(o) => {
        if (length >= 5) {
          toast.info("You can only have 5 banners");
          return;
        }
        setOpened(o);
      }}>
      <DialogTrigger>
        <AspectRatio ratio={16 / 9}>
          <div className="bg-black/20 items-center justify-center flex size-full gap-3">
            <Plus />
            Add Banner
          </div>
        </AspectRatio>
      </DialogTrigger>
      <Form {...form}>
        <form>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Banner</DialogTitle>
            </DialogHeader>
            <FileInput
              disabled
              control={form.control}
              name="image"
              fileUploadDescription="Recommended size: 1280x720px"
            />
            <DialogFooter>
              <DialogClose type="button">Cancel</DialogClose>
              <Button
                onClick={onAdd}
                loading={mutation.isPending}
                variant={"muted"}>
                Add
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
      </Form>
    </Dialog>
  );
};
