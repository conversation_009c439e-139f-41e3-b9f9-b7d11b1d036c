import { useMutation } from "@connectrpc/connect-query";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { AuthService, GetSessionResponse } from "@vtuber/services/users";
import { FileUploader } from "@vtuber/ui/components/file-uploader";
import { PersonIcon } from "@vtuber/ui/components/icons/person-icon";
import { Label } from "@vtuber/ui/components/label";
import { NavigationBlocker } from "@vtuber/ui/components/navigation-blocker";
import { useState } from "react";
import { toast } from "sonner";

export const UserImageUpdater = () => {
  const { getText } = useLanguage();
  const { session, setSesssion } = useAuth();
  const [userImage, setUserImage] = useState(session?.user?.image);

  const { mutateAsync, isPending } = useMutation(
    AuthService.method.updateUserImage,
    {
      onSuccess: (data) => {
        setUserImage(data.message);
        toast.success(getText("profile_image_success_message"));
        setSesssion(
          (prev) =>
            ({
              ...prev,
              user: {
                ...prev?.user,
                image: data.message,
              },
            }) as GetSessionResponse,
        );
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  return (
    <div className="gap-y-3 grid">
      <NavigationBlocker condition={isPending}>
        <p>{getText("please_wait_for_the_file_to_finish_uploading")}</p>
      </NavigationBlocker>
      <Label className="text-2xl font-bold">{getText("profile_picture")}</Label>
      <FileUploader
        className="rounded-none border-solid border-font p-0 [&>div]:p-0 py-1.5 px-2"
        placeholder={
          <div className="flex flex-col gap-y-2">
            <div className="bg-[#555367] rounded-[3px] h-[123px] w-[184px] mx-auto flex items-center justify-center px-4 py-2">
              <PersonIcon className="[&>path]:stroke-gray01" />
            </div>
            <div className="text-sm font-medium text-sub text-center border border-dashed border-font bg-[#555367] rounded-[3px] py-2 px-4 w-full">
              <p>
                {getText("upload_image")}
                <br /> {getText("profile_picture_ratio")}
              </p>
            </div>
          </div>
        }
        onUpload={async (image) => {
          mutateAsync({
            image,
          });
        }}
        defaultImage={userImage}
      />
    </div>
  );
};
