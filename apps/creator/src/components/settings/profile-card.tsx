import { useAuth } from "@vtuber/auth/hooks";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { ExternalLinks } from "@vtuber/ui/components/external-links";
import { Image } from "@vtuber/ui/components/image";

export const ProfileCard = ({ className }: { className?: string }) => {
  const { session: user } = useAuth();
  const vtuber = user?.vtuber;
  return (
    <div className={className}>
      <Card className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl overflow-hidden">
        <CardContent className="p-6">
          <div className="space-y-4">
            <AspectRatio
              ratio={16 / 9}
              className="rounded-2xl overflow-hidden border-2 border-sub relative">
              <Image
                src={vtuber?.bannerImage}
                alt="Banner preview"
                className="size-full object-cover rounded-2xl"
              />
            </AspectRatio>

            <div className="relative px-2">
              <div className="size-20 -translate-y-14 ml-4">
                <Image
                  src={vtuber?.image}
                  alt={vtuber?.displayName}
                  className="object-cover size-full rounded-2xl border-2 border-sub"
                />
              </div>

              <div className="space-y-3">
                <div>
                  <h4 className=" font-bold text-lg">{vtuber?.displayName}</h4>
                  <p className="text-muted-foreground">@{vtuber?.furigana}</p>
                </div>

                <p className="text-font text-sm leading-relaxed">
                  {vtuber?.description}
                </p>
                <ExternalLinks
                  className="pt-10"
                  socialMediaLinks={vtuber?.socialMediaLinks}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
