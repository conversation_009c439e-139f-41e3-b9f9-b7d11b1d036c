import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation } from "@connectrpc/connect-query";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService, GetSessionResponse } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import { Form } from "@vtuber/ui/components/form";
import { DOBInput } from "@vtuber/ui/components/form-inputs/date-of-birth-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { Tag } from "@vtuber/ui/components/tag";
import { toast } from "@vtuber/ui/components/toaster";
import { checkDob, convertToTimestamp } from "@vtuber/ui/lib/utils";
import { useForm } from "react-hook-form";
import { UserImageUpdater } from "./UserImageUpdater";

export const AccountForm = () => {
  const { session, setSesssion } = useAuth();
  const user = session?.user;
  const { getText } = useLanguage();

  const form = useForm({
    defaultValues: {
      fullName: user?.fullName ?? "",
      dateOfBirth: user?.dob ? timestampDate(user?.dob).toISOString() : "",
    },
  });

  const mutation = useMutation(AuthService.method.updateUserDetails, {
    onSuccess: (data, { fullName, dateOfBirth }) => {
      toast.success(data.message);
      setSesssion(
        (prev) =>
          ({
            ...prev,
            user: {
              ...prev?.user,
              fullName,
              dateOfBirth,
            },
          }) as GetSessionResponse,
      );
    },
    onError: (err) => {
      handleConnectError(err, form);
    },
  });

  const onUpdate = form.handleSubmit((data) => {
    if (!!data.dateOfBirth) {
      const inValidDob = checkDob(data.dateOfBirth);
      if (inValidDob) {
        form.setError("dateOfBirth", {
          message: getText("invalid_dob"),
        });
        return;
      }
    }
    mutation.mutate({
      dateOfBirth: convertToTimestamp(data.dateOfBirth),
      fullName: data.fullName,
    });
  });

  return (
    <section className="grid gap-y-20 bg-sub py-14 md:px-14 px-8 rounded-[24px]">
      <h3 className="font-bold text-[32px] font-mplus">
        {getText("edit_profile")}
      </h3>
      <Form {...form}>
        <form className="space-y-8">
          <TextInput
            wrapperClassName="gap-y-3"
            label={
              <div className="text-font font-medium text-2xl flex items-center gap-x-3">
                <p>{getText("user_name")}</p>
                <Tag
                  variant={"destructive"}
                  className="rounded-[2px]">
                  {getText("required")}
                </Tag>
              </div>
            }
            id="user_name"
            control={form.control}
            name="fullName"
            size={"lg"}
            variant={"material"}
            placeholder="vsai"
          />
          <DOBInput
            type="separated"
            control={form.control}
            name="dateOfBirth"
            wrapperClassName="gap-y-3"
            size={"lg"}
            label={
              <div className="text-font font-medium text-2xl flex items-center gap-x-3">
                <p>{getText("date_of_birth")}</p>
                <Tag
                  variant={"destructive"}
                  className="rounded-[2px]">
                  {getText("required")}
                </Tag>
              </div>
            }
            variant={"material"}
          />
        </form>
      </Form>
      <UserImageUpdater />
      <Button
        onClick={onUpdate}
        loading={mutation.isPending}
        variant={"success"}
        size={"lg"}
        className="w-max rounded-[3px] capitalize">
        {getText("udpate_profile")}
      </Button>
    </section>
  );
};
