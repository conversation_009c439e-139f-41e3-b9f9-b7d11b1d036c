// src/components/creator-subscription-form.tsx
import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { VtuberPlan, VtuberPlanService } from "@vtuber/services/vtubers";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { HtmlInput } from "@vtuber/ui/components/form-inputs/html-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { FileText, InfoIcon, Save } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

type VtuberPlanFormFormProps = {
  defaultValues?: VtuberPlan;
  onSuccess?: () => void;
};

export function VtuberPlanForm({
  defaultValues,
  onSuccess,
}: VtuberPlanFormFormProps) {
  const router = useRouter();
  const { getText } = useLanguage();

  const form = useForm({
    defaultValues: {
      price: defaultValues?.price,
      description: defaultValues?.description,
      title: defaultValues?.title,
      index: defaultValues?.index,
      shortDescription: defaultValues?.shortDescription,
      annualPrice: defaultValues?.annualPrice,
    },
  });

  const addMutation = useMutation(VtuberPlanService.method.addVtuberPlan, {
    onError: (err) => {
      handleConnectError(err, form);
    },
    onSuccess: () => {
      toast.success("Plan added successfully");
      router.invalidate();
      onSuccess?.();
      router.navigate({
        to: "/plans",
      });
    },
  });

  const updateMutation = useMutation(
    VtuberPlanService.method.updateVtuberPlanById,
    {
      onSuccess: (data) => {
        data.message;
        toast.success(data.message);
        router.invalidate();
        onSuccess?.();
        router.navigate({
          to: "/plans",
        });
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const onSubmit = form.handleSubmit((values) => {
    if (defaultValues) {
      updateMutation.mutateAsync({
        ...values,
        id: defaultValues.id,
      });
      return;
    }
    addMutation.mutateAsync(values);
  });

  const isLoading = addMutation.isPending || updateMutation.isPending;

  return (
    <div className="mt-10 md:max-w-4xl w-full mx-auto">
      <Form {...form}>
        <form
          onSubmit={onSubmit}
          className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <InfoIcon className="w-5 h-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              <TextInput
                control={form.control}
                name="title"
                wrapperClassName="space-y-2"
                label={getText("Plan_Title") + "*"}
                required
                variant={"muted"}
                size={"lg"}
              />
              <div className="grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-6">
                <TextInput
                  control={form.control}
                  name="price"
                  type="number"
                  leftIcon={
                    <span className="text-muted-foreground">
                      {getText("yen")}
                    </span>
                  }
                  label={"Monthly price*"}
                  wrapperClassName="space-y-2"
                  required
                  variant={"muted"}
                  size={"lg"}
                />
                <TextInput
                  control={form.control}
                  name="annualPrice"
                  type="number"
                  leftIcon={
                    <span className="text-muted-foreground">
                      {getText("yen")}
                    </span>
                  }
                  label={"Annual price*"}
                  wrapperClassName="space-y-2"
                  required
                  variant={"muted"}
                  size={"lg"}
                />
                <TextInput
                  control={form.control}
                  name="index"
                  type="number"
                  label={getText("Display_Order") + "*"}
                  wrapperClassName="space-y-2"
                  required
                  variant={"muted"}
                  size={"lg"}
                />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="w-5 h-5" />
                Descriptions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <TextAreaInput
                variant={"muted"}
                maxLength={255}
                name="shortDescription"
                control={form.control}
                label={getText("short_description") + "*"}
                wrapperClassName="space-y-2"
                required
                defaultValue={defaultValues?.shortDescription}
              />
              <HtmlInput
                wrapperClassName="space-y-2"
                control={form.control}
                name="description"
                label={"Full Description*"}
                initialHtml={defaultValues?.description}
              />
            </CardContent>
          </Card>
          <Button
            variant={"success"}
            className="w-full"
            type="submit"
            size={"xl"}
            loading={isLoading}>
            <Save className="mr-1" />
            {getText("submit")}
          </Button>
        </form>
      </Form>
    </div>
  );
}
