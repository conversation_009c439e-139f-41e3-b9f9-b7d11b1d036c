import { Link } from "@tanstack/react-router";
import { buttonVariants } from "@vtuber/ui/components/button";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { cn } from "@vtuber/ui/lib/utils";
import { ArrowRight, FileText, Plus, Sparkles } from "lucide-react";

export const NoPlansMessage = () => {
  return (
    <div className="min-h-[90dvh] flex items-center justify-center">
      <div className="max-w-md w-full">
        <Card className="border-dashed border-2 bg-white/10 border-white backdrop-blur">
          <CardContent className="p-8 text-center">
            <div className="relative mb-6">
              <div className="w-20 h-20 mx-auto bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                <FileText className="w-10 h-10 text-white" />
              </div>
              <div className="absolute -top-1 -right-1">
                <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center animate-pulse">
                  <Sparkles className="w-3 h-3 text-yellow-800" />
                </div>
              </div>
            </div>

            <h3 className="text-2xl font-bold text-sub  mb-3">No Plans Yet</h3>

            <p className="text-muted-foreground mb-6 leading-relaxed">
              Get started by creating your first VTuber plan. You can set up
              pricing, display orders, and manage all your content in one place.
            </p>

            <Link
              to="/plans/add"
              className={cn(
                buttonVariants({
                  size: "xl",
                  variant: "add",
                }),
                "rounded-full w-full",
              )}>
              <Plus className="w-5 h-5 mr-2" />
              Create Your First Plan
              <ArrowRight className="w-4 h-4 ml-2" />
            </Link>

            <p className="text-xs text-slate-500 dark:text-slate-500 mt-4">
              Need help? Check out our guide on setting up VTuber plans
            </p>
          </CardContent>
        </Card>

        <div className="mt-12 grid grid-cols-1 sm:grid-cols-2 gap-4 text-left">
          <div className="bg-slate-800/50 p-4 rounded-lg border border-slate-700">
            <h3 className="font-medium text-white mb-1 flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              Quick Setup
            </h3>
            <p className="text-xs text-slate-400">
              Create plans in just a few clicks
            </p>
          </div>
          <div className="bg-slate-800/50 p-4 rounded-lg border border-slate-700">
            <h3 className="font-medium text-white mb-1 flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              Easy Management
            </h3>
            <p className="text-xs text-slate-400">Edit and organize anytime</p>
          </div>
        </div>
      </div>
    </div>
  );
};
