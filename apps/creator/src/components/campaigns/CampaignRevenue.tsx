import { useLanguage } from "@vtuber/language/hooks";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { ArrowUpRight, BarChart2, DollarSign } from "lucide-react";

export function CampaignRevenue() {
  const revenueData = {
    current: 12458.32,
    change: 12.5, // percentage
    target: 15000,
    lastMonth: 11042.75,
  };
  const { getText } = useLanguage();
  return (
    <Card className="border-gray-800 bg-gradient-3">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {getText("Total_Revenue")}
        </CardTitle>
        <DollarSign className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          ${revenueData.current.toLocaleString()}
        </div>
        <div className="flex items-center pt-2">
          <div className="flex items-center text-xs text-green-600">
            <ArrowUpRight className="h-3 w-3" />
            <span>{revenueData.change}%</span>
          </div>
          <span className="ml-1 text-xs text-muted-foreground">
            {getText("Compared_To_Last_Month")} ($
            {revenueData.lastMonth.toLocaleString()})
          </span>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm">
            <BarChart2 className="h-4 w-4 opacity-70" />
            <span className="text-muted-foreground">{getText("Target")}:</span>
            <span>${revenueData.target.toLocaleString()}</span>
          </div>
          <div className="flex items-center">
            <div className="h-2 w-24 rounded-full bg-gray-200">
              <div
                className="h-2 rounded-full bg-green-600"
                style={{
                  width: `${Math.min(100, (revenueData.current / revenueData.target) * 100)}%`,
                }}
              />
            </div>
            <span className="ml-2 text-xs text-muted-foreground">
              {Math.round((revenueData.current / revenueData.target) * 100)}%
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
