import { useQuery } from "@connectrpc/connect-query";
import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import {
  CampaignService,
  GetCampaignById,
  SubscriberResponse,
} from "@vtuber/services/campaigns";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Button } from "@vtuber/ui/components/button";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { ColumnDef, DataTable } from "@vtuber/ui/components/data-table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@vtuber/ui/components/dialog";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { ScrollArea, ScrollBar } from "@vtuber/ui/components/scroll-area";
import {
  Tabs,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@vtuber/ui/components/tabs";
import { useState } from "react";
import {
  CartesianGrid,
  <PERSON>,
  <PERSON>Chart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>Axis,
} from "recharts";

const dummyAnalyticsData = [
  { date: "Jan", views: 400, likes: 120 },
  { date: "Feb", views: 600, likes: 200 },
  { date: "Mar", views: 800, likes: 320 },
  { date: "Apr", views: 750, likes: 280 },
  { date: "May", views: 900, likes: 360 },
  { date: "Jun", views: 1100, likes: 450 },
];

export function CampaignTab({
  id,
  campaign,
}: {
  id: string;
  campaign: GetCampaignById;
}) {
  const { data: subscribers, isPending: loadingSubscribers } = useQuery(
    CampaignService.method.getSubscribers,
    {
      campaignId: campaign?.id,
      pagination: {
        size: 10,
        order: "desc",
        sort: "created_at",
      },
    },
    {
      enabled: !!campaign?.id,
    },
  );

  const { getText } = useLanguage();

  const columns: ColumnDef<SubscriberResponse["data"][0]>[] = [
    {
      accessorKey: "image",
      header: "Image",
      cell: ({ row }) => (
        <Avatar
          src={row.original.image}
          className="h-10 w-10"
        />
      ),
    },
    {
      accessorKey: "name",
      header: "User Name",
      cell: ({ row }) => {
        return <p>{row.original.name}</p>;
      },
    },

    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }) => {
        return <p>¥ {row.original.amount}</p>;
      },
    },
    {
      accessorKey: "campaignVariantTitle",
      header: "Variant Title",
      cell: ({ row }) => {
        const variantId = row.original.campaignVariantId;
        if (!variantId) {
          return <p>No variant</p>;
        }
        return (
          <Link
            to="/campaigns/variant/$variantId"
            params={{ variantId: variantId.toString() }}>
            <p className="underline">{row.original.campaignVariantTitle}</p>
          </Link>
        );
      },
    },
    {
      accessorKey: "comment",
      header: "Comment",
      cell: ({ row }) => {
        const [isModalOpen, setIsModalOpen] = useState(false);
        const comment = row.original.comment || "";
        return (
          <>
            <div className="flex items-center gap-2">
              {comment && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsModalOpen(true)}>
                  View Comment
                </Button>
              )}
            </div>

            <Dialog
              open={isModalOpen}
              onOpenChange={setIsModalOpen}>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Comment from {row.original.name}</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  <div className=" p-4 rounded-lg">
                    <p className="text-sm leading-relaxed whitespace-pre-wrap">
                      {comment}
                    </p>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Tabs defaultValue="Project Inroduction">
        <ScrollArea>
          <ScrollBar orientation="horizontal" />
          <TabsList className="mb-4 grid grid-cols-3 w-full">
            <TabsTrigger value="Project Inroduction">
              {getText("project_introduction")}
            </TabsTrigger>
            <TabsTrigger value="analytics">
              {getText("Campaign_Analytics")}
            </TabsTrigger>
            <TabsTrigger value="supporters">
              {getText("supporters")}
            </TabsTrigger>
          </TabsList>
        </ScrollArea>
        <TabsContent value="Project Inroduction">
          <div className="space-y-3">
            <section className="space-y-1">
              <h3 className="text-4xl text-primary mb-4 font-semibold">
                {getText("description")}:{" "}
              </h3>
              <MarkDown
                className="text-font"
                markdown={campaign.description}
              />
            </section>
            <section className="space-y-1">
              <h3 className="text-4xl mt-4 text-primary mb-4 font-semibold">
                {getText("short_description")}:{" "}
              </h3>
              <p className="text-font mb-2">{campaign.shortDescription}</p>
            </section>
            <section className="space-y-1">
              <h3 className="text-4xl text-primary mb-4 font-semibold">
                {getText("Promotional_Message")}:{" "}
              </h3>
              <p className="text-font">{campaign.promotionalMessage}</p>
            </section>
          </div>
        </TabsContent>
        <TabsContent value="analytics">
          <Card className="p-6 shadow-md rounded-lg  mt-4">
            <CardContent>
              <h2 className="text-xl font-bold mb-4">
                {getText("Campaign_Analytics")}
              </h2>

              <div className="w-full h-64">
                <ResponsiveContainer
                  width="100%"
                  height="100%">
                  <LineChart data={dummyAnalyticsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      stroke="#8884d8"
                    />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="views"
                      stroke="#4f46e5"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="likes"
                      stroke="#22c55e"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="supporters">
          {loadingSubscribers ? (
            <div className="flex items-center justify-center h-40">
              {getText("loading")}...
            </div>
          ) : !subscribers || subscribers.data.length === 0 ? (
            <div>
              <div className="text-center py-12 bg-sub rounded-lg">
                <p className="text-muted-foreground">
                  {getText("No_Subscribers_Yet")}
                </p>
              </div>
            </div>
          ) : (
            <div className="grid gap-y-10">
              <DataTable
                columns={columns}
                data={subscribers.data || []}
                pagination={{
                  currentPage: subscribers?.paginationDetails?.currentPage,
                  totalPage: subscribers?.paginationDetails?.totalPages || 1,
                }}
              />
            </div>
          )}
        </TabsContent>
      </Tabs>
    </>
  );
}
