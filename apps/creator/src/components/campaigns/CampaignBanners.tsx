import { GetCampaignById } from "@vtuber/services/campaigns";
import {
  Carousel,
  CarouselContent,
  CarouselIndicators,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@vtuber/ui/components/carousel";
import { Image } from "@vtuber/ui/components/image";

export const CampaignDetailsBanner = ({
  banners,
}: {
  banners: GetCampaignById["banners"];
}) => {
  return (
    <Carousel>
      <CarouselContent className="sm:h-[60vh] h-60">
        {banners?.map((img: any, i: number) => (
          <CarouselItem key={img.image + i.toString()}>
            <Image
              src={img.image || ""}
              alt="campaign-banner"
              className="h-full rounded-xl w-full"
            />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselNext className="right-3 sm:size-20 size-12 [&>svg]:h-8 [&>svg]:w-8" />
      <CarouselPrevious className="left-3 [&>svg]:h-8 [&>svg]:w-8 sm:size-20 size-12" />
      <CarouselIndicators variant="bar" />
    </Carousel>
  );
};
