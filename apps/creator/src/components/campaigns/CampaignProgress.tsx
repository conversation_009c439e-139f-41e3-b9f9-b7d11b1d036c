import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useLanguage } from "@vtuber/language/hooks";
import { GetCampaignById } from "@vtuber/services/campaigns";
import { Progress } from "@vtuber/ui/components/progress";

export function CampaignProgress({ campaign }: { campaign: GetCampaignById }) {
  const { getText } = useLanguage();

  const fundingPercentage = Number(
    ((campaign.totalRaised / campaign.totalBudget) * 100).toFixed(2),
  );

  const timeRemaning = campaign.endDate
    ? Math.ceil(
        (timestampDate(campaign.endDate).getTime() - Date.now()) /
          (1000 * 60 * 60 * 24),
      )
    : 0;
  return (
    <div
      className={`border border-gray-800 bg-gradient-3 rounded-lg p-4 shadow-sm `}>
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-semibold text-lg">
          {getText("Campaign_Progress")}
        </h3>
        <span className="text-sm font-medium px-3 py-1  rounded-full">
          {timeRemaning} days Remaining
        </span>
      </div>

      <div className="mt-4">
        <div className="flex justify-between text-sm mb-1">
          <span className="font-medium">{getText("Funding_Progress")}</span>
          <span className="font-bold">{fundingPercentage}%</span>
        </div>

        <Progress
          value={fundingPercentage}
          className="h-2"
        />

        <div className="flex justify-between mt-2 text-sm">
          <span>
            {getText("Raised")}: {campaign.totalRaised.toLocaleString()}{" "}
            {getText("yen")}
          </span>
          <span>
            {getText("Goal")}: {campaign.totalBudget.toLocaleString()}{" "}
            {getText("yen")}
          </span>
        </div>
      </div>

      <div className=" p-3 rounded-lg">
        <p className="text-xs">
          {timeRemaning ? getText("end_date") : "ended"}
        </p>
        <p>
          {timeRemaning
            ? timestampDate(campaign.endDate!).toLocaleDateString()
            : "N/A"}
        </p>
      </div>
    </div>
  );
}
