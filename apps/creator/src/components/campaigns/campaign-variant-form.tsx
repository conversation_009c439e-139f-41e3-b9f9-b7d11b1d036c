import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { CampaignVariant, GetCampaignById } from "@vtuber/services/campaigns";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Image } from "@vtuber/ui/components/image";
import { RoutePendingComponent } from "@vtuber/ui/components/route-pending-component";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { ArrowLeft, CheckCircle, Columns2, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { campaignVariantQueryOptions } from "~/utils/api";
import { CampaignVariantActions } from "./campaign-variant-actions";
import { CampaignVariantPicker } from "./campaign-variant-picker";

interface Props {
  onPrev?: () => void;
  campaign?: GetCampaignById;
  isEditMode?: boolean;
  campaignId: string;
  queryEnabled?: boolean;
}

export const CampaignVariantForm = ({
  onPrev,
  campaign,
  isEditMode = false,
  campaignId: id,
  queryEnabled = true,
}: Props) => {
  const campaignId = campaign?.id || id;
  const { data, isPending } = useQuery(
    campaignVariantQueryOptions(String(campaignId), {
      enabled: !!campaignId && queryEnabled,
    }),
  );
  const campaignVariants = data?.data || [];
  const [variants, setVariants] = useState<CampaignVariant[]>(
    campaignVariants || [],
  );
  const navigate = useNavigate();

  useEffect(() => {
    setVariants(campaignVariants || []);
  }, [campaignVariants, campaignId]);

  if (isPending) return <RoutePendingComponent />;

  return (
    <div className="pt-10">
      <Card className="md:max-w-4xl w-full mx-auto">
        <CardHeader className={"flex-row items-center"}>
          <CardTitle className="flex items-center gap-2 text-2xl">
            <Columns2 className="size-5 mt-1" />
            Campaign Variants
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            {variants.length > 0 ? (
              <div className="grid md:grid-cols-3 grid-cols-2 gap-6">
                {variants
                  .sort((a, b) => a.displayOrder - b.displayOrder)
                  .map((v) => (
                    <AspectRatio
                      key={v.id}
                      ratio={288 / 208}
                      className="rounded-lg overflow-hidden relative group">
                      <Image
                        src={getCdnUrl(v.image)}
                        alt={v.title}
                        className="size-full rounded-lg"
                      />
                      <CampaignVariantActions
                        variant={v}
                        campaign={campaign}
                        campaignId={String(campaignId)}
                      />
                    </AspectRatio>
                  ))}
                <CampaignVariantPicker
                  campaign={campaign}
                  campaignId={String(campaignId)}
                  asChild>
                  <AspectRatio
                    ratio={768 / 512}
                    className="rounded-lg overflow-hidden border-dashed border flex items-center justify-center">
                    <Button
                      variant={"ghost"}
                      className="size-full">
                      <Plus className="mr-2 size-5" /> Add More
                    </Button>
                  </AspectRatio>
                </CampaignVariantPicker>
              </div>
            ) : (
              <CampaignVariantPicker
                campaign={campaign}
                campaignId={String(campaignId)}
                asChild
                className="w-full mt-12 mb-4">
                <Button
                  size={"xl"}
                  variant={"ghost"}
                  className="border-dashed border-2 rounded-lg">
                  <Plus className="mr-2 size-5" /> Add New Variant
                </Button>
              </CampaignVariantPicker>
            )}
          </div>
        </CardContent>
        <CardFooter className="gap-3">
          {onPrev && (
            <Button
              variant={"accent"}
              onClick={onPrev}
              size={"xl"}
              className="w-full">
              <ArrowLeft className="mr-2 size-5" />
              Previous
            </Button>
          )}
          {!isEditMode && (
            <Button
              variant={"success"}
              onClick={() => {
                if (!!campaign?.id) {
                  navigate({
                    to: "/campaigns/$id",
                    params: {
                      id: campaign?.id?.toString(),
                    },
                  });
                  return;
                }
                navigate({
                  to: "/campaigns",
                });
              }}
              size={"xl"}
              className="w-full">
              <CheckCircle className="mr-2 size-5" />
              Done
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};
