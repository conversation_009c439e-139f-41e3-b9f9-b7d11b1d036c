import { useQueryClient } from "@tanstack/react-query";
import {
  CampaignVariant,
  GetAllCampaignVariantsResponse,
  GetCampaignById,
} from "@vtuber/services/campaigns";
import { campaignVariantClient } from "@vtuber/services/client";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import { DeleteDialog } from "@vtuber/ui/components/DeleteDialog";
import { MediaModal } from "@vtuber/ui/components/media-modal";
import { truncateString } from "@vtuber/ui/lib/truncate-string";
import { cn, getCdnUrl } from "@vtuber/ui/lib/utils";
import { Edit, Eye, Trash } from "lucide-react";
import { campaignVariantQueryOptions } from "~/utils/api";
import { CampaignVariantPicker } from "./campaign-variant-picker";

interface Props {
  variant: CampaignVariant;
  campaign?: GetCampaignById;
  campaignId: string;
}

export const CampaignVariantActions = ({
  variant,
  campaign,
  campaignId,
}: Props) => {
  const queryClient = useQueryClient();
  const queryKey = campaignVariantQueryOptions(String(campaignId), {}).queryKey;

  return (
    <div
      className={cn(
        "absolute inset-0 bg-black/50 transition-all ease-in-out duration-300 flex items-center justify-center",
        "hover:backdrop-blur",
      )}>
      <div
        className={cn("flex flex-col items-center gap-y-4 group-hover:hidden")}>
        <h3 className="text-xl font-medium">
          {variant.title.length > 13
            ? truncateString(variant.title, 13) + "..."
            : variant.title}
        </h3>
        <div className="flex items-center gap-x-3">
          <Badge variant={"secondary"}>Subs: {variant.maxSub}</Badge>
          <Badge variant={"secondary"}>Price: {variant.price}</Badge>
        </div>
      </div>

      <div
        className={cn(
          "group-hover:flex items-center justify-center gap-x-3 hidden",
        )}>
        <MediaModal
          currentSrc={getCdnUrl(variant?.image)}
          mediaType="picture">
          <Button
            className="rounded-full size-12"
            variant={"accent"}
            size={"icon"}>
            <Eye />
          </Button>
        </MediaModal>
        <CampaignVariantPicker
          campaign={campaign}
          campaignId={campaignId}
          asChild
          variant={variant}>
          <Button
            variant={"accent"}
            className="rounded-full size-12"
            size={"icon"}>
            <Edit />
          </Button>
        </CampaignVariantPicker>
        <DeleteDialog
          name={variant.title}
          onDelete={() => {
            campaignVariantClient
              .deleteCampaignVariantById({
                id: variant.id,
              })
              .then(() => {
                queryClient.setQueryData(queryKey, (old) => {
                  if (old?.data) {
                    return {
                      ...old,
                      data: old.data.filter((v) => v.id !== variant.id),
                    } as GetAllCampaignVariantsResponse;
                  }
                  return old;
                });
              });
          }}>
          <Button
            className="rounded-full size-12"
            variant={"destructive"}
            size={"icon"}>
            <Trash />
          </Button>
        </DeleteDialog>
      </div>
    </div>
  );
};
