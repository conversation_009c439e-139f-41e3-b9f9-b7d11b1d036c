import { useMutation } from "@connectrpc/connect-query";
import { useQueryClient } from "@tanstack/react-query";
import { useLanguage } from "@vtuber/language/hooks";
import {
  CampaignVariant,
  CampaignVariantService,
  GetAllCampaignVariantsResponse,
  GetCampaignById,
} from "@vtuber/services/campaigns";
import { handleConnectError } from "@vtuber/services/client";
import { AlertDialogTriggerProps } from "@vtuber/ui/components/alert-dialog";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Form } from "@vtuber/ui/components/form";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { HtmlInput } from "@vtuber/ui/components/form-inputs/html-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { Users } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { campaignVariantQueryOptions } from "~/utils/api";

type Props = AlertDialogTriggerProps & {
  variant?: CampaignVariant;
  campaign?: GetCampaignById;
  campaignId: string;
};

export function CampaignVariantPicker({
  variant,
  campaign,
  campaignId,
  ...props
}: Props) {
  const queryClient = useQueryClient();
  const queryKey = campaignVariantQueryOptions(String(campaignId), {}).queryKey;
  const { getText } = useLanguage();
  const [opened, setOpened] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const form = useForm<CampaignVariant>({
    defaultValues: {
      title: variant?.title,
      image: variant?.image,
      price: variant?.price,
      maxSub: variant?.maxSub,
      description: variant?.description,
      displayOrder: variant?.displayOrder,
    },
  });

  const updateVariantMutation = useMutation(
    CampaignVariantService.method.updateCampaignVariantById,
    {
      onSuccess: (data, variables) => {
        queryClient.setQueryData(queryKey, (old) => {
          if (old?.data) {
            return {
              ...old,
              data: old.data.map((v) => {
                if (v.id === variables.id) {
                  return variables;
                }
                return v;
              }),
            } as GetAllCampaignVariantsResponse;
          }
          return old;
        });
        toast.success(data.message);
        setOpened(false);
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const addVariantMutation = useMutation(
    CampaignVariantService.method.addCampaignVariant,
    {
      onSuccess: (data) => {
        toast.success("Variant added to campaign successfully");
        setOpened(false);
        queryClient.setQueryData(queryKey, (old) => {
          if (old?.data) {
            return {
              ...old,
              data: [...old.data, data.data],
            } as GetAllCampaignVariantsResponse;
          }
          return old;
        });
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const onSubmit = form.handleSubmit((values) => {
    if (variant) {
      updateVariantMutation.mutate({
        description: values.description,
        id: variant.id,
        image: values.image,
        maxSub: values.maxSub,
        price: values.price,
        title: values.title,
        displayOrder: values.displayOrder,
      });
      form.clearErrors();
      return;
    }
    addVariantMutation.mutate({
      campaignId: campaign?.id,
      description: values.description,
      image: values.image,
      maxSub: values.maxSub,
      price: values.price,
      title: values.title,
      displayOrder: values.displayOrder,
    });
  });

  const isPending =
    updateVariantMutation.isPending || addVariantMutation.isPending;

  return (
    <Dialog
      open={opened}
      onOpenChange={setOpened}>
      <DialogTrigger {...props} />
      <DialogContent
        withCloseButton={false}
        className="md:w-[40vw] max-w-[40vw] p-0">
        <DialogHeader className="sr-only">
          <DialogTitle>
            {variant ? getText("Update_Variant") : getText("Add_Variant")}
          </DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[80vh]">
          <Form {...form}>
            <div className="space-y-6 p-6 pb-8">
              <TextInput
                control={form.control}
                name="title"
                label={getText("Title")}
                variant={"muted"}
                wrapperClassName="space-y-2"
              />
              <TextInput
                leftIcon={"¥"}
                control={form.control}
                name="price"
                label={getText("price")}
                type="number"
                variant={"muted"}
                wrapperClassName="space-y-2"
              />
              <TextInput
                control={form.control}
                name="displayOrder"
                label={getText("Display_Order")}
                type="number"
                variant={"muted"}
                wrapperClassName="space-y-2"
              />
              <TextInput
                control={form.control}
                name="maxSub"
                leftIcon={<Users className="size-4" />}
                label={getText("Max_Sub")}
                type="number"
                variant={"muted"}
                wrapperClassName="space-y-2"
              />
              <HtmlInput
                control={form.control}
                name="description"
                label={getText("description")}
                wrapperClassName="space-y-2"
              />
              <FileInput
                control={form.control}
                name="image"
                label="Thumbnail"
                wrapperClassName="space-y-2"
                defaultImage={getCdnUrl(variant?.image)}
                onUploadingChange={setIsUploading}
                fileUploadDescription="Recommended size: 1280x720px"
              />
            </div>
          </Form>
        </ScrollArea>
        <DialogFooter className="flex flex-row gap-x-3 items-center p-6 border-t">
          <DialogClose
            disabled={isPending || isUploading}
            type="button">
            {getText("Cancel")}
          </DialogClose>
          <Button
            loading={isPending}
            disabled={isUploading || isPending}
            onClick={onSubmit}
            variant={"muted"}>
            {variant ? getText("Update_Variant") : getText("Add_Variant")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
