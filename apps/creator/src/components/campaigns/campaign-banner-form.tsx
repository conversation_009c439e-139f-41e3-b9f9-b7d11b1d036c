import { DndContext, closestCorners } from "@dnd-kit/core";
import {
  SortableContext,
  horizontalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Button, buttonVariants } from "@vtuber/ui/components/button";
import { RoutePendingComponent } from "@vtuber/ui/components/route-pending-component";

import { useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { GetCampaignById } from "@vtuber/services/campaigns";
import {
  ArrowLeft,
  ArrowRight,
  ArrowUpDown,
  CircleCheck,
  GalleryThumbnails,
  Plus,
  Save,
  X,
} from "lucide-react";
import { useCampaignReorder } from "~/hooks/use-campaign-reorder";
import { campaignBannerQueryOptions } from "~/utils/api";
import { CampaignBanner } from "./campaign-banner";
import { CampaignBannerPicker } from "./campaign-banner-picker";

interface Props {
  onNext?: (id?: string) => void;
  onPrev?: () => void;
  campaign?: GetCampaignById;
  campaignId: string;
  queryEnabled?: boolean;
}

export const CampaignBannerForm = ({
  onNext,
  onPrev,
  campaign,
  campaignId: id,
  queryEnabled = true,
}: Props) => {
  const campaignId = campaign?.id || id;

  const { data, isPending } = useQuery(
    campaignBannerQueryOptions(campaignId, {
      enabled: !!campaignId && queryEnabled,
    }),
  );

  const campaignBanners = data?.data || [];

  const {
    banners,
    editMode,
    handleCancel,
    handleEditModeToggle,
    onDragEnd,
    sortedBanners,
    isUpdating,
  } = useCampaignReorder({
    campaign,
    campaignBanners,
    campaignId: String(campaignId),
  });

  if (isPending) return <RoutePendingComponent />;

  return (
    <div className="pt-10 md:max-w-4xl w-full mx-auto space-y-10">
      {onNext && (
        <div className="flex justify-end">
          <Link
            to="/campaigns/$id"
            params={{
              id: campaignId.toString(),
            }}
            className={buttonVariants({
              variant: "ghost",
            })}>
            Skip
          </Link>
        </div>
      )}
      <div className="flex justify-center">
        <CampaignBannerPicker
          campaign={campaign}
          campaignId={String(campaignId)}
          index={banners.length > 0 ? banners.length + 1 : undefined}
        />
      </div>
      <section
        className={`flex items-center ${sortedBanners.length > 1 && "justify-between"}`}>
        <div className="flex items-center gap-2 text-2xl">
          <GalleryThumbnails className="size-5 mt-1" />
          <h1 className="text-2xl font-semibold text-font">Campaign Banners</h1>
        </div>
        <div className="flex items-center gap-x-3">
          {editMode && (
            <Button
              onClick={handleCancel}
              variant={"ghost"}
              disabled={isUpdating}>
              <X className="mr-2 size-4" />
              Cancel
            </Button>
          )}
          {sortedBanners.length > 1 && (
            <Button
              onClick={handleEditModeToggle}
              className={`rounded-lg ${editMode && "text-green-500"}`}
              variant={"dark"}
              loading={isUpdating}>
              {editMode ? (
                <CircleCheck className="mr-2 size-4" />
              ) : (
                <ArrowUpDown className="mr-2 size-4" />
              )}
              {editMode ? "Done" : "Rearrange order"}
            </Button>
          )}
        </div>
      </section>
      {sortedBanners.length > 0 ? (
        <DndContext
          collisionDetection={closestCorners}
          onDragEnd={onDragEnd}>
          <div className="grid md:grid-cols-3 grid-cols-2 gap-6">
            <SortableContext
              strategy={horizontalListSortingStrategy}
              items={sortedBanners.map((b) => b?.id.toString())}>
              {sortedBanners.map((b) => (
                <CampaignBanner
                  banner={b}
                  key={b.id}
                  editMode={editMode}
                  campaign={campaign}
                  campaignId={String(campaignId)}
                />
              ))}
            </SortableContext>
          </div>
        </DndContext>
      ) : (
        <CampaignBannerPicker
          campaign={campaign}
          asChild
          campaignId={String(campaignId)}
          index={banners.length > 0 ? banners.length + 1 : undefined}>
          <Button
            variant={"ghost"}
            className=" w-full border-dashed border-2"
            size={"xl"}>
            <Plus className="mr-2" /> Add Banners
          </Button>
        </CampaignBannerPicker>
      )}
      <div className="flex items-center gap-3">
        {onPrev && (
          <Button
            variant={"accent"}
            onClick={onPrev}
            size={"xl"}
            className="w-full">
            <ArrowLeft className="mr-2 size-5" />
            Previous
          </Button>
        )}
        {onNext && (
          <Button
            variant={"success"}
            onClick={() => onNext(campaign?.id.toString())}
            size={"xl"}
            className="w-full">
            {!onPrev && <Save className="size-5 mr-2" />}
            Next
            <ArrowRight className="ml-3 size-5" />
          </Button>
        )}
      </div>
    </div>
  );
};
