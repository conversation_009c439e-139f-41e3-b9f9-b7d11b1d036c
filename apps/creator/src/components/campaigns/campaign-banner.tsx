import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  CampaignBanner as CampaignBannerType,
  GetCampaignById,
} from "@vtuber/services/campaigns";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Image } from "@vtuber/ui/components/image";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { CampaignBannerActions } from "./campaign-banner-actions";

interface Props {
  banner: CampaignBannerType;
  editMode?: boolean;
  campaign?: GetCampaignById;
  campaignId: string;
}

export const CampaignBanner = ({
  banner,
  editMode = false,
  campaign,
  campaignId,
}: Props) => {
  const { listeners, setNodeRef, attributes, transform, transition } =
    useSortable({ id: banner.id.toString() });

  const style = {
    transition,
    transform: CSS.Transform.toString(transform),
  };

  if (editMode)
    return (
      <div
        ref={setNodeRef}
        {...attributes}
        {...listeners}
        style={style}>
        <AspectRatio
          ratio={768 / 512}
          className="rounded-lg overflow-hidden relative group">
          <div className="inset-0 absolute bg-black/50 z-50 flex items-center justify-center">
            <div className="size-12 rounded-full flex items-center justify-center bg-accent text-2xl font-semibold">
              {banner.index}
            </div>
          </div>
          <Image
            src={getCdnUrl(banner.image)}
            alt={banner.image}
            className="rounded-lg size-full"
          />
        </AspectRatio>
      </div>
    );

  return (
    <AspectRatio
      ratio={768 / 512}
      className="rounded-lg overflow-hidden relative group">
      <Image
        src={getCdnUrl(banner.image)}
        alt={banner.image}
        className="rounded-lg size-full"
      />
      <CampaignBannerActions
        banner={banner}
        campaign={campaign}
        campaignId={campaignId}
      />
    </AspectRatio>
  );
};
