import { useMutation } from "@connectrpc/connect-query";
import { useQueryClient } from "@tanstack/react-query";
import { useLanguage } from "@vtuber/language/hooks";
import {
  CampaignBanner,
  CampaignBannerService,
  GetBannerByCampaignIdResponse,
  GetCampaignById,
} from "@vtuber/services/campaigns";
import { handleConnectError } from "@vtuber/services/client";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Form } from "@vtuber/ui/components/form";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { Layout } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { campaignBannerQueryOptions } from "~/utils/api";

interface Props {
  banner?: CampaignBanner;
  children?: React.ReactNode;
  asChild?: boolean;
  className?: string;
  campaign?: GetCampaignById;
  index?: number;
  campaignId: string;
}

export function CampaignBannerPicker({
  banner,
  children,
  asChild = false,
  className,
  campaign,
  index,
  campaignId,
}: Props) {
  const [opened, setOpened] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<CampaignBanner>({
    defaultValues: {
      image: banner?.image,
      index: index ? index : banner?.index ? banner?.index + 1 : 1,
      id: banner?.id,
    },
  });

  useEffect(() => {
    if (!!index) {
      form.setValue("index", index);
    }
  }, [index]);

  const addMutation = useMutation(
    CampaignBannerService.method.addCampaignBanner,
    {
      onError: (err) => {
        handleConnectError(err, form);
      },
      onSuccess: (data, { index }) => {
        queryClient.setQueryData(
          campaignBannerQueryOptions(String(campaignId), { enabled: true })
            .queryKey,
          (old) => {
            if (old?.data) {
              return {
                ...old,
                data: [...old.data, data.data],
              } as GetBannerByCampaignIdResponse;
            }
            return old;
          },
        );
        form.reset({
          index: index ? index + 1 : 1,
        });
        toast.success("Banner added successfully");
        setOpened(false);
      },
    },
  );

  const updateMutation = useMutation(
    CampaignBannerService.method.updateCampaignBannerById,
    {
      onError: (err) => {
        handleConnectError(err, form);
      },
      onSuccess: (data) => {
        queryClient.invalidateQueries({
          queryKey: ["campaign_banner", String(campaignId)],
          exact: false,
        });

        toast.success(data.message);
        setOpened(false);
      },
    },
  );

  const handleSubmit = form.handleSubmit((values) => {
    if (banner) {
      updateMutation.mutate({
        id: banner.id,
        image: values.image,
        index: values.index,
      });
      return;
    }
    addMutation.mutate({
      image: values.image,
      index: values.index,
      campaignId: campaign?.id,
    });
  });
  const { getText } = useLanguage();
  const isPending = addMutation.isPending || updateMutation.isPending;
  return (
    <Dialog
      open={opened}
      onOpenChange={setOpened}>
      <DialogTrigger
        asChild={asChild}
        className={className}>
        {children || (
          <div className="border-2 border-dashed border-slate-600 rounded-lg px-4 py-10 sm:w-96 text-center bg-slate-700/30">
            <Layout className="h-12 w-12 mx-auto text-slate-400 mb-4" />
            <p className="text-slate-300 mb-4">Upload campaign banners</p>
            <Button
              variant="outline"
              className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600">
              Add Banners
            </Button>
          </div>
        )}
      </DialogTrigger>
      <DialogContent
        withCloseButton={false}
        className="p-0">
        <DialogHeader>
          <DialogTitle className="sr-only">
            {banner ? getText("Update_Banner") : getText("Add_Banner")}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form className="space-y-4 p-6 pb-3">
            <FileInput
              control={form.control}
              name="image"
              defaultImage={getCdnUrl(banner?.image)}
              required
              onUploadingChange={setIsUploading}
              fileUploadDescription="Recommended size: 1200*800px"
            />
            <TextInput
              control={form.control}
              name="index"
              label={getText("Display_Order")}
              type="number"
              min={0}
              required
            />
          </form>
        </Form>
        <DialogFooter className="gap-2 border-t p-6">
          <DialogClose
            disabled={isUploading || isPending}
            type="button">
            {getText("Cancel")}
          </DialogClose>
          <Button
            disabled={isUploading || isPending}
            loading={isPending}
            onClick={handleSubmit}
            variant={"muted"}>
            {banner ? getText("Update_Banner") : getText("Add_Banner")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
