import { timestampDate } from "@bufbuild/protobuf/wkt";
import { Link, useRouter } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { Campaign } from "@vtuber/services/campaigns";
import { campaignClient } from "@vtuber/services/client";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Button } from "@vtuber/ui/components/button";
import { Card, CardContent, CardFooter } from "@vtuber/ui/components/card";
import { DeleteDialog } from "@vtuber/ui/components/DeleteDialog";
import { Image } from "@vtuber/ui/components/image";
import { Progress } from "@vtuber/ui/components/progress";
import { RemainingDaysBadge } from "@vtuber/ui/components/remaining-days-badge";
import { Tag } from "@vtuber/ui/components/tag";
import { getProgress } from "@vtuber/ui/lib/utils";
import { Calendar, CalendarCheck, Edit, Trash2 } from "lucide-react";

export function CampaignCard({ campaign }: { campaign: Campaign }) {
  const router = useRouter();
  const { getText } = useLanguage();
  const { getMultipleCategories } = useCategories();

  return (
    <Card className="bg-slate-800 group border-slate-700 overflow-hidden hover:border-slate-500 transition-all flex flex-col justify-between">
      <Link
        className="relative block"
        to="/campaigns/$id"
        params={{
          id: campaign.id.toString(),
        }}>
        <AspectRatio
          ratio={16 / 9}
          className="overflow-hidden bg-slate-900">
          <Image
            src={campaign.thumbnail}
            alt={campaign.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform"
          />
        </AspectRatio>
        <RemainingDaysBadge
          endDate={campaign.endDate!}
          className={"absolute top-3 right-3"}
        />
      </Link>
      <Link
        to="/campaigns/$id"
        params={{
          id: campaign.id.toString(),
        }}>
        <CardContent className="p-5">
          <h3 className="text-xl font-bold mb-2 line-clamp-1">
            {campaign.name}
          </h3>
          <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
            {campaign.shortDescription}
          </p>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-muted-foreground">
                  {getText("progress")}
                </span>
                <span className="font-medium">
                  {getProgress(campaign.totalBudget, campaign.totalRaised)}%
                </span>
              </div>
              <Progress
                value={getProgress(campaign.totalBudget, campaign.totalRaised)}
                variant={"success"}
                className="h-2"
              />
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="text-xs text-muted-foreground">
                  {getText("Raised")}
                </p>
                <p className="text-lg font-bold">
                  {getText("yen")}
                  {campaign.totalRaised.toLocaleString()}
                </p>
              </div>
              <div className="text-right">
                <p className="text-xs text-muted-foreground">
                  {getText("Target")}
                </p>
                <p className="text-sm">
                  {getText("yen")}
                  {campaign.totalBudget.toLocaleString()}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2 flex-wrap">
              {getMultipleCategories(campaign.categories).map((c) => (
                <Tag
                  key={c?.id}
                  variant={"outline-white"}
                  className="h-8 px-6">
                  {c?.name}
                </Tag>
              ))}
            </div>

            <div className="flex items-center gap-6 mt-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-green-800" />
                <span className="text-sm">{getText("start_date")}:</span>
                <span className="text-sm font-medium">
                  {campaign.startDate
                    ? timestampDate(campaign.startDate).toLocaleDateString()
                    : "N/A"}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <CalendarCheck className="h-4 w-4 text-red-800" />
                <span className="text-sm ">{getText("end_date")}:</span>
                <span className="text-sm font-medium">
                  {campaign.endDate
                    ? timestampDate(campaign.endDate).toLocaleDateString()
                    : "N/A"}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Link>
      <CardFooter className="p-5 pt-0 flex justify-between gap-2 items-stretch">
        <Button
          onClick={() => {
            router.navigate({
              to: "/campaigns/$id/edit",
              params: { id: campaign.id.toString() },
            });
          }}
          variant="minimal-dark"
          size={"lg"}
          className="flex-1 ">
          <Edit className="w-4 h-4 mr-2" />
          {getText("EDIT")}
        </Button>

        <DeleteDialog
          name={campaign.name}
          onDelete={() =>
            campaignClient.deleteCampaignById({ id: campaign.id })
          }>
          <Button
            variant="muted-destructive"
            size={"lg"}>
            <Trash2 className="w-4 h-4" />
          </Button>
        </DeleteDialog>
      </CardFooter>
    </Card>
  );
}
