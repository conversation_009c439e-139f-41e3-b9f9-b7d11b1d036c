import { useQuery } from "@connectrpc/connect-query";
import { useSearch } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Event, EventCommentService } from "@vtuber/services/events";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { useEffect, useState } from "react";
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { EventCommentForm } from "./EventCommentForm";
import { EventComments } from "./EventComments";

const dummyAnalyticsData = [
  { date: "Jan", views: 400, likes: 120 },
  { date: "Feb", views: 600, likes: 200 },
  { date: "Mar", views: 800, likes: 320 },
  { date: "Apr", views: 750, likes: 280 },
  { date: "May", views: 900, likes: 360 },
  { date: "Jun", views: 1100, likes: 450 },
];

export function EventTab({ id, event }: { id: string; event: Event }) {
  const { tab } = useSearch({ from: "/_app/event/$id/" });
  const [tabValue, setTabValue] = useState(tab || "about");
  useEffect(() => {
    if (tab) {
      setTabValue(tab);
    }
  }, [tab]);
  const { data: comments, refetch } = useQuery(
    EventCommentService.method.getAllEventComments,
    {
      eventId: id,
      pagination: {
        size: 100,
        order: "desc",
        sort: "created_at",
      },
    },
    {
      enabled: tabValue === "comment",
    },
  );
  const { getText } = useLanguage();
  return (
    <Tabs
      value={tabValue}
      onValueChange={setTabValue}
      className="mt-8">
      <TabsList className="w-full">
        <TabsTrigger
          value="about"
          className="px-4 py-2 whitespace-nowrap sm:flex-1 flex-1 sm:min-w-max text-center">
          {getText("About")}
        </TabsTrigger>
        <TabsTrigger
          value="participation_flow"
          className="px-4 py-2 whitespace-nowrap sm:flex-1 flex-1 sm:min-w-max text-center">
          {getText("particiaption_flow")}
        </TabsTrigger>
        <TabsTrigger
          value="benefits"
          className="px-4 py-2 whitespace-nowrap sm:flex-1 flex-1 sm:min-w-max text-center">
          {getText("Benefits")}
        </TabsTrigger>
        <TabsTrigger
          value="requirements"
          className="px-4 py-2 whitespace-nowrap sm:flex-1 flex-1 sm:min-w-max text-center">
          {getText("Requirements")}
        </TabsTrigger>
        <TabsTrigger
          value="discussion"
          className="px-4 py-2 whitespace-nowrap sm:flex-1 flex-1 sm:min-w-max text-center">
          {getText("discussion")}
        </TabsTrigger>
        <TabsTrigger
          value="analytics"
          className="px-4 py-2 whitespace-nowrap sm:flex-1 flex-1 sm:min-w-max text-center">
          {getText("Analytics")}
        </TabsTrigger>
        <TabsTrigger
          value="comment"
          className="px-4 py-2 whitespace-nowrap sm:flex-1 flex-1 sm:min-w-max text-center">
          {getText("comments")}
        </TabsTrigger>
      </TabsList>
      <TabsContent value="about">
        <MarkDown
          className="text-font"
          markdown={event.description}
        />
      </TabsContent>
      <TabsContent value="participation_flow">
        <MarkDown
          className="text-font"
          markdown={event.participationFlow}
        />
      </TabsContent>
      <TabsContent value="benefits">
        <MarkDown
          className="text-font"
          markdown={event.benefits}
        />
      </TabsContent>
      <TabsContent value="requirements">
        <MarkDown
          className="text-font"
          markdown={event.requirements}
        />
      </TabsContent>
      <TabsContent value="discussion">
        <Card className="border-none shadow-md">
          <CardContent className="pt-6">
            <div className="grid group gap-y-8 relative">
              <ScrollArea className="max-h-[768px] flex flex-col">
                <div className="text-font grid gap-y-5 p-6 bg-[#2C2A37] border border-[#A9A9A9] rounded-[24px]">
                  <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-[#686868] rounded-10 group-hover:hidden py-[18px] px-2 z-10 w-[138px] flex items-center justify-center">
                    <div className="flex flex-col items-center gap-3">
                      <small className="text-[10px] font-medium text-white">
                        縦スクロール可能です
                      </small>
                    </div>
                  </div>
                  <div className="bg-gradient-4 pb-0.5">
                    <div className="px-4 py-[10px] text-font font-bold text-xl bg-[#2C2A37]">
                      参加資格
                    </div>
                  </div>
                  <div className="grid gap-y-8">
                    <p className="font-medium">
                      【参加資格】
                      <br />
                      V祭のユーザーであって、Charaforio共通利用規約、本参加要項に同意された方
                    </p>
                    <section>
                      <h6>★ご参加のVTuberの皆様 </h6>{" "}
                      <ul className="list-disc list-inside">
                        <li>国内在住の方 </li>
                        <li>18歳以上の方 </li>
                        <li>全年齢向けで活動されている方 </li>
                        <li>個人のVTuberの方 </li>
                        <li>
                          または法人格を持たないグループVtuberに所属してる方{" "}
                        </li>
                        <li>
                          YouTubeチャンネル登録者数10万人以下の方（※イベント参加申し込み時点）{" "}
                        </li>
                        <li>ご自身のVTuberを○▼×登録されている方 </li>
                        <li>
                          ステージ制作の都合上、東京都内にお越しいただく可能性がございますのでご対応可能な方{" "}
                        </li>
                      </ul>
                    </section>
                    <p className="font-medium">
                      ※参加申し込み期間内に○▼×登録がお済みでない方はご参加できません。○▼×登録には本人確認の実施等、お時間がかかりますのでお早めに登録申請を行ってください。
                    </p>
                  </div>
                  <div className="bg-gradient-4 pb-0.5">
                    <div className="px-4 py-[10px] text-font font-bold text-xl bg-[#2C2A37]">
                      参加資格
                    </div>
                  </div>
                  <div className="grid gap-y-8">
                    <p className="font-medium">
                      【参加資格】
                      <br />
                      V祭のユーザーであって、Charaforio共通利用規約、本参加要項に同意された方
                    </p>
                    <section>
                      <h6>★ご参加のVTuberの皆様 </h6>{" "}
                      <ul className="list-disc list-inside">
                        <li>国内在住の方 </li>
                        <li>18歳以上の方 </li>
                        <li>全年齢向けで活動されている方 </li>
                        <li>個人のVTuberの方 </li>
                        <li>
                          または法人格を持たないグループVtuberに所属してる方{" "}
                        </li>
                        <li>
                          YouTubeチャンネル登録者数10万人以下の方（※イベント参加申し込み時点）{" "}
                        </li>
                        <li>ご自身のVTuberを○▼×登録されている方 </li>
                        <li>
                          ステージ制作の都合上、東京都内にお越しいただく可能性がございますのでご対応可能な方{" "}
                        </li>
                      </ul>
                    </section>
                    <p className="font-medium">
                      ※参加申し込み期間内に○▼×登録がお済みでない方はご参加できません。○▼×登録には本人確認の実施等、お時間がかかりますのでお早めに登録申請を行ってください。
                    </p>
                  </div>
                </div>
              </ScrollArea>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="analytics">
        <Card className="p-6 shadow-md rounded-lg">
          <CardContent>
            <h2 className="text-xl font-bold  mb-4">
              {getText("Event_Analytics")}
            </h2>

            <div className="w-full h-64">
              <ResponsiveContainer
                width="100%"
                height="100%">
                <LineChart data={dummyAnalyticsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    stroke="#8884d8"
                  />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="views"
                    stroke="#4f46e5"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="likes"
                    stroke="#22c55e"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="comment">
        <Card className="p-6 shadow-md rounded-lg mt-4">
          <CardContent>
            <EventCommentForm
              eventId={id}
              refetch={refetch}
              className="w-full mb-6 border border-gray-500 rounded-lg p-4"
            />
            <div className="space-y-6">
              {comments?.data && comments.data.length > 0 ? (
                comments.data.map((comment) => (
                  <EventComments
                    comment={comment}
                    refetch={refetch}
                    key={comment.id}
                  />
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  {getText("No_Comments")}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
