import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation, useQuery } from "@connectrpc/connect-query";
import { RefetchOptions } from "@tanstack/react-query";
import { useSearch } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { EventComment, EventCommentService } from "@vtuber/services/events";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Textarea } from "@vtuber/ui/components/textarea";
import { getTimeAgo } from "@vtuber/ui/lib/get-time-ago";
import { cn, getCdnUrl } from "@vtuber/ui/lib/utils";
import {
  ChevronDown,
  ChevronUp,
  Edit,
  MessageSquare,
  Save,
  Trash,
  X,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { EventCommentForm } from "./EventCommentForm";

interface Props {
  refetch: (options?: RefetchOptions) => Promise<any>;
  comment: EventComment;
  depth?: number;
}

export const EventComments = ({ comment, depth = 0, refetch }: Props) => {
  const [eventComment, setEventComment] = useState(comment.content);
  const [isReplying, setIsReplying] = useState(false);
  const [showReplies, setShowReplies] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [hasReplies, setHasReplies] = useState(comment.hasReply);
  const { session: authUser } = useAuth();
  const { commentId } = useSearch({ from: "/_app/event/$id/" });
  const canDelete =
    authUser?.vtuber?.id === comment?.vtuber?.id ||
    comment.user?.id === authUser?.user?.id;

  const user = comment.vtuber ? comment.vtuber : comment.user;
  // const { ref } = useScrollAndHighlight({
  //   targetId: commentId,
  //   currentId: comment.id,
  // });
  const {
    data,
    isPending,
    refetch: refetchReplies,
  } = useQuery(
    EventCommentService.method.getAllRepliesOfEventComment,
    {
      id: comment.id,
      pagination: {
        size: 100,
        order: "desc",
        sort: "created_at",
      },
    },
    {
      enabled: showReplies,
    },
  );

  const updateCommentMutations = useMutation(
    EventCommentService.method.updateEventCommentById,
    {
      onSuccess: () => {
        refetch && refetch();
        setIsEditing(false);
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const deleteMutations = useMutation(
    EventCommentService.method.deleteEventCommentById,
    {
      onSuccess: () => {
        refetchReplies();
        refetch && refetch();
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const handleEditComment = () => {
    updateCommentMutations.mutate({
      id: comment.id,
      content: eventComment,
    });
  };

  const handleDeleteComment = () => {
    if (window.confirm("Are you sure you want to delete this comment?")) {
      deleteMutations.mutate({ id: comment.id });
    }
  };

  const replies = data?.data;
  const indentClass = depth > 0 ? `ml-${Math.min(depth * 6, 12)}` : "";
  const { getText } = useLanguage();
  return (
    <div className={`flex gap-3 relative group ${indentClass} mb-4`}>
      {depth > 0 && (
        <div className="absolute left-0 top-10 bottom-0 w-0.5  group-hover:bg-blue-100 transition-colors duration-300" />
      )}

      <Avatar
        src={getCdnUrl(user?.image)}
        alt={user?.name}
        className="rounded-full border-border border w-10 h-10 flex-shrink-0 ring-2 ring-white shadow-sm"
      />

      <div className="flex-1">
        <div
          // ref={ref}
          className="rounded-2xl px-5 py-3 shadow-sm transition-all duration-200">
          <div className="flex items-center justify-between">
            <div className="font-semibold ">{user?.name}</div>
            <span className="text-xs ">
              {getTimeAgo(timestampDate(comment.createdAt!))}
            </span>
          </div>

          {isEditing ? (
            <div className="mt-2">
              <Textarea
                value={eventComment}
                onChange={(e) => setEventComment(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
              />
              <div className="flex gap-2 mt-2">
                <button
                  onClick={handleEditComment}
                  className="flex items-center gap-1 px-3 py-1 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                  <Save size={14} />
                  {getText("Save")}
                </button>
                <button
                  onClick={() => setIsEditing(false)}
                  className="flex items-center gap-1 px-3 py-1 text-sm font-medium  bg-red-600 rounded-lg hover:bg-red-800">
                  <X size={14} />
                  {getText("cancel")}
                </button>
              </div>
            </div>
          ) : (
            <div className="mt-2 leading-relaxed">{comment.content}</div>
          )}
        </div>

        <div className="flex gap-4 text-xs mt-2 items-center ml-1">
          <button
            className={cn(
              "flex items-center gap-1 hover:text-blue-600 font-medium py-1 transition-colors duration-200",
              { "text-blue-600": isReplying, "text-gray-500": !isReplying },
            )}
            onClick={() => setIsReplying((prev) => !prev)}>
            <MessageSquare size={14} />
            {isReplying ? getText("Cancel") : getText("Reply")}
          </button>

          {(hasReplies || (replies && replies?.length > 0)) && (
            <button
              className={cn(
                "flex items-center gap-1 font-medium py-1 transition-colors duration-200",
                {
                  "text-blue-600": showReplies,
                  "text-gray-500 hover:text-blue-600": !showReplies,
                },
              )}
              onClick={() => setShowReplies((prev) => !prev)}>
              {showReplies ? (
                <>
                  <ChevronUp size={14} />
                  {getText("Hide_Replies")}
                </>
              ) : (
                <>
                  <ChevronDown size={14} />
                  {getText("View_Replies")}
                </>
              )}
            </button>
          )}

          {comment.user?.id === authUser?.user?.id && (
            <button
              className={cn(
                "flex items-center gap-1 hover:text-blue-600 font-medium py-1 transition-colors duration-200",
                { "text-blue-600": isEditing, "text-gray-500": !isEditing },
              )}
              onClick={() => setIsEditing((prev) => !prev)}>
              <Edit size={14} />
              {isEditing ? getText("Editing") : getText("EDIT")}
            </button>
          )}
          {canDelete && (
            <button
              className="flex items-center gap-1 hover:text-red-600 font-medium py-1 transition-colors duration-200 text-gray-500"
              onClick={handleDeleteComment}>
              <Trash size={14} />
              {getText("Delete")}
            </button>
          )}
        </div>

        {isReplying && (
          <div className="mt-3 transition-all duration-300 animate-fadeIn">
            <EventCommentForm
              setShowReplies={(value) => {
                setShowReplies(true);
                setHasReplies(true);
              }}
              refetch={refetchReplies}
              className="p-3 rounded-xl border border-gray-800"
              eventId={comment.eventId}
              parentId={comment.id}
            />
          </div>
        )}

        {showReplies && (
          <div className="space-y-3 mt-4 pl-2 transition-all duration-300">
            {isPending && (
              <div className="flex justify-center py-4">
                <div className="animate-pulse flex space-x-2">
                  <div className="h-2 w-2 bg-blue-200 rounded-full"></div>
                  <div className="h-2 w-2 bg-blue-300 rounded-full"></div>
                  <div className="h-2 w-2 bg-blue-400 rounded-full"></div>
                </div>
              </div>
            )}

            {!isPending && replies?.length === 0 && (
              <div className="text-sm text-center italic py-2">
                {getText("No_Relies_Yet")}
              </div>
            )}

            {!isPending &&
              replies?.map((r) => (
                <EventComments
                  key={r.id}
                  refetch={refetchReplies}
                  comment={r}
                  depth={depth + 1}
                />
              ))}
          </div>
        )}
      </div>
    </div>
  );
};
