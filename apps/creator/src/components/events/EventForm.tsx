// src/components/creator-subscription-form.tsx
import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { Event, EventService } from "@vtuber/services/events";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { DateInput } from "@vtuber/ui/components/form-inputs/date-input";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { HtmlInput } from "@vtuber/ui/components/form-inputs/html-input";
import { MultiSelectInput } from "@vtuber/ui/components/form-inputs/multi-select-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@vtuber/ui/components/hover-card";
import { Image } from "@vtuber/ui/components/image";
import { Separator } from "@vtuber/ui/components/separator";
import { Spinner } from "@vtuber/ui/components/spinner";
import { convertEmptyStringToUndefined } from "@vtuber/ui/lib/convert-empty-string-to-undefined";
import {
  FileText,
  HelpCircle,
  ImageIcon,
  InfoIcon,
  Link,
  Save,
  Settings,
} from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

type EventFormProps = {
  mode: "add" | "edit";
  defaultValues?: Event & { asVtuber?: boolean };
  onSuccess?: () => void;
};

export function EventForm({ mode, defaultValues, onSuccess }: EventFormProps) {
  const router = useRouter();
  const { getText } = useLanguage();
  const { categories } = useCategories();
  const [isUploading, setIsUploading] = useState(false);

  const form = useForm({
    defaultValues: {
      asVtuber: defaultValues?.asVtuber || true,
      benefits: defaultValues?.benefits,
      categories: defaultValues?.categories,
      endDate: defaultValues?.endDate,
      id: defaultValues?.id,
      image: defaultValues?.image,
      overview: defaultValues?.overview,
      participationFlow: defaultValues?.participationFlow,
      requirements: defaultValues?.requirements,
      rules: defaultValues?.rules,
      shortDescription: defaultValues?.shortDescription,
      startDate: defaultValues?.startDate,
      title: defaultValues?.title,
      socialMediaLinks: defaultValues?.socialMediaLinks,
    },
  });

  const addMutation = useMutation(EventService.method.addEvent, {
    onError: (err) => {
      handleConnectError(err, form);
    },
    onSuccess: () => {
      toast.success("Event Added Successfully");
      router.invalidate();
      onSuccess?.();
      router.navigate({
        to: "/event",
      });
    },
  });

  const updateMutation = useMutation(EventService.method.updateEventById, {
    onSuccess: () => {
      toast.success("Event Updated successfully");
      router.invalidate();
      onSuccess?.();
      router.navigate({
        to: "/event",
      });
    },
    onError: (err) => {
      handleConnectError(err, form);
    },
  });

  const onSubmit = form.handleSubmit((values) => {
    const data = {
      ...values,
      socialMediaLinks: convertEmptyStringToUndefined(
        values.socialMediaLinks || {},
      ),
    };
    if (mode === "add") {
      return addMutation.mutateAsync(data);
    } else {
      return updateMutation.mutateAsync(data);
    }
  });

  const isLoading = addMutation.isPending || updateMutation.isPending;

  return (
    <div className="pt-10 max-w-4xl mx-auto">
      <Form {...form}>
        <form
          onSubmit={onSubmit}
          className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <InfoIcon className="size-5" /> Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <TextInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                control={form.control}
                name="title"
                label={getText("event_title")}
                required
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <DateInput
                  wrapperClassName="space-y-2"
                  control={form.control}
                  name="startDate"
                  className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 border focus-visible:ring-1 focus-visible:ring-white rounded-md"
                  label={getText("start_date")}
                />
                <DateInput
                  wrapperClassName="space-y-2"
                  control={form.control}
                  className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 border focus-visible:ring-1 focus-visible:ring-white rounded-md"
                  name="endDate"
                  label={getText("end_date")}
                />
              </div>
              <MultiSelectInput
                wrapperClassName="space-y-2"
                variant={"muted"}
                control={form.control}
                placeholder="select event category"
                name="categories"
                label={getText("category")}
                options={categories.map((category) => ({
                  label: category.name,
                  value: category.id.toString(),
                }))}
              />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Link className="size-5 mt-1" /> Social Links{" "}
                <span className="text-sm text-gray01">(optional)</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid sm:grid-cols-2 items-center gap-6">
                <TextInput
                  control={form.control}
                  label="Youtube Link"
                  name="socialMediaLinks.youtube"
                  type="url"
                  inputMode="url"
                  variant={"muted"}
                  wrapperClassName="space-y-2"
                />
                <TextInput
                  label="Twitter Link"
                  control={form.control}
                  name="socialMediaLinks.twitter"
                  type="url"
                  inputMode="url"
                  variant={"muted"}
                  wrapperClassName="space-y-2"
                />
                <TextInput
                  label="Tiktok Link"
                  control={form.control}
                  name="socialMediaLinks.tiktok"
                  type="url"
                  inputMode="url"
                  variant={"muted"}
                  wrapperClassName="space-y-2"
                />
                <TextInput
                  label="Twitch Link"
                  control={form.control}
                  name="socialMediaLinks.twitch"
                  type="url"
                  inputMode="url"
                  variant={"muted"}
                  wrapperClassName="space-y-2"
                />
                <TextInput
                  label="Discord Link"
                  control={form.control}
                  name="socialMediaLinks.discord"
                  type="url"
                  inputMode="url"
                  variant={"muted"}
                  wrapperClassName="space-y-2"
                />
                <TextInput
                  label="Instagram Link"
                  control={form.control}
                  name="socialMediaLinks.instagram"
                  type="url"
                  inputMode="url"
                  variant={"muted"}
                  wrapperClassName="space-y-2"
                />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <ImageIcon className="size-5 mt-1" /> Event Thumbnail
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FileInput
                onUploadingChange={setIsUploading}
                control={form.control}
                name="image"
                defaultImage={defaultValues?.image}
                fileUploadDescription="Recommended size: 1280x800px"
              />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="size-5 mt-1" /> About Event
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <TextAreaInput
                variant={"muted"}
                wrapperClassName="space-y-2"
                maxLength={255}
                name="shortDescription"
                control={form.control}
                required
                label={
                  <div className="flex items-center gap-2">
                    <span>{getText("short_description")}</span>
                    <HoverCard openDelay={0}>
                      <HoverCardTrigger>
                        <HelpCircle className="size-4 text-tertiary" />
                      </HoverCardTrigger>
                      <HoverCardContent className="bg-background space-y-3 p-0">
                        <div className="pb-0 p-4">
                          <h3 className="text-font font-medium leading-relaxed">
                            This will be displayed on the event card.
                          </h3>
                        </div>
                        <Separator />
                        <Image
                          src="https://cdn.v-sai.com/assets/event_card.png"
                          className="size-full object-cover"
                          alt="Event Card"
                        />
                      </HoverCardContent>
                    </HoverCard>
                  </div>
                }
              />
              <HtmlInput
                control={form.control}
                name="overview"
                label={"Overview"}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Settings className="size-5 mt-1" /> Other Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <HtmlInput
                wrapperClassName="space-y-2"
                control={form.control}
                name="rules"
                label={getText("rules")}
              />
              <HtmlInput
                wrapperClassName="space-y-2"
                control={form.control}
                name="participationFlow"
                label={"Participation Flow"}
              />

              <HtmlInput
                wrapperClassName="space-y-2"
                control={form.control}
                name="benefits"
                label={"Benefits"}
              />
              <HtmlInput
                wrapperClassName="space-y-2"
                control={form.control}
                name="requirements"
                label={"Requirements"}
              />
            </CardContent>
          </Card>
          <Button
            type="submit"
            variant={"success"}
            className="w-full"
            size={"xl"}
            disabled={isLoading || isUploading}>
            {isLoading ? (
              <Spinner className="!size-8" />
            ) : (
              <Save className="w-5 h-5 mr-2" />
            )}
            {getText("submit")}
          </Button>
        </form>
      </Form>
    </div>
  );
}
