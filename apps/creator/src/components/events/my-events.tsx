import { useQuery } from "@connectrpc/connect-query";
import { useSearch } from "@tanstack/react-router";
import { EventService } from "@vtuber/services/events";
import { Paginator } from "@vtuber/ui/components/paginator";
import { EventCard } from "./EventCard";

type Props = {
  tabValue: string;
};

export const MyEvents = ({ tabValue }: Props) => {
  const search = useSearch({ from: "/_app/event/" });
  const { data, isPending, error } = useQuery(
    EventService.method.getMyEvents,
    { pagination: { sort: "created_at", order: "desc", ...search } },
    { enabled: tabValue === "myEvents" },
  );

  if (isPending)
    return (
      <div className="h-40 flex items-center justify-center">
        <p>Loading...</p>
      </div>
    );

  if (error)
    return (
      <div className="h-40 flex items-center justify-center">
        ERROR: <p>{error.rawMessage}</p>
      </div>
    );
  if (!data || data.data.length === 0)
    return (
      <div className="h-40 flex items-center justify-center">
        <p>No Events</p>
      </div>
    );
  const events = data.data;
  return (
    <div className="space-y-10">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {events.map((event) => (
          <EventCard
            event={event}
            key={event.id}
          />
        ))}
      </div>
      <Paginator
        revalidate={false}
        currentPage={data?.paginationDetails?.currentPage}
        totalPages={data?.paginationDetails?.totalPages}
      />
    </div>
  );
};
