import { useLanguage } from "@vtuber/language/hooks";
import { Badge } from "@vtuber/ui/components/badge";
import { cn } from "@vtuber/ui/lib/utils";
import { useCallback } from "react";

export const EventStatus = ({
  status,
  className,
}: {
  status: string;
  className?: string;
}) => {
  const { getText } = useLanguage();
  const statusTextMap = {
    approved: getText("Approved"),
    rejected: getText("Rejected"),
    pending: getText("Pending"),
  };
  const getEventStatusColor = useCallback(() => {
    switch (status) {
      case "approved":
        return "bg-green-700 hover:bg-green-800 text-white";
      case "pending":
        return "bg-yellow-700 hover:bg-yellow-800 text-white";
      case "rejected":
        return "bg-red-700 hover:bg-red-800 text-white";
      default:
        return "bg-gray-700 hover:bg-gray-800 text-white";
    }
  }, []);

  return (
    <Badge className={cn(className, getEventStatusColor())}>
      {statusTextMap[status as keyof typeof statusTextMap] || status}
    </Badge>
  );
};
