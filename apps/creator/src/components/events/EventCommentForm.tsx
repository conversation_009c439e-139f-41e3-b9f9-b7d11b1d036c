import { useMutation } from "@connectrpc/connect-query";
import { RefetchOptions } from "@tanstack/react-query";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { EventCommentService } from "@vtuber/services/events";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Button } from "@vtuber/ui/components/button";
import { Form } from "@vtuber/ui/components/form";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { cn, getCdnUrl } from "@vtuber/ui/lib/utils";
import { Send } from "lucide-react";
import { Dispatch, SetStateAction } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

type Props = {
  eventId: string;
  parentId?: string;
  className?: string;
  refetch: (options?: RefetchOptions) => Promise<any>;
  setShowReplies?: Dispatch<SetStateAction<boolean>>;
};

export const EventCommentForm = ({
  eventId,
  parentId,
  className,
  refetch,
  setShowReplies,
}: Props) => {
  const router = useRouter();
  const { session: user } = useAuth();
  const form = useForm({
    defaultValues: {
      comment: "",
      asVtuber: false,
      parentId,
    },
  });
  const comment = form.watch("comment");

  const mutation = useMutation(EventCommentService.method.addEventComment, {
    onSuccess: async () => {
      await refetch();
      setShowReplies && setShowReplies(true);
      form.reset();
      toast.success("Comment added successfully");
    },
    onSettled: () => {
      setShowReplies && setShowReplies(true);
    },
    onError: (err) => {
      handleConnectError(err, form);
    },
  });

  const onComment = form.handleSubmit((data) => {
    if (!user) {
      router.navigate({ to: "/login" });
      return;
    }
    mutation.mutate({
      content: data.comment,
      eventId,
      asVtuber: data.asVtuber,
      parentId: data.parentId,
    });
  });
  const { getText } = useLanguage();
  return (
    <Form {...form}>
      <form
        onSubmit={onComment}
        className={cn("flex items-start gap-3 w-full", className)}>
        <Avatar
          src={getCdnUrl(user?.vtuber?.image)}
          alt={user?.vtuber?.displayName}
          className="w-6 h-6"
        />
        <div className="flex-1 space-y-3 relative">
          <TextAreaInput
            className={cn(
              "h-auto overflow-hidden min-h-[40px] pt-3 pb-2 rounded-2xl resize-none transition-all duration-200",
              "border border-gray-200 focus-visible:border-blue-300 shadow-sm",
              "focus-visible:ring-1 focus-visible:ring-blue-200 focus-visible:ring-offset-1",
              comment ? "pr-16" : "pr-12",
            )}
            onInput={(e) => {
              e.currentTarget.style.height = "auto";
              e.currentTarget.style.height = `${e.currentTarget.scrollHeight}px`;
            }}
            name="comment"
            control={form.control}
            placeholder={`${getText("Comment_as")} ${user?.vtuber?.displayName}`}
          />
          <Button
            disabled={!user ? false : !comment || mutation.isPending}
            className={cn(
              "rounded-full shadow-none absolute transition-all duration-200 ease-in-out right-2 z-20",
              !!comment ? "top-2" : "top-1.5",
            )}
            variant={user ? "secondary" : "default"}
            type="submit"
            loading={mutation.isPending}>
            {!user ? (
              "Login"
            ) : (
              <Send
                size={20}
                className={cn(
                  "text-blue-600 hover:text-blue-700 transition-colors duration-200",
                  mutation.isPending && "opacity-50",
                )}
              />
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};
