import { useQuery } from "@connectrpc/connect-query";
import { useSearch } from "@tanstack/react-router";
import { EventParticipantService } from "@vtuber/services/events";
import { Paginator } from "@vtuber/ui/components/paginator";
import { EventCard } from "./EventCard";

export const ParticipatedEvents = ({ tabValue }: { tabValue: string }) => {
  const search = useSearch({ from: "/_app/event/" });

  const { data, isPending, refetch } = useQuery(
    EventParticipantService.method.getMyEventParticipation,
    {
      pagination: {
        sort: "created_at",
        order: "desc",
        ...search,
      },
    },
    {
      enabled: tabValue === "participatedEvents",
    },
  );

  if (isPending)
    return (
      <div className="h-40 flex items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  if (!data || data.data.length === 0)
    return (
      <div className="h-40 flex items-center justify-center">
        <p>No Events</p>
      </div>
    );
  const events = data.data;

  return (
    <div className="space-y-10 p-10 pt-16">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {events.map((event) => (
          <EventCard
            event={event.event!}
            key={event.id}
            isPartication
          />
        ))}
      </div>
      <Paginator
        revalidate={false}
        currentPage={data.paginationDetails?.currentPage}
        onPageChange={() => {
          refetch();
        }}
        totalPages={data?.paginationDetails?.totalPages}
      />
    </div>
  );
};
