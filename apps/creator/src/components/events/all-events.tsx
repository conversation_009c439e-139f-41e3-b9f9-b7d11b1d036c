import { useQuery } from "@connectrpc/connect-query";
import { useSearch } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { EventService } from "@vtuber/services/events";

import { Paginator } from "@vtuber/ui/components/paginator";
import { EventCard } from "./EventCard";

export const AllEvents = ({ tabValue }: { tabValue: string }) => {
  const search = useSearch({ from: "/_app/event/" });
  const { data, isPending } = useQuery(
    EventService.method.getAllEvents,
    {
      pagination: {
        sort: "created_at",
        order: "desc",
        ...search,
      },
    },
    {
      enabled: tabValue === "allEvents",
    },
  );

  const { getText } = useLanguage();
  if (isPending)
    return (
      <div className="h-40 flex items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  if (!data || data.data.length === 0)
    return (
      <div className="h-40 flex items-center justify-center">
        <p>{getText("No_Events")}</p>
      </div>
    );
  const events = data.data;
  return (
    <div className="p-10 pt-16 space-y-10">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {events.map((event) => (
          <EventCard
            event={event}
            key={event.id}
            showAdminControls={false}
          />
        ))}
      </div>
      <Paginator
        revalidate={false}
        currentPage={data?.paginationDetails?.currentPage}
        totalPages={data?.paginationDetails?.totalPages}
      />
    </div>
  );
};
