import { timestampDate } from "@bufbuild/protobuf/wkt";
import { Link } from "@tanstack/react-router";
import { Post } from "@vtuber/services/content";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { buttonVariants } from "@vtuber/ui/components/button";
import { Card, CardContent, CardFooter } from "@vtuber/ui/components/card";
import { Media } from "@vtuber/ui/components/media";
import { Clock, Edit, Eye, Heart, Lock, MessageSquare } from "lucide-react";

export function PostCard({ post }: { post: Post }) {
  return (
    <Card className="bg-slate-800 border-slate-700 flex flex-col justify-between overflow-hidden hover:border-slate-500 transition-all">
      <div className="relative">
        <AspectRatio
          ratio={4 / 3}
          className="overflow-hidden bg-slate-900">
          <Media
            type={post.mediaType}
            src={post.media}
            alt={post.title}
            className="w-full h-full object-cover hover:scale-105 transition-transform"
          />
        </AspectRatio>
        {post.membershipOnly && (
          <Badge className="absolute top-3 right-3 bg-green-600 hover:bg-green-700">
            <Lock className="w-3 h-3 mr-1" />
            Membership Only
          </Badge>
        )}
      </div>

      <CardContent className="p-5">
        <div className="flex items-center gap-2 mb-3">
          <Avatar
            className="w-6 h-6"
            src={post.vtuber?.image}
            fallback={post.vtuber?.name}
            alt={post.vtuber?.name}
          />
          <span className="text-sm text-font">{post.vtuber?.name}</span>
          <div className="flex-1"></div>
          <div className="flex items-center text-xs text-font">
            <Clock className="w-3 h-3 mr-1" />
            {post.createdAt
              ? timestampDate(post.createdAt).toLocaleDateString()
              : "N/A"}
          </div>
        </div>

        <h3 className="text-xl font-bold mb-2 line-clamp-1">{post.title}</h3>
        <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
          {post.shortDescription}
        </p>
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-1 text-font">
            <Heart className="w-4 h-4 text-pink-500" />
            <span>{post.postLikes}</span>
          </div>
          <div className="flex items-center gap-1 text-font">
            <MessageSquare className="w-4 h-4 text-blue-500" />
            <span>{post.postComments}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-5 flex justify-between gap-2 border-t border-slate-700 mt-4">
        <Link
          to="/posts/$id"
          params={{
            id: post.id.toString(),
          }}
          className={buttonVariants({
            size: "xl",
            variant: "minimal-dark",
            className: "flex-1",
          })}>
          <Eye className="w-4 h-4 mr-2" />
          View
        </Link>
        <Link
          to="/posts/$id/edit"
          params={{
            id: post.id.toString(),
          }}
          className={buttonVariants({
            size: "xl",
            variant: "minimal-dark",
            className: "flex-1",
          })}>
          <Edit className="w-4 h-4 mr-2" />
          Edit
        </Link>
      </CardFooter>
    </Card>
  );
}
