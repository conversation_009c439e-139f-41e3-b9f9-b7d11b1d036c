import { useQuery } from "@connectrpc/connect-query";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { PostCommentService } from "@vtuber/services/content";
import { Card, CardContent } from "@vtuber/ui/components/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { useEffect, useState } from "react";
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { PostComments } from "./PostComment";
import { PostCommentForm } from "./PostCommentForm";

const dummyAnalyticsData = [
  { date: "Jan", views: 400, likes: 120 },
  { date: "Feb", views: 600, likes: 200 },
  { date: "Mar", views: 800, likes: 320 },
  { date: "Apr", views: 750, likes: 280 },
  { date: "May", views: 900, likes: 360 },
  { date: "Jun", views: 1100, likes: 450 },
];

export function PostTab({ id }: { id: string }) {
  const { tab } = useSearch({ from: "/_app/posts/$id/" });
  const [tabValue, setTabValue] = useState(tab || "analytics");
  useEffect(() => {
    if (tab) {
      setTabValue(tab);
    }
  }, [tab]);
  const {
    data: comments,
    isPending,
    refetch,
  } = useQuery(PostCommentService.method.getAllPostComments, {
    postId: String(id),
    pagination: {
      size: 100,
      order: "desc",
      sort: "created_at",
    },
  });

  const { getText } = useLanguage();
  const navigate = useNavigate();
  return (
    <Tabs
      value={tabValue}
      onValueChange={(val) => {
        setTabValue(val);
        navigate({
          // @ts-ignore
          search: (prev) => ({
            ...prev,
            tab: val,
          }),
          resetScroll: false,
        });
      }}
      defaultValue="analytics"
      className="mt-8">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="analytics">
          {getText("Posts_Analytics")}
        </TabsTrigger>
        <TabsTrigger value="comments">{getText("comments")}</TabsTrigger>
      </TabsList>

      <TabsContent value="analytics">
        <Card className="p-6 shadow-md rounded-lg mt-4">
          <CardContent>
            <h2 className="text-xl font-bold  mb-4">Posts Analytics</h2>

            <div className="w-full h-64">
              <ResponsiveContainer
                width="100%"
                height="100%">
                <LineChart data={dummyAnalyticsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    stroke="#8884d8"
                  />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="views"
                    stroke="#4f46e5"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="likes"
                    stroke="#22c55e"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="comments">
        <Card className="p-6 shadow-md rounded-lg mt-4">
          <CardContent>
            <PostCommentForm
              postId={id}
              refetch={refetch}
              className="w-full mb-6 border border-gray-500 rounded-lg p-4"
            />
            <div className="space-y-6">
              {comments?.data && comments.data.length > 0 ? (
                comments.data.map((comment) => (
                  <PostComments
                    comment={comment}
                    refetch={refetch}
                    key={comment.id}
                  />
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  {getText("No_Comments")}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
