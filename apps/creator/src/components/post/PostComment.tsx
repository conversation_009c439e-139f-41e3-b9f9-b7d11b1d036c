import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation, useQuery } from "@connectrpc/connect-query";
import { RefetchOptions } from "@tanstack/react-query";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { PostComment, PostCommentService } from "@vtuber/services/content";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Textarea } from "@vtuber/ui/components/textarea";
import { getTimeAgo } from "@vtuber/ui/lib/get-time-ago";
import { cn, getCdnUrl } from "@vtuber/ui/lib/utils";
import {
  ChevronDown,
  ChevronUp,
  Edit,
  MessageSquare,
  Save,
  Trash,
  X,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { PostCommentForm } from "./PostCommentForm";

interface Props {
  refetch: (options?: RefetchOptions) => Promise<any>;
  comment: PostComment;
  depth?: number;
}

export const PostComments = ({ comment, depth = 0, refetch }: Props) => {
  const [postComment, setpostComment] = useState(comment.content);
  const [isReplying, setIsReplying] = useState(false);
  const [showReplies, setShowReplies] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [hasReplies, setHasReplies] = useState(comment.hasReply);
  const { session: authUser } = useAuth();
  const user = comment.vtuber ? comment.vtuber : comment.user;
  const canDelete =
    authUser?.vtuber?.id === comment?.vtuber?.id ||
    comment.user?.id === authUser?.user?.id;
  const {
    data,
    isPending,
    refetch: refetchReplies,
  } = useQuery(
    PostCommentService.method.getAllReplesOfComment,
    {
      id: comment.id,
      pagination: {
        size: 100,
        order: "desc",
        sort: "created_at",
      },
    },
    {
      enabled: showReplies,
    },
  );

  const updateCommentMutations = useMutation(
    PostCommentService.method.updatePostCommentById,
    {
      onSuccess: () => {
        refetch && refetch();
        setIsEditing(false);
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const deleteMutations = useMutation(
    PostCommentService.method.deletePostCommentById,
    {
      onSuccess: () => {
        refetchReplies();
        refetch && refetch();
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const handleEditComment = () => {
    updateCommentMutations.mutate({
      id: comment.id,
      content: postComment,
    });
  };

  const handleDeleteComment = () => {
    if (window.confirm("Are you sure you want to delete this comment?")) {
      deleteMutations.mutate({ id: comment.id });
    }
  };

  const replies = data?.data;
  const indentClass = depth > 0 ? `ml-${Math.min(depth * 6, 12)}` : "";
  const { getText } = useLanguage();
  return (
    <div className={`flex gap-3 relative group rounded-lg ${indentClass} mb-4`}>
      {depth > 0 && (
        <div className="absolute left-0 top-10 bottom-0 w-0.5  group-hover:bg-blue-100 transition-colors duration-300" />
      )}

      <Avatar
        src={getCdnUrl(user?.image)}
        alt={user?.name}
        className="rounded-full  border-border border w-10 h-10 flex-shrink-0 ring-2 ring-white shadow-sm"
      />

      <div className="flex-1">
        <div className="rounded-2xl px-5 py-3 shadow-sm transition-all duration-200">
          <div className="flex items-center justify-between">
            <div className="font-semibold">{user?.name}</div>
            <span className="text-xs ">
              {getTimeAgo(timestampDate(comment.createdAt!))}
            </span>
          </div>

          {isEditing ? (
            <div className="mt-2">
              <Textarea
                value={postComment}
                onChange={(e) => setpostComment(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
              />
              <div className="flex gap-2 mt-2">
                <button
                  onClick={handleEditComment}
                  className="flex items-center gap-1 px-3 py-1 text-sm font-medium bg-blue-600 rounded-lg hover:bg-blue-700">
                  <Save size={14} />
                  {getText("Save")}
                </button>
                <button
                  onClick={() => setIsEditing(false)}
                  className="flex items-center gap-1 px-3 py-1 text-sm font-medium  rounded-lg hover:bg-gray-300">
                  <X size={14} />
                  {getText("cancel")}
                </button>
              </div>
            </div>
          ) : (
            <div className=" mt-2 leading-relaxed">{comment.content}</div>
          )}
        </div>

        <div className="flex gap-4 text-xs mt-2 items-center ml-1">
          <button
            className={cn(
              "flex items-center gap-1 hover:text-blue-600 font-medium py-1 transition-colors duration-200",
              { "text-blue-600": isReplying, "": !isReplying },
            )}
            onClick={() => setIsReplying((prev) => !prev)}>
            <MessageSquare size={14} />
            {isReplying ? getText("Cancel") : getText("Reply")}
          </button>

          {(hasReplies || (replies && replies?.length > 0)) && (
            <button
              className={cn(
                "flex items-center gap-1 font-medium py-1 transition-colors duration-200",
                {
                  "text-blue-600": showReplies,
                  "hover:text-blue-600": !showReplies,
                },
              )}
              onClick={() => setShowReplies((prev) => !prev)}>
              {showReplies ? (
                <>
                  <ChevronUp size={14} />
                  {getText("Hide_Replies")}
                </>
              ) : (
                <>
                  <ChevronDown size={14} />
                  {getText("View_Replies")}
                </>
              )}
            </button>
          )}

          {comment.user?.id === authUser?.user?.id && (
            <button
              className={cn(
                "flex items-center gap-1 hover:text-blue-600 font-medium py-1 transition-colors duration-200",
                { "text-blue-600": isEditing, "": !isEditing },
              )}
              onClick={() => setIsEditing((prev) => !prev)}>
              <Edit size={14} />
              {isEditing ? getText("Editing") : getText("EDIT")}
            </button>
          )}
          {canDelete && (
            <button
              className="flex items-center gap-1 hover:text-red-600 font-medium py-1 transition-colors duration-200 "
              onClick={handleDeleteComment}>
              <Trash size={14} />
              {getText("Delete")}
            </button>
          )}
        </div>

        {isReplying && (
          <div className="mt-3 transition-all duration-300 animate-fadeIn">
            <PostCommentForm
              setShowReplies={(value) => {
                setShowReplies(true);
                setHasReplies(true);
              }}
              refetch={refetchReplies}
              className="p-3 rounded-xl border border-gray-100"
              postId={comment.postId}
              parentId={comment.id}
            />
          </div>
        )}

        {showReplies && (
          <div className="space-y-3 mt-4 pl-2 transition-all duration-300">
            {isPending && (
              <div className="flex justify-center py-4">
                <div className="animate-pulse flex space-x-2">
                  <div className="h-2 w-2 bg-blue-200 rounded-full"></div>
                  <div className="h-2 w-2 bg-blue-300 rounded-full"></div>
                  <div className="h-2 w-2 bg-blue-400 rounded-full"></div>
                </div>
              </div>
            )}

            {!isPending && replies?.length === 0 && (
              <div className="text-sm text-center italic py-2">
                {getText("No_Relies_Yet")}
              </div>
            )}

            {!isPending &&
              replies?.map((r) => (
                <PostComments
                  key={r.id}
                  refetch={refetchReplies}
                  comment={r}
                  depth={depth + 1}
                />
              ))}
          </div>
        )}
      </div>
    </div>
  );
};
