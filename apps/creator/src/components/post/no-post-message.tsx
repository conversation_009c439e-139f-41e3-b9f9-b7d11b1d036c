import { Link } from "@tanstack/react-router";
import { buttonVariants } from "@vtuber/ui/components/button";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { cn } from "@vtuber/ui/lib/utils";
import {
  ArrowRight,
  BookOpen,
  FileText,
  ImageIcon,
  PlusCircle,
  Sparkles,
  TrendingUp,
  Users,
  Video,
} from "lucide-react";
import { motion } from "motion/react";
import { floatingVariants } from "~/data/constatns";

export const NoPostMessage = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh] p-6 text-white">
      <div className="max-w-lg w-full text-center">
        <div className="relative w-64 h-64 mx-auto mb-8">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-purple-500/20 rounded-full blur-xl opacity-50" />

          <motion.div
            className="absolute"
            style={{ top: "30%", left: "15%" }}
            variants={floatingVariants}
            initial="initial"
            animate="animate"
            custom={0}>
            <div className="bg-green-500/90 p-3 rounded-full shadow-lg shadow-green-500/20">
              <FileText className="w-6 h-6 text-white" />
            </div>
          </motion.div>

          <motion.div
            className="absolute"
            style={{ top: "20%", right: "20%" }}
            variants={floatingVariants}
            initial="initial"
            animate="animate"
            custom={1}>
            <div className="bg-blue-500/90 p-3 rounded-full shadow-lg shadow-blue-500/20">
              <Video className="w-6 h-6 text-white" />
            </div>
          </motion.div>

          <motion.div
            className="absolute"
            style={{ bottom: "25%", right: "15%" }}
            variants={floatingVariants}
            initial="initial"
            animate="animate"
            custom={2}>
            <div className="bg-purple-500/90 p-3 rounded-full shadow-lg shadow-purple-500/20">
              <ImageIcon className="w-6 h-6 text-white" />
            </div>
          </motion.div>

          <motion.div
            className="absolute"
            style={{ bottom: "20%", left: "25%" }}
            variants={floatingVariants}
            initial="initial"
            animate="animate"
            custom={3}>
            <div className="bg-orange-500/90 p-3 rounded-full shadow-lg shadow-orange-500/20">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
          </motion.div>

          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="bg-slate-800 p-6 rounded-full border-4 border-slate-700 shadow-xl">
              <Sparkles className="w-12 h-12 text-slate-300" />
            </div>
          </div>
        </div>

        <h2 className="text-3xl font-bold mb-3 text-white">No Posts Yet</h2>
        <p className="text-slate-400 mb-8 max-w-sm mx-auto">
          Start sharing your content with the world. Create your first post to
          engage your audience.
        </p>

        <Link
          to="/posts/add"
          className={cn(
            buttonVariants({}),
            "w-full text-white font-medium py-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] bg-gradient-2",
          )}>
          <PlusCircle className="w-5 h-5 mr-2 text-black" />
          <span className="text-black">Create You First Post</span>
          <ArrowRight className="w-4 h-4 ml-2 text-black" />
        </Link>
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}>
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Users className="w-5 h-5 text-blue-400" />
              </div>
              <h3 className="font-medium text-white mb-1 text-sm">
                Engage Audience
              </h3>
              <p className="text-xs text-slate-400">
                Connect with your community through compelling content
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="w-5 h-5 text-purple-400" />
              </div>
              <h3 className="font-medium text-white mb-1 text-sm">
                Track Performance
              </h3>
              <p className="text-xs text-slate-400">
                Monitor likes, comments, and engagement metrics
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Sparkles className="w-5 h-5 text-green-400" />
              </div>
              <h3 className="font-medium text-white mb-1 text-sm">
                Rich Content
              </h3>
              <p className="text-xs text-slate-400">
                Support for text, images, videos, and more
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};
