import { useMutation } from "@connectrpc/connect-query";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService, ChangePasswordRequest } from "@vtuber/services/users";
import { But<PERSON> } from "@vtuber/ui/components/button";
import { Card } from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { Label } from "@vtuber/ui/components/label";
import { toast } from "@vtuber/ui/components/toaster";
import { useScrollInToView } from "@vtuber/ui/hooks/use-scroll-intoview";
import { useForm } from "react-hook-form";

export const PasswordForm = ({
  activeSection,
  triggerTimestamp,
}: {
  activeSection?: string | null;
  triggerTimestamp?: number;
}) => {
  const { ref } = useScrollInToView({
    trigger: activeSection === "password" ? triggerTimestamp : undefined,
    withHeader: true,
    headerHeight: 100,
  });
  const { getText } = useLanguage();
  const form = useForm<ChangePasswordRequest & { confirmPassword: string }>({
    defaultValues: {
      newPassword: "",
      oldPassword: "",
      confirmPassword: "",
    },
  });

  const mutation = useMutation(AuthService.method.changePassword, {
    onSuccess: (data) => {
      toast.success(data.message);
      form.reset();
    },
    onError: (err) => {
      toast.error(err.message);
      handleConnectError(err, form);
    },
  });

  const onSubmit = form.handleSubmit(({ confirmPassword, ...data }) => {
    if (confirmPassword !== data.newPassword) {
      form.setError("confirmPassword", {
        type: "server",
        message: "Password does not match",
      });
      return;
    }
    mutation.mutate(data);
  });

  return (
    <Form {...form}>
      <form
        className="flex flex-col gap-y-6"
        onSubmit={onSubmit}>
        <Card className="p-8 bg-sub">
          <section
            className="flex flex-col gap-y-3"
            ref={ref}>
            <div className=" flex items-center justify-between">
              <Label
                htmlFor="password"
                className="text-font font-medium text-2xl">
                パスワード
              </Label>
              <Button
                type="button"
                variant={"muted-outline"}
                className="rounded-full">
                変更する
              </Button>
            </div>
            <TextInput
              id="password"
              control={form.control}
              name="oldPassword"
              type="password"
              placeholder="＊＊＊＊＊＊＊＊＊＊"
              size={"lg"}
              variant={"material"}
            />
          </section>
          <section className="grid gap-y-6">
            <TextInput
              control={form.control}
              wrapperClassName="gap-y-3"
              name="newPassword"
              labelClassName="font-medium text-base"
              type="password"
              placeholder="＊＊＊＊＊＊＊＊＊＊"
              size={"lg"}
              label="新しいパスワード"
            />
            <TextInput
              control={form.control}
              wrapperClassName="gap-y-3"
              name="confirmPassword"
              type="password"
              label="確認用パスワード"
              labelClassName="font-medium text-base"
              placeholder="＊＊＊＊＊＊＊＊＊＊"
              size={"lg"}
            />
            <p className="text-font text-xs">
              ※半角で英大文字・英小文字・数字を含む8文字以上30文字以内で入力してください。
            </p>
            <Button
              variant={"success"}
              size={"lg"}
              className="w-max rounded-[3px]">
              新しいパスワードに変更する
            </Button>
          </section>
        </Card>
      </form>
    </Form>
  );
};
