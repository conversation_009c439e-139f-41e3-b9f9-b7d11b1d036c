import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { handleConnectError, OmitTypeName } from "@vtuber/services/client";
import { VtuberCategory } from "@vtuber/services/taxonomy";
import {
  AddVtuberProfileRequest,
  VtuberProfilesService,
} from "@vtuber/services/vtubers";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { MultiSelectInput } from "@vtuber/ui/components/form-inputs/multi-select-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export function CreateVtuberProfileCard({
  categories,
}: {
  categories?: VtuberCategory[];
}) {
  const router = useRouter();

  const form = useForm<OmitTypeName<AddVtuberProfileRequest>>({
    defaultValues: {
      bannerImage: "",
      description: "",
      displayName: "",
      furigana: "",
      image: "",
      username: "",
      socialMediaLinks: {},
      categories: [],
    },
  });

  const mutation = useMutation(VtuberProfilesService.method.addVtuberProfile, {
    onError: (err) => {
      handleConnectError(err, form);
    },
    onSuccess: () => {
      toast.success("Profile created successfully");
      router.invalidate();
    },
  });

  const onSubmit = form.handleSubmit((val) => {
    mutation.mutateAsync({
      ...val,
    });
  });

  return (
    <Card className="max-w-3xl w-full ">
      <CardHeader className="text-center ">
        <CardTitle>Create Vtuber Profile</CardTitle>
        <CardDescription>
          Enter the details below to create your Vtuber profile
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={onSubmit}
            className="space-y-4">
            <TextInput
              control={form.control}
              name="displayName"
              label="Display Name"
              placeholder="Display Name"
            />
            <TextInput
              control={form.control}
              name="username"
              label="Username"
              placeholder="Username"
            />
            <TextAreaInput
              control={form.control}
              name="description"
              label="Description"
              placeholder="Description"
            />
            <TextInput
              control={form.control}
              name="furigana"
              label="Furigana"
              placeholder="Furigana"
            />
            <MultiSelectInput
              control={form.control}
              name="categories"
              label="Categories"
              placeholder="Select categories"
              options={
                categories?.map((c) => ({
                  label: c.name,
                  value: c.id.toString(),
                })) || []
              }
            />
            <FileInput
              control={form.control}
              name="image"
              label="Image"
              fileUploadDescription="Recommended size: 1080x1080px"
            />
            <FileInput
              control={form.control}
              name="bannerImage"
              label="Banner Image"
              fileUploadDescription="Recommended size: 1920x1080px"
            />
            <TextInput
              control={form.control}
              type="url"
              inputMode="url"
              name="socialMediaLinks.instagram"
              label="Instagram Link"
              placeholder="Instagram Link"
            />
            <TextInput
              control={form.control}
              name="socialMediaLinks.tiktok"
              type="url"
              inputMode="url"
              label="Tiktok Link"
              placeholder="Tiktok Link"
            />

            <TextInput
              control={form.control}
              name="socialMediaLinks.twitter"
              type="url"
              inputMode="url"
              label="X Link"
              placeholder="X Link"
            />
            <TextInput
              control={form.control}
              name="socialMediaLinks.youtube"
              type="url"
              inputMode="url"
              label="Youtube Link"
              placeholder="Youtube Link"
            />
            <TextInput
              control={form.control}
              name="socialMediaLinks.twitch"
              type="url"
              inputMode="url"
              label="Twitch Link"
              placeholder="Twitch Link"
            />
            <TextInput
              control={form.control}
              name="socialMediaLinks.discord"
              type="url"
              inputMode="url"
              label="Discord Link"
              placeholder="Discord Link"
            />
            <Button
              loading={mutation.isPending}
              className="w-full">
              Create Profile
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
