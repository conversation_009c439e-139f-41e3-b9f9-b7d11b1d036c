import { createFileRoute, Navigate, Outlet } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { DictionaryKey } from "@vtuber/language/types";
import { vtuberCategoryClient } from "@vtuber/services/client";
import { AppSidebar } from "@vtuber/ui/components/app-sidebar";
import { HeaderBreadCrumb } from "@vtuber/ui/components/header-bread-crumb";
import { RoutePendingComponent } from "@vtuber/ui/components/route-pending-component";
import { Separator } from "@vtuber/ui/components/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@vtuber/ui/components/sidebar";
import { cn } from "@vtuber/ui/lib/utils";
import {
  BadgeDollarSign,
  Home,
  LucideIcon,
  Megaphone,
  Newspaper,
  PartyPopper,
  User,
} from "lucide-react";
import { useEffect, useState } from "react";
import { CreateVtuberProfileCard } from "~/components/CreateVtuberProfileCard";
import { CreatorAccessCard } from "~/components/CreatorAccessCard";

export const Route = createFileRoute("/_app")({
  component: RouteComponent,
  pendingComponent: () => <RoutePendingComponent />,
  loader: async () => {
    const [res] = await vtuberCategoryClient.getAllVtuberCategories({});
    return res?.categories;
  },
});

function RouteComponent() {
  const categories = Route.useLoaderData();
  const [scrolledValue, setScrolledValue] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const scrollValue = window.scrollY;
      setScrolledValue(scrollValue);
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const { getText } = useLanguage();
  const links: { title: DictionaryKey; icon: LucideIcon; url: string }[] = [
    {
      title: "Dashboard",
      icon: Home,
      url: "/",
    },
    {
      title: "Plans",
      icon: BadgeDollarSign,
      url: "/plans",
    },
    {
      title: "CAMPAIGNS",
      icon: Megaphone,
      url: "/campaigns",
    },
    {
      title: "Posts",
      icon: Newspaper,
      url: "/posts",
    },

    {
      title: "Events",
      icon: PartyPopper,
      url: "/event",
    },
    // {
    //   title: "notifications",
    //   icon: BellIcon,
    //   url: "/notifications",
    // },
    {
      title: "Profile_Settings",
      icon: User,
      url: "/profile",
    },
  ];

  const { session, isCreator, hasVtuberProfile, logout } = useAuth();

  if (!session) {
    return <Navigate to="/login" />;
  }

  if (!isCreator) {
    return (
      <div className="w-full min-h-screen py-10 px-4 flex justify-center items-center">
        <CreatorAccessCard />
      </div>
    );
  }

  if (!hasVtuberProfile) {
    return (
      <div className="w-full min-h-screen px-4 py-10 flex justify-center items-center">
        <CreateVtuberProfileCard categories={categories} />
      </div>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar
        links={links}
        session={session}
        logout={logout}
      />
      <SidebarInset>
        <header
          className={cn(
            "flex h-16 shrink-0 items-center gap-2",
            scrolledValue > 200 &&
              "bg-background sticky z-50 w-full top-0 left-0 animate-slide-in-down border-b",
          )}>
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 h-4"
            />
            <HeaderBreadCrumb
              isUser={false}
              className="relative z-20"
              homeText={getText("Dashboard")}
            />
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <Outlet />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
