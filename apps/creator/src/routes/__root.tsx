import { TransportProvider } from "@connectrpc/connect-query";
import type { QueryClient } from "@tanstack/react-query";
import {
  createRootRouteWithContext,
  HeadContent,
  Outlet,
  Scripts,
} from "@tanstack/react-router";
import {
  AuthProvider,
  CategoriesProvider,
  CreatorParticipatedEventProvider,
} from "@vtuber/auth/provider";
import { LanguageProvider } from "@vtuber/language/provider";
import {
  authClient,
  categoryClient,
  eventParticipantClient,
  transport,
} from "@vtuber/services/client";
import { DefaultCatchBoundary } from "@vtuber/ui/components/default-catch-boundry";
import { Toaster } from "@vtuber/ui/components/sonner";
import * as React from "react";
import { seo } from "~/utils/seo";

import { userPromise } from "@vtuber/auth/provider";
import css from "@vtuber/ui/globals.css?url";

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient;
}>()({
  head: () => ({
    meta: [
      {
        charSet: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      ...seo({
        title: "V-Sai | Creator",
        description: `VTUBER CREATOR`,
      }),
    ],
    links: [
      { rel: "stylesheet", href: css },
      {
        rel: "apple-touch-icon",
        sizes: "180x180",
        href: "/apple-touch-icon.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "32x32",
        href: "/favicon-32x32.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "16x16",
        href: "/favicon-16x16.png",
      },
      { rel: "manifest", href: "/site.webmanifest", color: "#fffff" },
      { rel: "icon", href: "/favicon.ico" },
    ],
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    );
  },
  component: RootComponent,
  loader: async () => {
    const language = getCookie("language");

    const [categories] = await categoryClient.getAllCategories({});
    const [creatorParticipatedEvents] =
      await eventParticipantClient.getCreatorEventParticipation({});
    const [user] = await authClient.getSession({});
    userPromise.resolve(user);
    return {
      language: (["en", "ja"].includes(language) ? language : "ja") as
        | "en"
        | "ja",
      categories,
      creatorParticipatedEvents,
      user,
    };
  },
  beforeLoad: async () => {
    return {
      user: userPromise.promise,
    };
  },
  shouldReload: false,
});

function RootComponent() {
  const { categories, language, creatorParticipatedEvents, user } =
    Route.useLoaderData();

  return (
    <RootDocument>
      <TransportProvider transport={transport}>
        <AuthProvider user={user}>
          <LanguageProvider defaultLanguage={language || "ja"}>
            <CategoriesProvider categories={categories?.categories || null}>
              <CreatorParticipatedEventProvider
                creatorParticipatedEvents={
                  creatorParticipatedEvents?.events.map(String) || null
                }>
                <Toaster
                  richColors
                  position="top-center"
                />
                <Outlet />
              </CreatorParticipatedEventProvider>
            </CategoriesProvider>
          </LanguageProvider>
        </AuthProvider>
      </TransportProvider>
    </RootDocument>
  );
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html className="light">
      <head>
        <HeadContent />
      </head>
      <body>
        {children}
        <Scripts />
      </body>
    </html>
  );
}
