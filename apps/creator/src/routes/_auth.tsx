import {
  createFileRoute,
  <PERSON>,
  Navigate,
  Outlet,
} from "@tanstack/react-router";
import { useSession } from "@vtuber/auth/hooks";
import { Card } from "@vtuber/ui/components/card";
import { Container } from "@vtuber/ui/components/container";
import { Logo } from "@vtuber/ui/components/logo";
export const Route = createFileRoute("/_auth")({
  component: RouteComponent,
});

function RouteComponent() {
  const user = useSession();

  if (user) {
    return <Navigate to="/" />;
  }

  return (
    <main
      className="bg-no-repeat bg-center bg-cover min-h-screen flex flex-col pb-10"
      style={{
        backgroundImage:
          "linear-gradient(92deg, rgba(133, 243, 251, 0.10), rgba(147, 118, 205, 0.10)), url('https://cdn.v-sai.com/assets/auth_bg.png')",
      }}>
      <div className="h-[80px] flex items-center px-20 sticky top-0 z-50">
        <Link to="/">
          <Logo />
        </Link>
      </div>
      <Container className="flex items-center justify-center flex-1">
        <Card
          style={{
            filter: "drop-shadow(0px 0px 25px rgba(0, 0, 0, 0.25))",
          }}>
          <Outlet />
        </Card>
      </Container>
    </main>
  );
}
