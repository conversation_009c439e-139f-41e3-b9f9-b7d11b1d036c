import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService, VerifyChangeEmailRequest } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import { Card } from "@vtuber/ui/components/card";
import { Container } from "@vtuber/ui/components/container";
import { Form } from "@vtuber/ui/components/form";
import { PinInput } from "@vtuber/ui/components/form-inputs/pin-input";
import { ArrowRight, Shield } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

export const Route = createFileRoute("/_protected/email/verify")({
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = useNavigate();
  const { getText } = useLanguage();
  const form = useForm<VerifyChangeEmailRequest>({
    defaultValues: {
      verificationCode: "",
    },
  });

  const { mutateAsync, isPending } = useMutation(
    AuthService.method.verifyChangeEmail,
    {
      onSuccess: (data) => {
        navigate({
          to: "/email/new-email",
          search: {
            token: data.token,
          },
        });
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const {
    mutateAsync: sendVerificationCode,
    isPending: isSendingVerificationCode,
  } = useMutation(AuthService.method.changeEmailVerification, {
    onSuccess: (data) => {
      toast.success(data.message);
    },
    onError: (err) => {
      handleConnectError(err);
    },
  });

  const verificationCode = form.watch("verificationCode");

  return (
    <Container
      variant="center"
      className="min-h-[90dvh]">
      <Card className="relative w-full max-w-md p-8 bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-4 rounded-xl mb-4 shadow-lg">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">
            {getText("verification_code_label")}
          </h1>
          <p className="text-slate-400 text-sm">
            {getText("enter_verification_code_sent_to_your_email")}
          </p>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(async (data) => {
              await mutateAsync(data);
            })}
            className="space-y-6">
            <PinInput
              required
              pattern="digitsAndChars"
              containerClassName="justify-between"
              labelClassName="mb-3"
              label={getText("verification_code")}
              placeholder="•"
              control={form.control}
              name="verificationCode"
              maxLength={6}
              className="xs:size-12 size-10"
            />

            <Button
              type="submit"
              loading={isPending}
              size={"xl"}
              disabled={isSendingVerificationCode || !verificationCode}
              className="w-full bg-gradient-4 font-medium rounded-lg shadow-lg hover:shadow-xl group">
              <div className="flex items-center space-x-2">
                <span className="capitalize">{getText("verify_code")}</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </div>
            </Button>

            <div className="text-center">
              <p className="text-slate-400 text-sm mb-2">
                {getText("did_not_receive_code")}
              </p>
              <Button
                variant={"link"}
                type="button"
                loading={isSendingVerificationCode}
                onClick={async () => {
                  await sendVerificationCode({});
                }}
                className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors disabled:opacity-50">
                {getText("send_verification_code")}
              </Button>
            </div>
          </form>
        </Form>

        <div className="mt-8 pt-6 border-t border-slate-700/50">
          <p className="text-xs text-slate-500 text-center">
            {getText("having_trouble_contact_support")}
          </p>
        </div>
      </Card>
    </Container>
  );
}
