import { createFileRoute, useNavigate } from "@tanstack/react-router";
import {
  campaignBannerClient,
  campaignClient,
  campaignVariantClient,
} from "@vtuber/services/client";
import {
  Stepper,
  StepperIndicator,
  StepperItem,
  StepperTrigger,
} from "@vtuber/ui/components/stepper";
import { useState } from "react";
import { z } from "zod";
import { CampaignBannerForm } from "~/components/campaigns/campaign-banner-form";
import { CampaignForm } from "~/components/campaigns/campaign-form";
import { CampaignVariantForm } from "~/components/campaigns/campaign-variant-form";

export const Route = createFileRoute("/_app/campaigns/add")({
  component: RouteComponent,
  validateSearch: z.object({
    step: z.number().optional(),
    campaignId: z.string().optional(),
  }),
  loader: async ({ location }) => {
    const id = (location.search as any)?.campaignId;

    if (!id) return {};
    const [campaign] = await campaignClient.getCampaignById({
      id,
    });
    const [banners] = await campaignBannerClient.getBannerByCampaignId({
      campaignId: String(id),
    });
    const [variants] = await campaignVariantClient.getAllCampaignVariants({
      campaignId: String(id),
    });
    return { campaign, banners, variants };
  },
});

function RouteComponent() {
  return <Component />;
}

export default function Component() {
  const data = Route.useLoaderData();
  const { step, campaignId } = Route.useSearch();
  const [currentStep, setCurrentStep] = useState(step ?? 1);
  const navigate = useNavigate({ from: Route.fullPath });

  const onNext = (id?: string) => {
    setCurrentStep((prev) => prev + 1);
    navigate({
      search: {
        step: currentStep + 1,
        campaignId: id,
      },
      mask: {
        search: {
          step: undefined,
          campaignId: undefined,
        },
      },
    });
  };

  const onPrev = () => {
    setCurrentStep((prev) => prev - 1);
    navigate({
      search: (prev) => ({
        ...prev,
        step: currentStep - 1,
      }),
      mask: {
        search: {
          step: undefined,
          campaignId: undefined,
        },
      },
    });
  };

  return (
    <div className="pt-10">
      {currentStep === 1 && (
        <CampaignForm
          onNext={onNext}
          campaign={data?.campaign?.data}
        />
      )}
      {currentStep === 2 && (
        <CampaignBannerForm
          onNext={onNext}
          onPrev={onPrev}
          campaign={data?.campaign?.data}
          campaignId={campaignId || ""}
        />
      )}
      {currentStep === 3 && (
        <CampaignVariantForm
          campaign={data?.campaign?.data}
          onPrev={onPrev}
          campaignId={campaignId || ""}
        />
      )}
      <section className="mx-auto max-w-xl space-y-8 text-center">
        <div className="space-y-3">
          <Stepper
            value={currentStep}
            onValueChange={(s) => {
              setCurrentStep(s);
              navigate({
                search: {
                  step: s,
                },
              });
            }}>
            <StepperItem
              step={1}
              className="flex-1">
              <StepperTrigger
                className="w-full flex-col items-start gap-2"
                asChild>
                <StepperIndicator
                  asChild
                  className="bg-border h-2 w-full rounded-none">
                  <span className="sr-only">Campaign Basic Information</span>
                </StepperIndicator>
              </StepperTrigger>
            </StepperItem>
            <StepperItem
              step={2}
              className="flex-1">
              <StepperTrigger
                className="w-full flex-col items-start gap-2"
                asChild>
                <StepperIndicator
                  asChild
                  className="bg-border h-2 w-full rounded-none">
                  <span className="sr-only">Campaign Banners</span>
                </StepperIndicator>
              </StepperTrigger>
            </StepperItem>
            <StepperItem
              step={3}
              className="flex-1">
              <StepperTrigger
                className="w-full flex-col items-start gap-2"
                asChild>
                <StepperIndicator
                  asChild
                  className="bg-border h-2 w-full rounded-none">
                  <span className="sr-only">Campaign Varations</span>
                </StepperIndicator>
              </StepperTrigger>
            </StepperItem>
          </Stepper>
          <div className="text-muted-foreground text-sm font-medium tabular-nums">
            Step {currentStep} of 3
          </div>
        </div>
      </section>
    </div>
  );
}
