import { timestampDate } from "@bufbuild/protobuf/wkt";
import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { GetCampaignSubResponse } from "@vtuber/services/campaigns";
import { campaignVariantClient } from "@vtuber/services/client";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { ColumnDef, DataTable } from "@vtuber/ui/components/data-table";
import { Image } from "@vtuber/ui/components/image";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Progress } from "@vtuber/ui/components/progress";
import { Skeleton } from "@vtuber/ui/components/skeleton";
import { Calendar, Clock, DollarSign, Users } from "lucide-react";
export const Route = createFileRoute("/_app/campaigns/variant/$variantId")({
  component: RouteComponent,
  loader: async ({ params }) => {
    try {
      const [variant] = await campaignVariantClient.getCampaignVariantById({
        id: String(params.variantId),
      });
      const [subscribers] = await campaignVariantClient.getCampaignVariantSubs({
        id: String(params.variantId),
      });
      return { variant, subscribers, isLoading: false, error: null };
    } catch (error) {
      return {
        variant: null,
        subscribers: null,
        isLoading: false,
        error: "Failed to load campaign data",
      };
    }
  },
});

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(date);
};

const columns: ColumnDef<GetCampaignSubResponse["subs"][0]>[] = [
  {
    accessorKey: "image",
    header: "Image",
    cell: ({ row }) => (
      <Avatar
        src={row.original.image}
        className="h-10 w-10"
      />
    ),
  },
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => <div className="font-medium">{row.original.name}</div>,
  },
  {
    header: "Amount",
    accessorKey: "amount",
    cell: ({ row }) => <div className="font-medium">{row.original.amount}</div>,
  },
];

function CampaignSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-64 w-full rounded-xl" />
      <Skeleton className="h-12 w-3/4 rounded-lg" />
      <Skeleton className="h-8 w-1/4 rounded-lg" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-full rounded" />
        <Skeleton className="h-4 w-full rounded" />
        <Skeleton className="h-4 w-2/3 rounded" />
      </div>
    </div>
  );
}

function RouteComponent() {
  const { variant, subscribers, isLoading, error } = Route.useLoaderData();
  const { getText } = useLanguage();
  const maxSubs = variant?.data?.maxSub || 0;
  const currentSubs = variant?.data?.subCount || 0;
  const progressPercentage =
    maxSubs > 0 ? (Number(currentSubs) / maxSubs) * 100 : 0;
  const hasSubscribers = subscribers?.subs && subscribers.subs.length > 0;

  if (isLoading) return <CampaignSkeleton />;
  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center py-8">
          <h3 className="text-lg font-medium text-red-600 mb-2">
            Error Loading Campaign
          </h3>
          <p className="text-gray-500">{error}</p>
          <Button
            className="mt-4"
            variant="outline">
            Try Again
          </Button>
        </div>
      </Card>
    );
  }

  if (!variant?.data) {
    return (
      <Card className="p-6">
        <div className="text-center py-8">
          <h3 className="text-lg font-medium mb-2">Variant Not Found</h3>
          <p className="text-gray-500">
            The Variant you're looking for doesn't exist or has been removed.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <div className="">
      <Image
        src={variant.data.image}
        alt={variant.data.title}
        className="w-full h-80 object-cover"
      />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{variant.data.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <MarkDown markdown={variant?.data?.description} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users size={18} />
                <span>Subscribers</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {!hasSubscribers ? (
                <div className="text-center py-6 rounded-lg">
                  <p>{getText("No_Subscribers_Yet")}</p>
                </div>
              ) : (
                <div className="rounded-lg overflow-hidden border">
                  <DataTable
                    columns={columns}
                    data={subscribers?.subs || []}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Variant Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-sm font-medium mb-2">
                  Subscription Progress
                </h3>
                <Progress
                  value={progressPercentage}
                  className="h-2"
                />
                <div className="flex justify-between mt-2 text-sm">
                  <span>{currentSubs} subscribers</span>
                  <span>{maxSubs} max</span>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <DollarSign className="h-5 w-5 " />
                  <div>
                    <p className="text-sm font-medium ">Price</p>
                    <p className="text-lg font-bold">{variant?.data?.price}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5" />
                  <div>
                    <p className="text-sm font-medium">Created On</p>
                    <p className="text-base">
                      {variant?.data?.createdAt
                        ? formatDate(timestampDate(variant.data.createdAt))
                        : "N/A"}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5" />
                  <div>
                    <p className="text-sm font-medium ">Status</p>
                    <Badge
                      variant={
                        currentSubs < maxSubs ? "success" : "destructive"
                      }>
                      {currentSubs < maxSubs ? "Open" : "Filled"}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
