import { createFileRoute, notFound } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { campaignClient, campaignVariantClient } from "@vtuber/services/client";
import { ExternalLinks } from "@vtuber/ui/components/external-links";
import { z } from "zod";
import { CampaignDetailsBanner } from "~/components/campaigns/CampaignBanners";
import { CampaignProgress } from "~/components/campaigns/CampaignProgress";
import { CampaignTab } from "~/components/campaigns/CampaignTab";
import { CampaignVariants } from "~/components/campaigns/CampaignVariants";

export const Route = createFileRoute("/_app/campaigns/$id/")({
  component: RouteComponent,
  validateSearch: z.object({
    tab: z.string().optional(),
  }),
  loader: async ({ params }) => {
    const [campaign, err] = await campaignClient.getCampaignById({
      id: params.id,
    });
    if (err)
      throw notFound({
        data: err.rawMessage,
      });
    const [variants] = await campaignVariantClient.getAllCampaignVariants({
      campaignId: String(params.id),
    });
    return { campaign, variants };
  },
});

function RouteComponent() {
  const { campaign, variants } = Route.useLoaderData();
  const { getText } = useLanguage();

  if (!campaign || !campaign.data) {
    return (
      <p className="text-center text-lg">{getText("Campaign_Not_Found")}</p>
    );
  }

  return (
    <div className="animate-fade-in">
      <CampaignDetailsBanner banners={campaign.data.banners} />
      <div className="p-6">
        <div className="flex md:flex-row flex-col md:items-center md:justify-between pb-8">
          <h1 className="text-4xl font-bold mb-6 capitalize">
            {campaign.data?.name}
          </h1>
          <ExternalLinks socialMediaLinks={campaign.data.socialMediaLinks} />
        </div>

        <div className="grid grid-cols-12 items-start gap-6">
          <div className="lg:col-span-8 col-span-12">
            <CampaignTab
              campaign={campaign.data}
              id={campaign.data.id}
            />
          </div>
          <div className="lg:col-span-4 col-span-12 space-y-3">
            <CampaignProgress campaign={campaign.data} />
            <CampaignVariants campaign={campaign.data} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default RouteComponent;
