import { createFileRoute, Link, notFound } from "@tanstack/react-router";
import { campaignClient } from "@vtuber/services/client";
import { buttonVariants } from "@vtuber/ui/components/button";
import { Paginator } from "@vtuber/ui/components/paginator";
import { Plus } from "lucide-react";
import { CampaignCard } from "~/components/campaigns/CampaignCard";
import { NocampaignsMessage } from "~/components/campaigns/no-campaigns-message";
import { validatePagination } from "~/data/constatns";

export const Route = createFileRoute("/_app/campaigns/")({
  component: RouteComponent,
  validateSearch: validatePagination,
  loader: async ({ deps }) => {
    const [campaigns, err] = await campaignClient.getMyCampaigns({
      pagination: {
        sort: "created_at",
        size: 20,
        order: "desc",
        ...deps,
      },
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }
    return campaigns;
  },
  loaderDeps: ({ search }) => search,
});

function RouteComponent() {
  const campaigns = Route.useLoaderData();

  if (!campaigns?.data || campaigns.data.length === 0)
    return <NocampaignsMessage />;

  return (
    <div className="container pt-10">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold">Campaigns</h1>
          <p className="text-slate-400 mt-1">
            Manage your active and upcoming campaigns
          </p>
        </div>
        <Link
          to="/campaigns/add"
          className={buttonVariants({
            variant: "success",
            size: "xl",
            className: "!rounded-lg",
          })}>
          <Plus className="w-4 h-4 mr-2" />
          Add Campaign
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {campaigns?.data.map((campaign) => (
          <CampaignCard
            key={campaign.id.toString()}
            campaign={campaign}
          />
        ))}
      </div>
      <div className="pt-8">
        <Paginator
          currentPage={campaigns?.paginationDetails?.currentPage}
          totalPages={campaigns?.paginationDetails?.totalPages}
        />
      </div>
    </div>
  );
}
