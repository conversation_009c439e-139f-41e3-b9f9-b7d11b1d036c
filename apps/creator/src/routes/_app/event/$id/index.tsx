import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute } from "@tanstack/react-router";
import { useCreatorParticipatedEvents } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { eventClient, eventParticipantClient } from "@vtuber/services/client";
import { EventParticipantService } from "@vtuber/services/events";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Media } from "@vtuber/ui/components/media";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { Calendar, Check, ChevronRight, Users } from "lucide-react";
import { toast } from "sonner";
import { z } from "zod";
import { EventTab } from "~/components/events/EventTab";

export const Route = createFileRoute("/_app/event/$id/")({
  component: EventDetailsPage,
  validateSearch: z.object({
    tab: z.string().optional(),
    commentId: z.number().optional(),
  }),
  loader: async ({ params }) => {
    const [event] = await eventClient.getEventById({
      id: params.id,
    });
    const [creatorParticipants] =
      await eventParticipantClient.getEventParticipantsByEventId({
        eventId: params.id,
      });
    return { event, creatorParticipants };
  },
});

function EventDetailsPage() {
  const { getText } = useLanguage();
  const { event, creatorParticipants } = Route.useLoaderData();
  const { creatorParticipation, setCreatorParticipation } =
    useCreatorParticipatedEvents();

  if (!event?.data) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-300">
          Event not found
        </h2>
        <p className="mt-2 text-gray-500">
          The event you're looking for doesn't exist or has been removed.
        </p>
        <Button
          className="mt-6"
          variant="outline">
          Back to Events
        </Button>
      </div>
    );
  }
  const { data } = event;

  const addMutation = useMutation(
    EventParticipantService.method.addEventParticipation,
    {
      onSuccess(data) {
        const creatorParticipations = [
          ...creatorParticipation,
          event?.data?.id!,
        ];
        setCreatorParticipation(creatorParticipations);
        toast.success(data.message);
      },
      onError(err) {
        toast.error(err.message);
      },
    },
  );

  const addEventParticipation = (eventId: string) => {
    addMutation.mutate({
      eventId,
    });
  };

  const hasParticipated = creatorParticipation.includes(event.data.id);
  const formattedStartDate = event.data.startDate
    ? timestampDate(event.data.startDate).toLocaleDateString(undefined, {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : "To be announced";

  const formattedEndDate = event.data.endDate
    ? timestampDate(event.data.endDate).toLocaleDateString(undefined, {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : "To be announced";

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "approved":
        return "success";
      case "rejected":
        return "destructive";
      case "pending":
        return "secondary";
      default:
        return "secondary";
    }
  };

  if (!event.data) return null;

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <Card className="overflow-hidden border shadow-lg rounded-xl">
            <div className="relative aspect-video">
              <Media
                src={data.image}
                alt={data.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute bottom-0 left-0 right-0 p-5 to-transparent">
                <Badge
                  variant={getStatusVariant(data.status)}
                  className="capitalize text-sm px-3 py-1 mb-2 shadow-sm">
                  {data.status}
                </Badge>
                <h1 className="text-2xl md:text-3xl font-bold leading-tight">
                  {data.title}
                </h1>
              </div>
            </div>

            <CardContent className="pt-6 px-5">
              <p className="text-lg mb-8 leading-relaxed border-b pb-6">
                {data.shortDescription}
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-8">
                {[
                  {
                    icon: <Calendar className="h-5 w-5 mr-3" />,
                    label: getText("start_date"),
                    value: formattedStartDate,
                  },
                  {
                    icon: <Calendar className="h-5 w-5 mr-3" />,
                    label: getText("end_date"),
                    value: formattedEndDate,
                  },
                ].map((item, index) => (
                  <div
                    key={index}
                    className="flex items-start p-3  rounded-lg transition-colors">
                    <div className="mt-0.5">{item.icon}</div>
                    <div>
                      <p className="text-xs uppercase tracking-wider">
                        {item.label}
                      </p>
                      <p className="font-medium">{item.value}</p>
                    </div>
                  </div>
                ))}
              </div>

              <Button
                className="w-full py-6 text-base font-semibold transition-all"
                variant={hasParticipated ? "default" : "add"}
                onClick={() => addEventParticipation(data.id)}
                disabled={hasParticipated}
                size="lg">
                {hasParticipated ? (
                  <div className="flex items-center">
                    <Check className="h-5 w-5 mr-2" />
                    {getText("Participated")}
                  </div>
                ) : (
                  getText("Participate")
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
        <div className="space-y-6">
          {/* <Card className="border-none bg-gradient-3 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <CardHeader>
              <CardTitle className="text-xl font-bold flex items-center">
                <Award className="h-6 w-6 mr-2 text-indigo-600 animate-bounce" />
                <span className="bg-clip-text text-transparent bg-gradient-2">
                  {getText("Prizes")}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="group flex items-start gap-4 p-3 rounded-lg">
                  <div className="relative">
                    <Trophy className="h-8 w-8 text-yellow-500" />
                    <Crown className="absolute -top-2 -right-2 h-4 w-4 text-yellow-600" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg">
                      {getText("First_Place")}
                    </h4>
                    <p className="flex items-center gap-2">
                      <span className="font-medium text-indigo-500">$500</span>
                      <Plus className="h-4 w-4" />
                      <span className="flex items-center">
                        <Star className="h-4 w-4 mr-1 text-yellow-400" />
                        {getText("Featured_Spotlight")}
                      </span>
                    </p>
                  </div>
                </div>

                <div className="group flex items-start gap-4 p-3 rounded-lg  transition-all duration-200">
                  <Medal className="h-8 w-8 text-amber-500 text-silver-500" />
                  <div>
                    <h4 className="font-bold text-lg ">
                      {getText("Second_Place")}
                    </h4>
                    <p className=" flex items-center gap-2">
                      <span className="font-medium text-indigo-500">$300</span>
                      <Plus className="h-4 w-4" />
                      <span className="flex items-center">
                        <Megaphone className="h-4 w-4 mr-1 text-yellow-500" />
                        {getText("Platform_promotion")}
                      </span>
                    </p>
                  </div>
                </div>

                <div className="group flex items-start gap-4 p-3 rounded-lg ">
                  <Award className="h-8 w-8 text-yellow-500" />
                  <div>
                    <h4 className="font-bold text-lg transition-colors">
                      {getText("Third_Place")}
                    </h4>
                    <p className="flex items-center gap-2">
                      <span className="font-medium text-indigo-500">$150</span>
                      <Plus className="h-4 w-4 " />
                      <span className="flex items-center">
                        <Sparkles className="h-4 w-4 mr-1 text-amber-500" />
                        {getText("Special_Mention")}
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card> */}

          <Card className="border-none bg-gradient-3 shadow-lg rounded-xl overflow-hidden">
            <CardHeader className="sticky top-0 z-10 bg-gradient-2  backdrop-blur-sm border-b">
              <CardTitle className="text-lg font-bold flex items-center text-white">
                <Users className="h-5 w-5 mr-2" />
                {getText("Participants")} (
                {creatorParticipants?.data?.length || 0})
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="max-h-[80dvh] overflow-y-auto">
                <div className="space-y-3 p-4">
                  {creatorParticipants?.data?.map((participant, index) => (
                    <div
                      key={index}
                      className="flex items-center p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-200 cursor-pointer group">
                      <Avatar
                        src={getCdnUrl(participant.vtuber?.image)}
                        alt={participant.vtuber?.name}
                        className="h-11 w-11 mr-3 ring-2 ring-indigo-400/50 group-hover:ring-indigo-300 transition-all"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="font-semibold text-white truncate capitalize">
                          {participant.vtuber?.name}
                        </p>
                      </div>
                      <ChevronRight className="h-4 w-4 text-indigo-300 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>
      <EventTab
        event={data}
        id={data.id}
      />
    </div>
  );
}
