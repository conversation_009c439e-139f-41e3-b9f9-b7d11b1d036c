import { createFileRoute } from "@tanstack/react-router";
import { eventClient } from "@vtuber/services/client";
import { Event } from "@vtuber/services/events";
import { EventForm } from "~/components/events/EventForm";

export const Route = createFileRoute("/_app/event/$id/edit")({
  component: RouteComponent,
  loader: async ({ params }) => {
    const [data] = await eventClient.getEventById({
      id: params.id,
    });
    return data?.data;
  },
});

function RouteComponent() {
  const data = Route.useLoaderData();
  return (
    <EventForm
      mode="edit"
      defaultValues={{ asVtuber: true, ...data } as Event}
    />
  );
}
