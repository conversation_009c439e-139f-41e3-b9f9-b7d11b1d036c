import { createFileRout<PERSON>, <PERSON>, useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { buttonVariants } from "@vtuber/ui/components/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { Plus } from "lucide-react";
import { useState } from "react";
import { z } from "zod";
import { AllEvents } from "~/components/events/all-events";
import { MyEvents } from "~/components/events/my-events";
import { ParticipatedEvents } from "~/components/events/participated-events";

export const Route = createFileRoute("/_app/event/")({
  component: RouteComponent,
  validateSearch: z.object({
    page: z.number().optional(),
    size: z.number().optional(),
    sort: z.string().optional(),
    order: z.string().optional(),
    tab: z.string().optional(),
  }),
});

function RouteComponent() {
  const navigate = useNavigate();
  const search = Route.useSearch();
  const [tabValue, setTabValue] = useState(search?.tab || "myEvents");
  const { getText } = useLanguage();
  return (
    <div className="pt-10">
      <Tabs
        variant={"material"}
        value={tabValue}
        onValueChange={(tab) => {
          setTabValue(tab);
          navigate({
            to: "/event",
            search: {
              tab,
            },
          });
        }}>
        <TabsList className="sm:gap-x-0 gap-x-0 md:justify-start justify-center">
          <TabsTrigger
            value="allEvents"
            className="px-6">
            {getText("All_Events")}
          </TabsTrigger>
          <TabsTrigger
            value="myEvents"
            className="px-6">
            {getText("My_Events")}
          </TabsTrigger>
          <TabsTrigger
            value="participatedEvents"
            className="px-6">
            {getText("Participated_Events")}
          </TabsTrigger>
        </TabsList>
        <TabsContent
          value="allEvents"
          className="rounded-b-lg">
          <AllEvents tabValue={tabValue} />
        </TabsContent>
        <TabsContent
          value="myEvents"
          className="rounded-b-lg">
          <div className="p-10 pt-16">
            <div className="flex justify-end items-center mb-4">
              <Link
                to="/event/add"
                className={buttonVariants({
                  variant: "success",
                  size: "xl",
                  className: "!rounded-lg",
                })}>
                <Plus className="size-5" /> {getText("Add_Event")}
              </Link>
            </div>
            <MyEvents tabValue={tabValue} />
          </div>
        </TabsContent>
        <TabsContent
          value="participatedEvents"
          className="rounded-b-lg">
          <ParticipatedEvents tabValue={tabValue} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
