import { timestampDate } from "@bufbuild/protobuf/wkt";
import { createFileRoute } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { getCdnUrl } from "@vtuber/ui/lib/utils";
import { CheckCheck, CircleX } from "lucide-react";
import { PasswordForm } from "~/components/Auth/PasswordForm";
import { AccountForm } from "~/components/settings/account-form";
import { UserEmailUpdater } from "~/components/UserEmailUpdated";

export const Route = createFileRoute("/_app/account")({
  component: RouteComponent,
});

function RouteComponent() {
  const { session: user } = useAuth();
  return (
    <div className="p-20">
      <h1 className="text-4xl font-bold">Profile</h1>
      <div className="grid grid-cols-12 items-start gap-6 pt-6">
        <div className="md:col-span-4 col-span-12">
          <Card>
            <CardHeader className="flex flex-col items-center">
              <Avatar
                className="h-36 w-36 text-6xl font-semibold"
                fallback={user?.vtuber?.displayName}
                src={getCdnUrl(user?.user?.image)}
                alt={user?.user?.fullName}
              />
              <CardTitle className="text-4xl font-bold pt-3 text-center">
                {user?.user?.fullName}
              </CardTitle>
              <CardDescription className="text-center">
                <a href={`mailto:${user?.user?.email}`}>{user?.user?.email}</a>
              </CardDescription>
              <div className="flex items-center gap-3 flex-wrap">
                <Badge
                  variant={user?.user?.emailVerified ? "success" : "secondary"}
                  className="gap-1">
                  EMAIL VERIFIED{" "}
                  {user?.user?.emailVerified ? (
                    <CheckCheck size={16} />
                  ) : (
                    <CircleX size={16} />
                  )}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="text-center">
              <p>
                Joined on{" "}
                {user?.vtuber?.createdAt &&
                  timestampDate(user?.vtuber?.createdAt!).toDateString()}
              </p>
            </CardContent>
          </Card>
        </div>
        <div className="md:col-span-8 col-span-12 space-y-10">
          <AccountForm />
          <UserEmailUpdater />
          <PasswordForm />
        </div>
      </div>
    </div>
  );
}
