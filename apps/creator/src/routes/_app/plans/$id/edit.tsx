// src/routes/_app/plans/$id/edit.tsx
import { createFileRoute, notFound } from "@tanstack/react-router";
import { vtuberPlanClient } from "@vtuber/services/client";
import { VtuberPlanForm } from "~/components/plans/vtuber-plan-form";

export const Route = createFileRoute("/_app/plans/$id/edit")({
  component: RouteComponent,
  loader: async ({ params }) => {
    const [data, err] = await vtuberPlanClient.getVtuberPlanById({
      id: String(params.id),
    });
    if (err || !data.data) {
      throw notFound({
        data: err?.rawMessage,
      });
    }
    return data?.data;
  },
});

function RouteComponent() {
  const data = Route.useLoaderData();

  return <VtuberPlanForm defaultValues={data} />;
}
