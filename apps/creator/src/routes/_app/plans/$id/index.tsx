import { timestampDate } from "@bufbuild/protobuf/wkt";
import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import {
  vtuberPlanClient,
  vtuberUserSubscriptionClient,
} from "@vtuber/services/client";

import { VtuberUserSubscription } from "@vtuber/services/vtubers";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { ColumnDef, DataTable } from "@vtuber/ui/components/data-table";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Progress } from "@vtuber/ui/components/progress";
import { Separator } from "@vtuber/ui/components/separator";
import { Skeleton } from "@vtuber/ui/components/skeleton";
import {
  AlertCircle,
  CalendarClock,
  CheckCircle2,
  Crown,
  DollarSign,
  TrendingUp,
  Users,
} from "lucide-react";
import { useMemo } from "react";
import { validatePagination } from "~/data/constatns";

export const Route = createFileRoute("/_app/plans/$id/")({
  component: RouteComponent,
  validateSearch: validatePagination,
  loaderDeps: ({ search }) => search,
  loader: async ({ params, deps: search }) => {
    try {
      const page = search.page ? Number(search.page) - 1 : 0;
      const [creatorSub] = await vtuberPlanClient.getVtuberPlanById({
        id: String(params.id),
      });
      const [subscribers] =
        await vtuberUserSubscriptionClient.getMyVtuberUserSubscriptions({
          subId: String(params.id),
          pagination: {
            page,
            size: search.size || 20,
            sort: search.sort || "created_at",
          },
        });
      return { creatorSub, subscribers };
    } catch (error) {
      console.error("Failed to load plan data:", error);
      throw error;
    }
  },
  pendingComponent: LoadingSkeleton,
  errorComponent: ({ error }) => (
    <div className="max-w-6xl mx-auto p-4 sm:p-6">
      <Card className="border-destructive/50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-destructive mb-2">
            <AlertCircle className="w-5 h-5" />
            <h2 className="text-lg font-semibold">Failed to load plan</h2>
          </div>
          <p className="text-sm text-muted-foreground mb-4">
            {error?.message || "An unexpected error occurred"}
          </p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </CardContent>
      </Card>
    </div>
  ),
});

function LoadingSkeleton() {
  return (
    <div className="max-w-7xl mx-auto p-4 sm:p-6 space-y-6 sm:space-y-8">
      {/* Header Skeleton */}
      <div className="space-y-4">
        <Skeleton className="h-8 sm:h-10 w-full max-w-md" />
        <Skeleton className="h-5 sm:h-6 w-full max-w-2xl" />
      </div>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <Skeleton
            key={i}
            className="h-32 w-full"
          />
        ))}
      </div>

      {/* Content Skeleton */}
      <div className="space-y-6">
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-80 w-full" />
      </div>
    </div>
  );
}

function RouteComponent() {
  const data = Route.useLoaderData();
  const { getText } = useLanguage();

  const { creatorSub, subscribers } = data;
  const hasSubscribers = subscribers?.vtubersubscription?.length! > 0;
  const totalSubscribers = subscribers?.paginationDetails?.totalItems || 0;

  const engagementMetrics = useMemo(() => {
    const activeCount =
      subscribers?.vtubersubscription?.filter(
        (sub) => timestampDate(sub.expiresOn!) > new Date(),
      ).length || 0;

    const retentionRate =
      totalSubscribers > 0 ? (activeCount / totalSubscribers) * 100 : 0;

    return {
      activeCount,
      retentionRate: Math.round(retentionRate),
      totalRevenue: (creatorSub?.data?.price || 0) * activeCount,
    };
  }, [
    subscribers?.vtubersubscription,
    totalSubscribers,
    creatorSub?.data?.price,
  ]);

  const columns: ColumnDef<VtuberUserSubscription>[] = [
    {
      accessorKey: "image",
      header: "",
      cell: ({ row }) => (
        <Avatar
          src={row.original.user?.image}
          className="w-10 h-10 flex-shrink-0"
          fallback={row.original.user?.name?.[0]?.toUpperCase() || "U"}
        />
      ),
      size: 60,
    },
    {
      header: getText("Name"),
      accessorKey: "name",
      cell: ({ row }) => (
        <div className="space-y-1 min-w-0">
          <p className="font-medium truncate">
            {row.original.user?.name || "Unknown User"}
          </p>
          <p className="text-sm text-muted-foreground truncate">
            {row.original.user?.email}
          </p>
        </div>
      ),
    },
    {
      header: getText("Status"),
      accessorKey: "status",
      cell: ({ row }) => {
        const isActive = timestampDate(row.original.expiresOn!) > new Date();
        return (
          <Badge
            variant={isActive ? "default" : "destructive"}
            className="flex items-center gap-1 w-fit">
            {isActive ? (
              <CheckCircle2 className="w-3 h-3" />
            ) : (
              <AlertCircle className="w-3 h-3" />
            )}
            {isActive ? getText("Active") : getText("Expired")}
          </Badge>
        );
      },
    },
    {
      header: getText("Expires_On"),
      accessorKey: "expiresOn",
      cell: ({ row }) => {
        const expirationDate = timestampDate(row.original.expiresOn!);
        const isExpiringSoon =
          expirationDate.getTime() - Date.now() < 7 * 24 * 60 * 60 * 1000;

        return (
          <div className="flex items-center gap-2">
            <CalendarClock
              className={`w-4 h-4 ${isExpiringSoon ? "text-orange-500" : "text-muted-foreground"}`}
            />
            <span
              className={`text-sm ${isExpiringSoon ? "text-orange-600 font-medium" : ""}`}>
              {expirationDate.toLocaleDateString("en-US", {
                year: "numeric",
                month: "short",
                day: "numeric",
              })}
            </span>
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <div className="mx-auto p-2 sm:p-6 space-y-6 sm:space-y-8">
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
            <div className="space-y-3 flex-1">
              <div className="flex items-center gap-2">
                <Crown className="w-6 h-6 text-primary" />
                <Badge
                  variant="secondary"
                  className="text-xs">
                  {getText("Premium_Plan")}
                </Badge>
              </div>
              <h1 className="text-2xl sm:text-3xl capitalize lg:text-4xl font-bold tracking-tight">
                {creatorSub?.data?.title}
              </h1>
              <p className="text-base sm:text-lg text-muted-foreground leading-relaxed">
                {creatorSub?.data?.shortDescription}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            <Card className="relative overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {getText("Monthly_Revenue")}
                  </CardTitle>
                  <DollarSign className="w-4 h-4 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-2xl sm:text-3xl font-bold text-green-600">
                    ${engagementMetrics.totalRevenue.toLocaleString()}
                  </p>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>${creatorSub?.data?.price}/user</span>
                    <Separator
                      orientation="vertical"
                      className="h-4"
                    />
                    <span>{getText("per_month")}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {getText("Subscribers")}
                  </CardTitle>
                  <Users className="w-4 h-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-2xl sm:text-3xl font-bold text-blue-600">
                    {engagementMetrics.activeCount.toLocaleString()}
                  </p>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>
                      {totalSubscribers.toLocaleString()} {getText("total")}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden sm:col-span-2 lg:col-span-1">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {getText("Retention_Rate")}
                  </CardTitle>
                  <TrendingUp className="w-4 h-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-2xl sm:text-3xl font-bold text-purple-600">
                    {engagementMetrics.retentionRate}%
                  </p>
                  <Progress
                    value={engagementMetrics.retentionRate}
                    className="h-2"
                  />
                  <span className="text-sm text-muted-foreground">
                    {getText("Active_retention")}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-xl sm:text-2xl">
              {getText("Plan_Details")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm sm:prose-base max-w-none dark:prose-invert">
              <MarkDown markdown={creatorSub?.data?.description} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <CardTitle className="text-xl sm:text-2xl flex items-center gap-2">
                <Users className="w-5 h-5" />
                {getText("Subscribers")}
              </CardTitle>
              <Badge
                variant="outline"
                className="px-3 py-1 w-fit">
                {totalSubscribers.toLocaleString()} {getText("total")}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            {!hasSubscribers ? (
              <div className="py-16 text-center">
                <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                  <Users className="w-12 h-12 text-muted-foreground" />
                </div>
                <div className="space-y-2 mb-6">
                  <h3 className="text-lg font-semibold">
                    {getText("No_Subscribers_Yet")}
                  </h3>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="hidden sm:block">
                  <DataTable
                    columns={columns}
                    data={subscribers?.vtubersubscription || []}
                    pagination={{
                      currentPage: subscribers?.paginationDetails?.currentPage,
                      isLast:
                        subscribers?.paginationDetails?.currentPage ===
                        subscribers?.paginationDetails?.totalPages,
                      totalItems: subscribers?.paginationDetails?.totalItems,
                      totalPage: subscribers?.paginationDetails?.totalPages,
                    }}
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default RouteComponent;
