import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";

import { Skeleton } from "@vtuber/ui/components/skeleton";
import useIntersectionObserver from "@vtuber/ui/hooks/use-intersection-observer";
import { NotificationCard } from "~/components/notification/notification-card";
import {
  NotificationProvider,
  useNotification,
} from "~/components/notification/notification-provider";

export const Route = createFileRoute("/_app/notifications")({
  component: () => (
    <NotificationProvider>
      <RouteComponent />
    </NotificationProvider>
  ),
});

function RouteComponent() {
  const { getText, language = "ja" } = useLanguage();
  const {
    notifications,
    unreadCount,
    fetchMoreNotifications,
    isRefetchingNotifications,
    loadingNotifications,
  } = useNotification();
  const { ref: loader } = useIntersectionObserver({
    rootMargin: "0px",
    onChange: (intersecting) => {
      fetchMoreNotifications(intersecting);
    },
  });

  const description = {
    en: `You have ${unreadCount} unread notifications.`,
    ja: `未読通知は ${unreadCount} 件です。`,
  };

  return (
    <div className="">
      <section className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold mb-2 capitalize">
            {getText("notifications")}
          </h1>
          <p className="text-muted-foreground">{description[language]}</p>
        </div>
      </section>

      <section className="space-y-6">
        {loadingNotifications ? (
          Array.from({ length: 10 }).map((_, i) => (
            <Skeleton
              className="w-full h-36 rounded-lg"
              key={i}
            />
          ))
        ) : notifications.length === 0 ? (
          <div className="min-h-[90dvh] flex items-center justify-center">
            {getText("no_notifications_message")}
          </div>
        ) : (
          notifications.map((n) => (
            <NotificationCard
              key={n.id}
              data={n}
            />
          ))
        )}
      </section>
      <div ref={loader} />
      {isRefetchingNotifications && (
        <div className="text-center mt-4">
          {getText("loading_notifications")}...
        </div>
      )}
    </div>
  );
}
