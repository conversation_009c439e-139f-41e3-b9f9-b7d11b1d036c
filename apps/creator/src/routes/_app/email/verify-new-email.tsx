import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, redirect, useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { authClient, handleConnectError } from "@vtuber/services/client";
import {
  AuthService,
  VerifyNewEmailWithCodeRequest,
} from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import { Card } from "@vtuber/ui/components/card";
import { Container } from "@vtuber/ui/components/container";
import { Form } from "@vtuber/ui/components/form";
import { PinInput } from "@vtuber/ui/components/form-inputs/pin-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { ArrowRight, Mail } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

export const Route = createFileRoute("/_app/email/verify-new-email")({
  component: RouteComponent,
  validateSearch: z.object({
    email: z.string().optional(),
    token: z.string().optional(),
  }),
  loaderDeps: ({ search }) => search,
  loader: async ({ deps, context }) => {
    const { token } = deps;
    if (token) {
      const [res, err] = await authClient.verifyNewEmail({
        token,
      });
      if (res) throw redirect({ to: "/email/success" });
      return {
        error: err.rawMessage,
      };
    }

    if (!context.user) {
      return {
        error: "Invalid Request",
      };
    } else {
      return {
        error: null,
      };
    }
  },
});

function RouteComponent() {
  const navigate = useNavigate();

  const { getText } = useLanguage();
  const { error } = Route.useLoaderData();
  const { email } = Route.useSearch();

  const form = useForm<VerifyNewEmailWithCodeRequest>({
    defaultValues: {
      newEmail: email,
      verificationCode: "",
    },
  });

  const { mutateAsync, isPending } = useMutation(
    AuthService.method.verifyNewEmail,
    {
      onSuccess: (data) => {
        navigate({ to: "/email/success" });
        toast.success(data.message);
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  if (error)
    return (
      <Container
        className="min-h-screen text-4xl font-medium capitalize text-font"
        variant="center">
        {error}
      </Container>
    );

  return (
    <Container
      variant="center"
      className="min-h-screen">
      <Card className="relative w-full max-w-md p-8 bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-4 rounded-xl mb-4 shadow-lg">
            <Mail className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">
            {getText("verify_new_email")}
          </h1>
          <p className="text-slate-400 text-sm">
            {getText("enter_6_digit_code")}
          </p>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(async (data) => {
              await mutateAsync({
                verifyNewEmailWithCode: data,
              });
            })}
            className="space-y-10">
            <div className="space-y-6">
              <TextInput
                required
                disabled
                label={getText("new_email")}
                labelClassName="mb-3"
                size={"lg"}
                control={form.control}
                name="newEmail"
                variant={"muted"}
                placeholder="<EMAIL>"
              />

              <PinInput
                maxLength={6}
                control={form.control}
                name="verificationCode"
                label={getText("verification_code")}
                labelClassName="mb-3"
                containerClassName="justify-between"
                className="xs:size-12 size-10"
                pattern="digitsAndChars"
              />
            </div>

            <Button
              type="submit"
              size={"xl"}
              loading={isPending}
              className="w-full h-12 bg-gradient-4 font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl group">
              <div className="flex items-center space-x-2">
                <span className="capitalize">{getText("update_email")}</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </div>
            </Button>
          </form>
        </Form>

        <div className="mt-8 pt-6 border-t border-slate-700/50">
          <p className="text-xs text-slate-500 text-center">
            {getText("verification_code_has_been_sent_to_your_new_email")}
          </p>
        </div>
      </Card>
    </Container>
  );
}
