import { createFileRoute, useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { CheckCircle, Home, Mail, Settings } from "lucide-react";

export const Route = createFileRoute("/_app/email/success")({
  component: RouteComponent,
});

function RouteComponent() {
  const { getText } = useLanguage();
  const router = useRouter();

  return (
    <div className="min-h-screen  flex items-center justify-center p-4 relative overflow-hidden">
      <Card
        className={
          "w-full max-w-md bg-white/10 backdrop-blur-lg overflow-hidden border-white/20 shadow-2xl animate-fade-in"
        }>
        <CardContent className="p-8 text-center space-y-6">
          <div className="relative mx-auto">
            <div
              className={
                "w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center transition-all duration-500 transform animate-slide-in-left"
              }>
              <CheckCircle className="w-10 h-10 text-white" />
            </div>
          </div>

          <div className="space-y-2">
            <h1
              className={
                "text-2xl font-bold text-white transition-all duration-500 delay-200"
              }>
              {getText("email_update_successfully")}
            </h1>
            <p
              className={"text-gray-300 transition-all duration-500 delay-300"}>
              {getText("email_verified_and_changed")}
            </p>
          </div>

          <div
            className={
              "flex justify-center transition-all duration-500 delay-400"
            }>
            <div className="relative">
              <Mail className="w-8 h-8 text-blue-400 animate-bounce" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-ping" />
            </div>
          </div>

          <div className={"space-y-3 transition-all duration-500 delay-500"}>
            <Button
              size={"xl"}
              onClick={() => {
                router.navigate({ to: "/" });
              }}
              className="w-full bg-gradient-gold-linear animate-slide-in-up duration-500 font-semibold py-3 rounded-lg">
              <Home className="w-4 h-4 mr-2" />
              {getText("back_to_top")}
            </Button>

            <Button
              onClick={() => {
                router.navigate({ to: "/profile" });
              }}
              variant="outline"
              size={"xl"}
              className="w-full bg-white/10 animate-slide-in-up duration-500 delay-150 border-white/30 text-white hover:bg-white/20 hover:border-white/40 transition-all ">
              <Settings className="w-4 h-4 mr-2" />
              {getText("account_settings")}
            </Button>
          </div>

          <div
            className={
              "pt-4 border-t border-white/20 transition-all duration-500 delay-600"
            }>
            <p className="text-xs text-gray-400">
              {getText("didnt_make_this_change")}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
