import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService, SendVerifyNewEmailRequest } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import { Card } from "@vtuber/ui/components/card";
import { Container } from "@vtuber/ui/components/container";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { ArrowRight, Mail } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

export const Route = createFileRoute("/_app/email/new-email")({
  component: RouteComponent,
  validateSearch: z.object({
    token: z.string(),
  }),
});

function RouteComponent() {
  const { token } = Route.useSearch();
  const navigate = useNavigate();

  const { getText } = useLanguage();
  const form = useForm<SendVerifyNewEmailRequest>({
    defaultValues: {
      newEmail: "",
      token,
    },
  });

  const { mutateAsync, isPending } = useMutation(
    AuthService.method.sendVerifyNewEmail,
    {
      onSuccess: (data, { newEmail }) => {
        navigate({
          to: "/email/verify-new-email",
          search: {
            email: newEmail,
          },
        });
        toast.success(data.message);
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );
  return (
    <Container
      variant="center"
      className="min-h-[90dvh]">
      <Card className="relative w-full max-w-md p-8 bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-4 rounded-xl mb-4 shadow-lg">
            <Mail className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">
            {getText("change_email")}
          </h1>
          <p className="text-slate-400 text-sm">
            {getText("update_email_for_your_account")}
          </p>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(async (data) => {
              await mutateAsync(data);
            })}
            className="space-y-6">
            <TextInput
              required
              label={getText("email_address")}
              labelClassName="mb-3"
              size={"lg"}
              control={form.control}
              name="newEmail"
              variant={"muted"}
              placeholder="<EMAIL>"
            />

            <Button
              type="submit"
              size={"xl"}
              loading={isPending}
              className="w-full h-12 bg-gradient-4 font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl group">
              <div className="flex items-center space-x-2">
                <span className="capitalize">{getText("update_email")}</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </div>
            </Button>
          </form>
        </Form>

        <div className="mt-8 pt-6 border-t border-slate-700/50">
          <p className="text-xs text-slate-500 text-center">
            {getText("verification_code_will_be_sent_to_new_email")}
          </p>
        </div>
      </Card>
    </Container>
  );
}
