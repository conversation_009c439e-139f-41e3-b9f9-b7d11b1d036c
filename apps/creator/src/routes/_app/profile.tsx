import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { vtuberCategoryClient } from "@vtuber/services/client";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { useState } from "react";
import { VtuberGallery } from "~/components/gallery/vtuber-gallery";
import { ProfileCard } from "~/components/settings/profile-card";
import { ProfileForm } from "~/components/settings/profile-form";
import { VtuberBanners } from "~/components/settings/vtuber-banners";
import { validatePagination } from "~/data/constatns";

export const Route = createFileRoute("/_app/profile")({
  component: RouteComponent,
  validateSearch: validatePagination,
  loader: async () => {
    const [res] = await vtuberCategoryClient.getAllVtuberCategories({});
    return res?.categories;
  },
});

function RouteComponent() {
  const categories = Route.useLoaderData();
  const { getText } = useLanguage();
  const [tabValue, setTabValue] = useState("profile");

  return (
    <div className="py-10">
      <section className="fixed inset-0 bg-gradient-to-br from-violet-900/20 via-black to-cyan-900/20">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
      </section>
      <section className="relative z-20">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent pb-3">
              Profile Settings
            </h1>
            <p className="text-gray-400">Customize your digital presence</p>
          </div>
        </div>
      </section>
      <section className="grid lg:grid-cols-12 gap-8 relative z-20 pt-12">
        <div className="lg:col-span-8 space-y-8 lg:order-1 order-2">
          <Tabs
            value={tabValue}
            onValueChange={setTabValue}>
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger
                value="profile"
                className="capitalize">
                {getText("my_profile")}
              </TabsTrigger>
              <TabsTrigger value="banners">{getText("Banners")}</TabsTrigger>
              <TabsTrigger value="gallery">{getText("gallery")}</TabsTrigger>
            </TabsList>
            <TabsContent value="profile">
              <div className="pt-10">
                <ProfileForm categories={categories} />
              </div>
            </TabsContent>
            <TabsContent value="banners">
              <div className="pt-10">
                <VtuberBanners tabValue={tabValue} />
              </div>
            </TabsContent>
            <TabsContent value="gallery">
              <div className="pt-10">
                <VtuberGallery tabValue={tabValue} />
              </div>
            </TabsContent>
          </Tabs>
        </div>
        <div className="lg:col-span-4 lg:order-2 order-1">
          <ProfileCard className=" sticky top-[75px]" />
        </div>
      </section>
    </div>
  );
}
