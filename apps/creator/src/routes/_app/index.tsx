import { createFileRoute } from "@tanstack/react-router";
import { useSession } from "@vtuber/auth/hooks";
import { Badge } from "@vtuber/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { DiscordIcon } from "@vtuber/ui/components/icons/discord-icon";
import { InstagramIcon } from "@vtuber/ui/components/icons/instagram-icon";
import { TikTokIcon } from "@vtuber/ui/components/icons/tittok-icon";
import { TwitchIcon } from "@vtuber/ui/components/icons/twitch-icon";
import { XIcon } from "@vtuber/ui/components/icons/x-icon";
import { YoutubeIcon } from "@vtuber/ui/components/icons/youtube-icon";

export const Route = createFileRoute("/_app/")({
  component: VTuberProfilePage,
});

function VTuberProfilePage() {
  const user = useSession();
  const vtuberProfile = user?.vtuber;

  if (!vtuberProfile) {
    return (
      <div className="flex h-full items-center justify-center p-6">
        <Card className="w-full max-w-md text-center p-8">
          <CardTitle className="mb-4">No VTuber Profile Found</CardTitle>
          <CardDescription>
            You don't have a VTuber profile set up yet.
          </CardDescription>
        </Card>
      </div>
    );
  }

  const socialLinks = [
    {
      url: vtuberProfile.socialMediaLinks?.twitter,
      icon: <XIcon />,
    },
    {
      url: vtuberProfile.socialMediaLinks?.youtube,
      icon: <YoutubeIcon className="[&>path]:fill-white size-6" />,
    },
    {
      url: vtuberProfile.socialMediaLinks?.instagram,
      icon: <InstagramIcon />,
    },
    {
      url: vtuberProfile.socialMediaLinks?.tiktok,
      icon: <TikTokIcon />,
    },
    {
      url: vtuberProfile.socialMediaLinks?.twitch,
      icon: <TwitchIcon />,
    },
    {
      url: vtuberProfile.socialMediaLinks?.discord,
      icon: <DiscordIcon />,
    },
  ];

  return (
    <Card className="overflow-hidden shadow-lg">
      <div className="relative">
        {vtuberProfile.bannerImage ? (
          <img
            src={vtuberProfile.bannerImage}
            alt="Banner"
            className="w-full h-64 md:h-80 object-cover"
          />
        ) : (
          <div className="w-full h-64 md:h-80" />
        )}

        {vtuberProfile.isUserSubscribed && (
          <Badge className="absolute top-4 right-4   font-semibold px-3 py-1">
            Subscribed
          </Badge>
        )}
      </div>

      <div className="relative px-6 md:px-8">
        <div className="absolute -top-16 left-1/2 transform -translate-x-1/2">
          {vtuberProfile.image ? (
            <img
              src={vtuberProfile.image}
              alt={vtuberProfile.displayName}
              className="w-32 h-32 rounded-full border-4 border-white shadow-lg object-cover"
            />
          ) : (
            <div className="w-32 h-32 rounded-full border-4 border-white  flex items-center justify-center  text-2xl font-bold">
              {vtuberProfile.displayName.charAt(0)}
            </div>
          )}
        </div>

        <CardHeader className="text-center pt-20">
          <CardTitle className="text-3xl font-bold">
            {vtuberProfile.displayName}
          </CardTitle>
          <CardDescription className="text-lg mt-1">
            {vtuberProfile.furigana}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-8 pb-8">
          {vtuberProfile.description && (
            <div className="rounded-lg p-4 md:p-6">
              <p className="leading-relaxed text-center md:text-left">
                {vtuberProfile.description}
              </p>
            </div>
          )}

          <div className="flex flex-wrap justify-center gap-3">
            {socialLinks.map((link, index) =>
              link.url ? (
                <a
                  target="_blank"
                  key={index}
                  href={link.url}
                  className="size-12 flex items-center justify-center border border-white rounded-full">
                  {link.icon}
                </a>
              ) : null,
            )}
          </div>
        </CardContent>
      </div>
    </Card>
  );
}
