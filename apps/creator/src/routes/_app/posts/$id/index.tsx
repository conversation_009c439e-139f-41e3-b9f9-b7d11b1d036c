import { timestampDate } from "@bufbuild/protobuf/wkt";
import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { postsClient } from "@vtuber/services/client";
import { Badge } from "@vtuber/ui/components/badge";
import { Card } from "@vtuber/ui/components/card";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Media } from "@vtuber/ui/components/media";
import { Calendar, Heart, MessageSquare } from "lucide-react";
import { z } from "zod";
import { PostTab } from "~/components/post/PostTab";

export const Route = createFileRoute("/_app/posts/$id/")({
  component: RouteComponent,
  validateSearch: z.object({
    tab: z.string().optional(),
  }),
  loader: async ({ params }) => {
    const [post] = await postsClient.getPostById({
      id: params.id,
    });
    return post;
  },
});

function RouteComponent() {
  const post = Route.useLoaderData();
  const { getText } = useLanguage();

  if (!post?.data) {
    return (
      <div className="flex items-center justify-center">
        <p className="text-center text-lg text-muted-foreground">
          Post not found
        </p>
      </div>
    );
  }

  const postDate = post.data?.createdAt
    ? timestampDate(post.data.createdAt)
    : null;

  return (
    <div className="animate-fade-in pb-12">
      <div className="relative aspect-video rounded-xl overflow-hidden shadow-lg mb-8">
        <Media
          type={post.data?.mediaType}
          src={post.data?.media}
          alt={post.data?.title}
          className="w-full h-full object-cover"
        />

        <div className="absolute inset-0  to-transparent" />

        <div className="absolute bottom-4 left-4 text-white z-10">
          <h1 className="text-2xl sm:text-3xl font-bold drop-shadow-lg">
            {post.data.name}
          </h1>
        </div>
      </div>

      <header className="px-4 sm:px-6 mb-8">
        <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <div className="space-y-2">
            <h1 className="text-3xl sm:text-4xl font-bold tracking-tight">
              {post.data.title}
            </h1>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant="success"
              className="p-2">
              <MessageSquare className="w-4 h-4 mr-2" />
              {post.data.postComments}
            </Badge>
            <Badge className="p-2 bg-red-500">
              <Heart className="w-4 h-4 mr-2" />
              {post.data.postLikes}
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-4 text-muted-foreground">
          {postDate && (
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              <span className="text-sm">
                {postDate.toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </span>
            </div>
          )}
        </div>
      </header>
      <Card className="mx-4 sm:mx-6 mb-4">
        <div className="p-6 prose prose-lg max-w-none dark:prose-invert">
          {post.data.shortDescription}
        </div>
      </Card>

      <Card className="mx-4 sm:mx-6">
        <div className="p-6 prose prose-lg max-w-none dark:prose-invert">
          <MarkDown markdown={post.data.description} />
        </div>
      </Card>

      <div className="mt-12 px-4 sm:px-6">
        <PostTab id={post.data.id} />
      </div>
    </div>
  );
}
