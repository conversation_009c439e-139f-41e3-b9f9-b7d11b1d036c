import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { postsClient } from "@vtuber/services/client";
import { PostForm } from "~/components/post/PostForm";

export const Route = createFileRoute("/_app/posts/$id/edit")({
  component: RouteComponent,
  loader: async ({ params }) => {
    const [post] = await postsClient.getPostById({
      id: params.id,
    });
    return post;
  },
});

function RouteComponent() {
  const post = Route.useLoaderData();
  const { getText } = useLanguage();

  if (!post?.data) return <div>No post found or data is loading...</div>;

  return <PostForm defaultValues={post.data} />;
}
