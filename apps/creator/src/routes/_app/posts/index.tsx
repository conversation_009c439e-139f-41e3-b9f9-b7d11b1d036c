import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { postsClient } from "@vtuber/services/client";
import { buttonVariants } from "@vtuber/ui/components/button";
import { Paginator } from "@vtuber/ui/components/paginator";
import { Plus } from "lucide-react";
import { NoPostMessage } from "~/components/post/no-post-message";
import { PostCard } from "~/components/post/PostCard";
import { validatePagination } from "~/data/constatns";

export const Route = createFileRoute("/_app/posts/")({
  component: RouteComponent,
  validateSearch: validatePagination,
  loader: async ({ deps: search }) => {
    const [posts] = await postsClient.getMyPosts({
      pagination: {
        ...search,
        sort: "created_at",
        size: 9,
        order: "DESC",
      },
    });
    return posts;
  },
  loaderDeps: ({ search }) => search,
});

function RouteComponent() {
  const postData = Route.useLoaderData();
  const { getText } = useLanguage();

  if (!postData?.data || postData.data.length === 0) return <NoPostMessage />;

  return (
    <div className="container pt-10">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold">{getText("Posts")}</h1>
          <p className="text-slate-400 mt-1">
            Manage your content and publications
          </p>
        </div>
        <Link
          to="/posts/add"
          className={buttonVariants({
            variant: "success",
            size: "xl",
            className: "!rounded-lg",
          })}>
          <Plus className="w-4 h-4 mr-2" />
          Add Post
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {postData?.data.map((post) => (
          <PostCard
            key={post.id.toString()}
            post={post}
          />
        ))}
      </div>
      <div className="pt-8">
        <Paginator
          currentPage={postData?.paginationDetails?.currentPage}
          totalPages={postData?.paginationDetails?.totalPages}
        />
      </div>
    </div>
  );
}
