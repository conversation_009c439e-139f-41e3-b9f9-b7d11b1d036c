import { createFileRoute } from "@tanstack/react-router";
import { Container } from "@vtuber/ui/components/container";
import { z } from "zod";
import { Header } from "~/components/Layout/Header";

export const Route = createFileRoute("/search")({
  component: RouteComponent,
  validateSearch: z.object({
    query: z.string().optional(),
  }),
});

function RouteComponent() {
  const search = Route.useSearch();
  return (
    <div>
      <Header />
      <Container className="h-80 flex items-center text-2xl font-semibold justify-center">
        You searched for {search?.query}
      </Container>
    </div>
  );
}
