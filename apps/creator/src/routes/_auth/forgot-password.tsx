import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, <PERSON>, useRouter } from "@tanstack/react-router";
import { handleConnectError } from "@vtuber/services/client";
import {
  AuthService,
  SendForgotPasswordEmailRequest,
} from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/form/text-input";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
export const Route = createFileRoute("/_auth/forgot-password")({
  component: RouteComponent,
  validateSearch: z.object({
    email: z.string().optional(),
  }),
});
function RouteComponent() {
  const { navigate } = useRouter();
  const { email: currentEmail } = Route.useSearch();

  const form = useForm<SendForgotPasswordEmailRequest>({
    defaultValues: {
      email: currentEmail,
    },
  });

  const forgotPassword = useMutation(
    AuthService.method.sendForgotPasswordEmail,
    {
      onSuccess: (data) => {
        toast.success(data.message);
        navigate({ to: "/reset-password" });
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const onSubmit = form.handleSubmit((data) => forgotPassword.mutate(data));
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-xl">Forgot Password</CardTitle>
        <CardDescription>
          Enter your email to reset your password
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={onSubmit}>
            <div className="grid gap-6">
              <div className="grid gap-6">
                <TextInput
                  control={form.control}
                  name="email"
                  label="Email"
                  placeholder="Enter your email"
                />
                <Button
                  loading={forgotPassword.isPending}
                  type="submit"
                  className="w-full">
                  Send Password Reset Email
                </Button>
              </div>
              <div className="text-center text-sm">
                Already received a code?{" "}
                <Link
                  to="/reset-password"
                  search={(prev) => ({
                    ...prev,
                    email: form.watch("email"),
                  })}
                  className="underline underline-offset-4">
                  Reset
                </Link>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
