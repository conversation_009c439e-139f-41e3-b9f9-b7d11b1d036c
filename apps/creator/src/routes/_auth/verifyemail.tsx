import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { Spinner } from "@vtuber/ui/components/spinner";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

export const Route = createFileRoute("/_auth/verifyemail")({
  component: RouteComponent,
  validateSearch: z.object({
    email: z.string().email(),
  }),
});

function RouteComponent() {
  const { email } = Route.useSearch();
  const { getText } = useLanguage();
  const router = useRouter();
  const form = useForm({
    defaultValues: {
      verificationCode: "",
      email,
      code: "",
    },
  });

  const {
    mutateAsync: resendVerificationCode,
    isPending: resendingVerificationCode,
  } = useMutation(AuthService.method.sendSignupEmailVerificationCode, {
    onError: (err) => {
      toast.error(err.rawMessage);
    },
  });

  const { mutate, isPending } = useMutation(
    AuthService.method.verifySignupEmail,
    {
      onSuccess: (data) => {
        router.navigate({
          to: "/welcome",
          search: {
            token: data.token,
            step: "welcome",
          },
        });
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const onSubmit = form.handleSubmit((data) => {
    mutate(data);
  });

  const isLoading = isPending || resendingVerificationCode;

  return (
    <div className="p-6 max-w-[350px] flex flex-col gap-y-[30px]">
      <section className="flex flex-col gap-y-5 text-font">
        <h3 className="text-[26px] font-bold">
          {getText("verification_page_title")}
        </h3>
        <p>{getText("verification_page_subtitle")}</p>
      </section>
      <Form {...form}>
        <form
          onSubmit={onSubmit}
          className="flex flex-col gap-y-[30px]">
          <TextInput
            size={"lg"}
            wrapperClassName="space-y-3"
            labelClassName="font-medium text-sm"
            control={form.control}
            name="verificationCode"
            label={getText("verify_page_title")}
            variant={"filled"}
            placeholder={getText("enter_code")}
          />
          <section className="flex flex-col gap-y-[30px]">
            <button
              disabled={isLoading}
              type="button"
              onClick={() => {
                resendVerificationCode({
                  email,
                });
              }}
              className="block text-center text-sm text-font relative">
              {resendingVerificationCode && (
                <Spinner className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
              )}
              {getText("resend_verification_code")}
            </button>
            <Button
              disabled={isLoading}
              loading={isPending}
              variant={"tertiary"}
              type="submit"
              className="font-bold w-full h-[45px] capitalize text-white">
              {getText("send")}
            </Button>
          </section>
        </form>
      </Form>
    </div>
  );
}
