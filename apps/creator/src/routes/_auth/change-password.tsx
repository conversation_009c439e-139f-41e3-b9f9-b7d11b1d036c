import { useMutation } from "@tanstack/react-query";
import { createFileRoute, useRouter } from "@tanstack/react-router";
import {
  authClient,
  handleConnectError,
  NoAuthContextValues,
} from "@vtuber/services/client";
import { ChangePasswordRequest } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/form/text-input";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
const authSearchSchema = z.object({
  token: z.string().optional(),
  email: z.string().email().optional(),
});

export const Route = createFileRoute("/_auth/change-password")({
  validateSearch: authSearchSchema,
  component: RouteComponent,
});

function RouteComponent() {
  const { navigate } = useRouter();

  const form = useForm<ChangePasswordRequest>({
    defaultValues: {
      oldPassword: "",
      newPassword: "",
    },
  });

  const resetPassword = useMutation({
    mutationFn: async (data: ChangePasswordRequest) => {
      return authClient.changePassword(data, {
        contextValues: NoAuthContextValues(),
      });
    },

    onSuccess: ([res, err]) => {
      if (res) {
        toast.success("Change password successfully. Login to continue");
        navigate({ to: "/login" });
      } else if (err) {
        handleConnectError(err, form);
      }
    },
  });

  const onSubmit = form.handleSubmit((data) => resetPassword.mutate(data));
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-xl">Change Password</CardTitle>
        <CardDescription>Fill the form to reset your account</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={onSubmit}>
            <div className="grid gap-6">
              <div className="grid gap-6">
                <TextInput
                  control={form.control}
                  name="oldPassword"
                  label="Old Password"
                  placeholder="Enter your old password"
                />
                <TextInput
                  control={form.control}
                  name="newPassword"
                  label="New Password"
                  placeholder="Enter new password"
                  type="password"
                />
                <Button
                  loading={resetPassword.isPending}
                  type="submit"
                  className="w-full">
                  Change Password
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
