import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { z } from "zod";
import { AuthenticationSuccessMessage } from "~/components/Auth/AuthenticatonSuccessMessage";
import { RegisterForm } from "~/components/Auth/RegisterForm";
import { RegistrationSuccessMessage } from "~/components/Auth/RegistrationSuccessMessage";

export const Route = createFileRoute("/_auth/welcome")({
  component: RouteComponent,
  validateSearch: z.object({
    step: z.enum(["welcome", "register", "completed"]).optional(),
    token: z.string().optional(),
  }),
});

function RouteComponent() {
  const navigate = useNavigate({ from: Route.fullPath });
  const { step, token } = Route.useSearch();

  if (step === "welcome")
    return (
      <AuthenticationSuccessMessage
        onNext={() => {
          navigate({
            search: (prev) => ({
              ...prev,
              step: "register",
            }),
          });
        }}
      />
    );

  if (step === "register")
    return (
      <RegisterForm
        token={token!}
        onNext={() => {
          navigate({
            search: {
              step: "completed",
            },
          });
        }}
      />
    );

  return <RegistrationSuccessMessage />;
}
