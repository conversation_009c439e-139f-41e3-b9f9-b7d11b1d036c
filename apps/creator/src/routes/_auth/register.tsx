import { useMutation } from "@connectrpc/connect-query";
import { createFile<PERSON>out<PERSON>, <PERSON>, useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { DictionaryKey } from "@vtuber/language/types";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import { Form } from "@vtuber/ui/components/form";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { AppleIcon } from "@vtuber/ui/components/icons/apple-icon";
import { FacebookIcon } from "@vtuber/ui/components/icons/facebook-icon";
import { GoogleIcon } from "@vtuber/ui/components/icons/google-icon";
import { InfoCircleIcon } from "@vtuber/ui/components/icons/info-circle-icon";
import { SupportIcon } from "@vtuber/ui/components/icons/support-icon";
import { TextDivider } from "@vtuber/ui/components/text-divider";
import { useForm } from "react-hook-form";

const list: {
  icon: React.ReactNode;
  title: DictionaryKey;
  description: DictionaryKey;
}[] = [
  {
    icon: <SupportIcon />,
    title: "you_can_support_vtuber",
    description: "register_how_to_support_vtuber",
  },
  {
    icon: <InfoCircleIcon />,
    title: "check_info_about_fav_idol",
    description: "register_how_to_check_your_idol_info",
  },
  {
    icon: <SupportIcon />,
    title: "you_can_participate_in_events",
    description: "register_how_to_participate_in_special_events",
  },
];

export const Route = createFileRoute("/_auth/register")({
  component: RouteComponent,
});

function RouteComponent() {
  const { getText } = useLanguage();
  const router = useRouter();
  const form = useForm({
    defaultValues: {
      email: "",
    },
  });

  const { mutateAsync, isPending } = useMutation(
    AuthService.method.sendSignupEmailVerificationCode,
    {
      onSuccess: (_, { email }) => {
        router.navigate({
          to: "/verifyemail",
          search: {
            email: email!,
          },
        });
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );
  const onSubmit = form.handleSubmit(async (data) => {
    await mutateAsync({
      email: data.email,
    });
  });

  return (
    <div className="py-8 pr-10">
      <div className="grid grid-cols-12 items-start">
        <section className="md:col-span-5 col-span-12 sm:px-[50px]">
          <div className="flex flex-col gap-y-[62px]">
            <div className="flex flex-col gap-y-5 text-center justify-center text-font">
              <h3 className="font-bold text-[26px]">
                {getText("register_page_title")}
              </h3>
              <h6>{getText("register_page_subtitle")}</h6>
            </div>
            <Form {...form}>
              <form
                className="flex flex-col gap-y-[30px]"
                onSubmit={onSubmit}>
                <TextInput
                  size={"lg"}
                  wrapperClassName="space-y-3"
                  labelClassName="font-medium text-sm"
                  control={form.control}
                  name="email"
                  label={getText("email_address")}
                  variant={"filled"}
                  placeholder="<EMAIL>"
                  type="email"
                  inputMode="email"
                />
                <Button
                  loading={isPending}
                  type="submit"
                  variant={"tertiary"}
                  className="w-full h-[45px] rounded-full text-white">
                  {getText("register")}
                </Button>
              </form>
            </Form>

            <div className="flex flex-col gap-y-[22px]">
              <TextDivider>{getText("or")}</TextDivider>
              <p className="text-font text-sm text-center font-medium">
                {getText("register_with_other_services")}
              </p>
              <div className="flex flex-col items-center gap-y-2">
                <Button
                  variant={"outline-dark"}
                  className="h-[43px] sm:max-w-[258px] w-full justify-between py-[9px] pr-10 block text-sm font-medium">
                  <div className="flex items-center justify-between w-full">
                    <GoogleIcon className="!size-5 mr-6" />
                    {getText("login_with_google")}
                  </div>
                </Button>
                <Button
                  variant={"outline-dark"}
                  className="h-[43px] sm:max-w-[258px] w-full py-[9px] pr-10 block text-sm font-medium">
                  <div className="flex items-center justify-between w-full">
                    <AppleIcon className="!h-[23px] !w-[19px] mr-6" />
                    {getText("login_with_apple")}
                  </div>
                </Button>
                <Button
                  variant={"outline-dark"}
                  className="h-[43px] sm:max-w-[258px] w-full justify-between py-[9px] pr-10 block text-sm font-medium">
                  <div className="flex items-center justify-between w-full">
                    <FacebookIcon className="!size-6 mr-6" />
                    {getText("login_with_facebook")}
                  </div>
                </Button>
              </div>
              <p className="text-font text-sm">
                {getText("register_terms_label")}
              </p>
              <Link
                to="/login"
                className="text-font underline text-sm text-center">
                {getText("already_have_an_account")}
              </Link>
            </div>
          </div>
        </section>
        <section className="md:col-span-7 col-span-12 sm:pl-16 pt-16">
          <h3 className="text-xl font-bold">
            {getText("connecting_with_vtubers")}
          </h3>
          <div className="flex flex-col gap-y-12 pt-12">
            {list.map((item, i) => (
              <div
                key={i}
                className="flex sm:items-start items-center sm:flex-row flex-col sm:gap-x-[18px] sm:gap-y-0 gap-y-3">
                <div className="size-[100px] flex items-center justify-center rounded-[8px] bg-gradient-2 p-[1px]">
                  <div
                    className="rounded-[8px] size-full flex items-center justify-center"
                    style={{
                      background:
                        "linear-gradient(-35deg, #2F4355 5%, #322A4D 100%)",
                    }}>
                    {item.icon}
                  </div>
                </div>
                <div className="text-font space-y-3 flex-1 sm:text-left text-center">
                  <h6 className="font-bold text-lg">{getText(item.title)}</h6>
                  <p className="text-sm font-medium">
                    {getText(item.description)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}
