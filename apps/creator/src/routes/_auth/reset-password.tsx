import { useMutation } from "@connectrpc/connect-query";
import { createFileRoute, <PERSON>, useRouter } from "@tanstack/react-router";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService, ResetPasswordRequest } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { PinInput } from "@vtuber/ui/form/pin-input";
import { TextInput } from "@vtuber/ui/form/text-input";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

const authSearchSchema = z.object({
  email: z.string().email().optional(),
});

export const Route = createFileRoute("/_auth/reset-password")({
  validateSearch: authSearchSchema,
  component: RouteComponent,
});

function RouteComponent() {
  const { navigate } = useRouter();
  const { email: defaultEmail } = Route.useSearch();

  const form = useForm<ResetPasswordRequest>({
    defaultValues: {
      email: defaultEmail,
      newPassword: "",
      verificationCode: "",
    },
  });

  const email = form.watch("email");

  const forgotPassword = useMutation(AuthService.method.resetPassword, {
    onSuccess: (data) => {
      toast.success(data.message);
      navigate({
        to: "/login",
        search: {
          email,
        },
      });
    },
    onError: (err) => {
      handleConnectError(err, form);
    },
  });

  const onSubmit = form.handleSubmit((data) => forgotPassword.mutate(data));
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-xl">Reset Password</CardTitle>
        <CardDescription>Fill the form to reset your account</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={onSubmit}>
            <div className="grid gap-6">
              <div className="grid gap-6">
                <TextInput
                  control={form.control}
                  name="email"
                  label="Email"
                  placeholder="Enter your email"
                />
                <TextInput
                  control={form.control}
                  name="newPassword"
                  label="New Password"
                  placeholder="Enter new password"
                  type="password"
                />
                <PinInput
                  control={form.control}
                  name="verificationCode"
                  label="Token"
                  placeholder="Enter your token"
                  maxLength={6}
                  required
                  pattern="digitsAndChars"
                />
                <Button
                  loading={forgotPassword.isPending}
                  type="submit"
                  className="w-full">
                  Reset
                </Button>
              </div>
              <div className="text-center text-sm">
                Didn&apos;t receive a code?{" "}
                <Link
                  to="/forgot-password"
                  className="underline underline-offset-4">
                  Resend
                </Link>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
