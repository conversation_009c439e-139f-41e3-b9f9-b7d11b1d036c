import { queryOptions } from "@tanstack/react-query";
import {
  campaignBannerClient,
  campaignVariantClient,
} from "@vtuber/services/client";

type queryOptionsType = {
  enabled?: boolean;
};

export const campaignBannerQueryOptions = (
  campaignId: string,
  { enabled = true }: queryOptionsType,
) => {
  const options = queryOptions({
    queryKey: ["campaign_banner", campaignId],
    queryFn: async () => {
      const [data] = await campaignBannerClient.getBannerByCampaignId({
        campaignId,
      });
      return data;
    },
    enabled,
  });
  return options;
};

export const campaignVariantQueryOptions = (
  campaignId: string,
  { enabled }: queryOptionsType,
) => {
  const options = queryOptions({
    queryKey: ["campaign_variant", campaignId],
    queryFn: async () => {
      const [data] = await campaignVariantClient.getAllCampaignVariants({
        campaignId,
      });
      return data;
    },
    enabled,
  });
  return options;
};
