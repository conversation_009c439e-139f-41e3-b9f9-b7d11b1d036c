import { useMutation } from "@connectrpc/connect-query";
import { DragEndEvent } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import {
  CampaignBanner,
  CampaignBannerService,
  GetCampaignById,
} from "@vtuber/services/campaigns";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

interface Props {
  campaignId: string;
  campaignBanners: CampaignBanner[];
  campaign?: GetCampaignById;
}

export const useCampaignReorder = ({
  campaignId,
  campaignBanners,
  campaign,
}: Props) => {
  const [editMode, setEditMode] = useState(false);
  const [banners, setBanners] = useState(campaignBanners);

  useEffect(() => {
    setBanners(campaignBanners || []);
  }, [campaignBanners, campaignId]);

  const sortedBanners = banners.sort((a, b) => a.index - b.index);

  const originalBannersRef = useRef<CampaignBanner[]>([]);

  const updateBannerMutation = useMutation(
    CampaignBannerService.method.updateCampaignBannerById,
  );

  const onDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!active || !over || active.id === over.id || banners.length === 1)
      return;

    setBanners((prevBanners) => {
      const currentSorted = prevBanners.sort((a, b) => a.index - b.index);
      const oldIndex = currentSorted.findIndex(
        (b) => String(b.id) === active.id,
      );
      const newIndex = currentSorted.findIndex((b) => String(b.id) === over.id);

      if (oldIndex === -1 || newIndex === -1) return prevBanners;

      const reorderedBanners = arrayMove(currentSorted, oldIndex, newIndex);

      const updatedBanners = reorderedBanners.map((banner, index) => ({
        ...banner,
        index: index + 1,
      }));

      return prevBanners.map((banner) => {
        const updated = updatedBanners.find((ub) => ub.id === banner.id);
        return updated || banner;
      });
    });
  };

  const updateBanners = async () => {
    if (!campaign) return;

    try {
      const updateResults = await Promise.allSettled(
        sortedBanners.map((banner) =>
          updateBannerMutation.mutateAsync({
            id: banner.id,
            image: banner.image,
            index: banner.index,
          }),
        ),
      );

      const successCount = updateResults.filter(
        (result) => result.status === "fulfilled",
      ).length;

      if (successCount === sortedBanners.length) {
        toast.success("Banner order updated successfully");
      } else if (successCount > 0) {
        toast.warning(
          `Updated ${successCount} of ${sortedBanners.length} banners`,
        );
      } else {
        toast.error("Failed to update banner order");
      }

      originalBannersRef.current = [];
      setEditMode(false);
    } catch (error) {
      toast.error("Failed to update banner order");
    }
  };

  const handleCancel = () => {
    if (originalBannersRef.current.length > 0) {
      setBanners([...originalBannersRef.current]);
    }
    setEditMode(false);
    originalBannersRef.current = [];
  };

  const handleEditModeToggle = () => {
    if (!editMode) {
      originalBannersRef.current = [...banners];
      setEditMode(true);
    } else {
      updateBanners();
    }
  };

  return {
    banners,
    sortedBanners,
    editMode,
    onDragEnd,
    handleCancel,
    handleEditModeToggle,
    isUpdating: updateBannerMutation.isPending,
  };
};
