import { useMutation } from "@connectrpc/connect-query";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { AuthService } from "@vtuber/services/users";
import { Button } from "@vtuber/ui/components/button";
import { Checkbox } from "@vtuber/ui/components/checkbox";
import { Form } from "@vtuber/ui/components/form";
import { DOBInput } from "@vtuber/ui/components/form-inputs/date-of-birth-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { Label } from "@vtuber/ui/components/label";
import { checkDob, convertToTimestamp } from "@vtuber/ui/lib/utils";
import { useState } from "react";
import { useForm } from "react-hook-form";

export const RegisterForm = ({
  onNext,
  token,
}: {
  onNext: () => void;
  token: string;
}) => {
  const [agreed, setAgreed] = useState(false);
  const { getText } = useLanguage();
  const form = useForm({
    defaultValues: {
      password: "",
      cofirmPassword: "",
      fullName: "",
      dateOfBirth: "",
    },
  });

  const { mutateAsync, isPending } = useMutation(
    AuthService.method.signupWithEmail,
    {
      onSuccess: () => {
        onNext();
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );
  const onSubmit = form.handleSubmit((data) => {
    if (data.password !== data.cofirmPassword) {
      form.setError("cofirmPassword", {
        message: getText("confirm_password_error"),
      });
      return;
    }

    if (data.dateOfBirth) {
      const inValidDob = checkDob(data.dateOfBirth);
      if (inValidDob) {
        form.setError("dateOfBirth", {
          message: getText("invalid_dob"),
        });
        return;
      }
    }

    mutateAsync({
      dateOfBirth: data.dateOfBirth
        ? convertToTimestamp(data.dateOfBirth)
        : undefined,
      fullName: data.fullName,
      password: data.password,
      token,
    });
  });
  return (
    <div className="max-w-[350px]">
      <section className="flex flex-col gap-y-[20px] text-font text-center">
        <h3 className="font-bold text-[26px]">
          {getText("register_form_title")}
        </h3>
        <p>{getText("register_form_subtitle")}</p>
      </section>

      <Form {...form}>
        <form onSubmit={onSubmit}>
          <section className="flex flex-col gap-y-[30px] pt-[30px]">
            <div className="gap-y-3 flex flex-col">
              <TextInput
                autoFocus
                control={form.control}
                size={"lg"}
                wrapperClassName="space-y-3"
                labelClassName="font-medium text-sm"
                name="fullName"
                label={getText("user_name")}
                style={{
                  filter: "drop-shadow(0px 0px 3px #85F3FB)",
                }}
                variant={"filled"}
                placeholder={"vsai"}
              />
              <p className="text-xs text-font leading-[180%]">
                {getText("register_form_user_name_description")}
              </p>
            </div>
            <div className="space-y-3">
              <TextInput
                type="password"
                control={form.control}
                name="password"
                label={getText("password")}
                variant={"filled"}
                size={"lg"}
                wrapperClassName="space-y-3"
                labelClassName="font-medium text-sm"
                placeholder="＊＊＊＊＊＊＊"
              />
              <div className="gap-y-3 flex flex-col">
                <TextInput
                  control={form.control}
                  variant={"filled"}
                  size={"lg"}
                  wrapperClassName="space-y-3"
                  labelClassName="font-medium text-sm"
                  name="cofirmPassword"
                  label={getText("confirm_password")}
                  style={{
                    filter: "drop-shadow(0px 0px 3px #85F3FB)",
                  }}
                  type="password"
                  placeholder="＊＊＊＊＊＊＊"
                />
                <p className="text-xs text-font leading-[180%]">
                  {getText("confirm_password_desccription")}
                </p>
              </div>
            </div>
            <div className="gap-y-3 flex flex-col">
              <DOBInput
                control={form.control}
                variant={"filled"}
                size={"lg"}
                wrapperClassName="space-y-3"
                labelClassName="font-medium text-sm"
                name="dateOfBirth"
                label={getText("date_of_birth")}
                style={{
                  filter: "drop-shadow(0px 0px 3px #85F3FB)",
                }}
              />
              <p className="text-xs text-font leading-[180%]">
                {getText("register_form_dob_description")}
              </p>
            </div>

            <div className="flex items-center md:items-center gap-3">
              <Checkbox
                className="rounded-none border-[#7A7A7A] size-5"
                id="terms"
                checked={agreed}
                onCheckedChange={(e) => {
                  setAgreed(e as boolean);
                }}
              />
              <Label
                htmlFor="terms"
                className="text-font text-sm font-medium">
                {getText("register_form_terms")}
              </Label>
            </div>
            <div>
              <Button
                loading={isPending}
                type="submit"
                variant={"tertiary"}
                className="w-full h-[45px] text-white rounded-full font-bold">
                {getText("register")}
              </Button>
            </div>
          </section>
        </form>
      </Form>
    </div>
  );
};
