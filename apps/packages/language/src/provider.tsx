import { cookies } from "@vtuber/cookie/client";
import { useEffect, useState } from "react";
import { LanguageContext } from "./context";
import { dictionary } from "./dictionary";
import { DictionaryKey, LanguageKey } from "./types";

type LanguageProviderProps = {
  defaultLanguage: LanguageKey;
  children: React.ReactNode;
};

export function LanguageProvider(props: LanguageProviderProps) {
  const [lang, setLang] = useState<LanguageKey>(props.defaultLanguage || "ja");

  const setLanguage = (key: LanguageKey) => {
    setCookie("language", key, { path: "/", maxAge: 30 * 24 * 60 * 60 });
    setLang(key);
  };

  useEffect(() => {
    if (props.defaultLanguage) {
      setLang(props.defaultLanguage);
    }
  }, [props.defaultLanguage]);

  useEffect(() => {
    if (typeof window != "undefined") {
      cookies.addChangeListener(async () => {
        const language = getCookie("language");
        if (language && language != lang) {
          setLang(language as LanguageKey);
        }
      });
    }

    return () => {
      if (typeof window != "undefined") {
        cookies.removeChangeListener(() => {});
      }
    };
  }, []);

  const getLanguageKey = (key: DictionaryKey) => {
    return dictionary[lang][key];
  };

  return (
    <LanguageContext.Provider
      value={{
        getText: getLanguageKey,
        language: lang,
        setLanguage,
      }}>
      {props.children}
    </LanguageContext.Provider>
  );
}
