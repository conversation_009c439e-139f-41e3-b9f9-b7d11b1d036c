{"name": "@vtuber/language", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@vtuber/cookie": "workspace:*"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vtuber/eslint-config": "workspace:*", "@vtuber/typescript-config": "workspace:*", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "react": "^19.0.0", "tailwindcss": "^3.4.14", "typescript": "^5.8.3"}, "exports": {"./provider": "./src/provider.tsx", "./hooks": "./src/hooks.ts", "./types": "./src/types.ts"}}