import { Control, FieldValues, Path } from "react-hook-form";
import { InputProps } from "../input";
import { MultipleSelector, MultipleSelectorProps } from "../multi-select";
import { getInputProps, InputField } from "./input-field";

type SelectInputProps<T extends FieldValues> = Omit<
  MultipleSelectorProps,
  "value"
> & {
  control: Control<T>;
  name: Path<T>;
  label: string;
  placeholder?: string;
  description?: string;
  variant?: InputProps["variant"];
  size?: InputProps["size"];
  wrapperClassName?: string;
  className?: string;
  serialize?: (value: string) => unknown;
  stringify?: (value: unknown) => string;
  labelClassName?: string;
  options: {
    label: string;
    value: string;
    disabled?: boolean;
  }[];
};

export const MultiSelectInput = <T extends FieldValues>(
  props: SelectInputProps<T>,
) => {
  const [fieldProps, inputProps] = getInputProps(props);

  return (
    <InputField
      {...fieldProps}
      wrapperClassName={props.wrapperClassName}
      labelClassName={props.labelClassName}
      input={({ field }) => {
        const selectedOptions = field.value
          ? field.value
              .map((val: unknown) => {
                const stringVal = props.stringify?.(val) || String(val);
                return props.options.find((opt) => opt.value === stringVal);
              })
              .filter(Boolean)
          : [];

        return (
          <MultipleSelector
            {...inputProps}
            onChange={(e) => {
              field.onChange(
                e.map((v) => props.serialize?.(v.value) || v.value),
              );
            }}
            value={selectedOptions}
            options={props.options}
          />
        );
      }}
    />
  );
};
