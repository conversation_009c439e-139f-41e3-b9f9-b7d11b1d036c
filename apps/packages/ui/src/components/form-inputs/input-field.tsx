import { cn } from "@vtuber/ui/lib/utils";
import {
  Control,
  ControllerFieldState,
  ControllerRenderProps,
  FieldValues,
  Path,
  UseFormStateReturn,
} from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../form";

export type InputFieldProps<T extends FieldValues> = {
  control: Control<T>;
  name: Path<T>;
  label?: React.ReactNode;
  labelright?: React.ReactElement;
  description?: string;
  wrapperClassName?: string;
  labelClassName?: string;
  input: ({
    field,
    fieldState,
    formState,
  }: {
    field: ControllerRenderProps<T>;
    fieldState: ControllerFieldState;
    formState: UseFormStateReturn<T>;
  }) => React.ReactElement;
};

export const InputField = <T extends FieldValues>({
  name,
  label,
  description,
  labelright,
  control,
  input,
  wrapperClassName,
  labelClassName,
}: InputFieldProps<T>) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState, formState }) => (
        <FormItem className={cn("grid", wrapperClassName)}>
          {label && (
            <div className="flex items-center justify-between">
              <FormLabel
                htmlFor={name}
                className={cn("capitalize", labelClassName)}>
                {label}
              </FormLabel>
              {labelright && labelright}
            </div>
          )}

          <FormControl>{input({ field, fieldState, formState })}</FormControl>

          <FormMessage className="capitalize">
            {description?.replaceAll("_", " ")}
          </FormMessage>
        </FormItem>
      )}
    />
  );
};

export function getInputProps<T extends FieldValues, U>(
  props: Omit<InputFieldProps<T>, "input"> & U,
) {
  const {
    name,
    label,
    description,
    control,
    labelright,
    labelClassName,
    wrapperClassName,
    ...extras
  } = props;

  const fieldProps = {
    name,
    labelright,
    label,
    description,
    control,
    labelClassName,
    wrapperClassName,
  } as InputFieldProps<T>;

  return [fieldProps, extras] as const;
}
