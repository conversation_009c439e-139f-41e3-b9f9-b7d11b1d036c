import { useLanguage } from "@vtuber/language/hooks";
import { authClient, uploadFile } from "@vtuber/services/client";
import { MediaModal } from "@vtuber/ui/components/media-modal";
import {
  CircleX,
  Eye,
  ImagePlus,
  RotateCw,
  SquareDashedMousePointer,
  Trash2,
  Upload,
  UploadCloud,
  X,
} from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { base64ToFile, cn, getCdnUrl } from "../lib/utils";
import { Button, buttonVariants } from "./button";
import { DeleteDialog } from "./DeleteDialog";
import { ImageCropper } from "./image-croppper/image-cropper";
import { Ratio } from "./image-croppper/types";
import { MorphingDialogImage } from "./morphing-dialog";
import { NavigationBlocker } from "./navigation-blocker";
import { Spinner } from "./spinner";
import { errorToast, toast } from "./toaster";
import { VideoPlayer } from "./video/video-player";

type FileUploaderProps = {
  onUpload: (fileUrl: string) => void;
  className?: string;
  placeholder?: React.ReactNode;
  defaultImage?: string;
  name?: string;
  onUploadingChange?: (o: boolean) => void;
  accept?: string;
  mediaType?: string;
  canDelete?: boolean;
  croppingEnabled?: boolean;
  aspectRatio?: Ratio;
  shape?: "square" | "round";
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
  minWidth?: number;
  fileUploadDescrtion?: React.ReactNode;
  onDelete?: () => void;
  isPending?: boolean;
};

export const FileUploader: React.FC<FileUploaderProps> = ({
  onUpload,
  placeholder,
  className,
  defaultImage,
  name,
  onUploadingChange,
  accept,
  mediaType = "picture",
  canDelete = false,
  onDelete,
  isPending,
  croppingEnabled,
  aspectRatio,
  shape,
  minHeight,
  maxWidth,
  maxHeight,
  minWidth,
  fileUploadDescrtion,
}) => {
  const { getText } = useLanguage();
  const [opened, setOpened] = useState(false);
  const [croppedFile, setCroppedFile] = useState<File | null>(null);
  const [dragging, setDragging] = useState(false);
  const [preview, setPreview] = useState<string | null>(defaultImage || null);
  const [isUploading, setUploading] = useState<boolean>(false);
  const abortController = useRef<AbortController | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [originalImage, setOriginalImage] = useState<string>(
    defaultImage || "",
  );
  useEffect(() => {
    if (defaultImage) {
      setPreview(defaultImage);
    }
  }, [defaultImage]);

  const dragCounter = useRef(0);

  const setIsUploading = (value: boolean) => {
    setUploading(value);
    onUploadingChange?.(value);
  };

  const handleFileChange = async (file: File) => {
    if (file) {
      setIsUploading(true);
      abortController.current = new AbortController();

      try {
        const uploadedFileUrl = await uploadFile(
          file,
          abortController.current.signal,
          getText("upload_cancelled"),
        );
        if (uploadedFileUrl !== null) {
          onUpload(uploadedFileUrl);
          setPreview(getCdnUrl(uploadedFileUrl));
        }
      } finally {
        setIsUploading(false);
        abortController.current = null;
      }
    }
  };

  const handleReset: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    setIsUploading(false);
    e.stopPropagation();
    setPreview(defaultImage || null);
    setOriginalImage(defaultImage || "");
  };

  const handleCancel: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    if (abortController.current) {
      abortController.current.abort();
    }
    setIsUploading(false);
    setPreview(defaultImage || null);
    setOriginalImage(defaultImage || "");
    onUpload(defaultImage || "");
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current++;
    if (dragCounter.current === 1) {
      setDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current--;
    if (dragCounter.current === 0) {
      setDragging(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current = 0;
    setDragging(false);

    if (isUploading) {
      toast.error(getText("please_wait_for_the_file_to_finish_uploading"));
      return;
    }

    const images = Array.from(e.dataTransfer.files).filter((file) =>
      file.type.startsWith("image/"),
    );

    if (images.length > 0 && !isUploading) {
      const file = images[0];
      if (file) {
        handleFileChange(file);
      }
    } else {
      errorToast(getText("only_images_are_allowed"));
    }
  };
  return (
    <div
      onDrop={handleDrop}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      onMouseLeave={() => {
        dragCounter.current = 0;
        setDragging(false);
      }}>
      <NavigationBlocker condition={isUploading}>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <p>{getText("your_file_is_being_uploaded")}</p>
            <Spinner className="h-5 w-5" />
          </div>
          <p>{getText("any_changes_you_made_will_be_lost")}</p>
        </div>
      </NavigationBlocker>
      {croppingEnabled && (
        <ImageCropper
          initialImage={originalImage || ""}
          isCropping={opened}
          onCropComplete={(e) => {
            const mimeType = file?.type;
            const extension = file?.name.split(".").pop();
            const fileName = file?.name;
            const croppedFile = base64ToFile({
              base64String: e,
              mimeType,
              extension,
              fileName,
            });
            setCroppedFile(croppedFile);
            setPreview(e);
            setOpened(false);
          }}
          setIsCropping={setOpened}
          aspectRatio={aspectRatio}
          shape={shape}
          minHeight={minHeight}
          maxWidth={maxWidth}
          maxHeight={maxHeight}
          minWidth={minWidth}
        />
      )}
      <div
        className={cn(
          "border-2 border-dashed border-border rounded-lg flex-1 flex flex-col items-center justify-center cursor-pointer hover:bg-white/10 transition-colors relative overflow-hidden group",
          className,
          dragging && "border-dashed border-dark bg-muted/50",
        )}>
        {isUploading ? (
          <div className="h-72 w-full bg-white/10 flex items-center justify-center text-center p-8">
            <div className="text-muted-foreground animate-blur-in text-sm">
              <p className="lowercase">
                {mediaType === "picture"
                  ? getText("your_image_will_be_here")
                  : getText("your_video_will_be_here")}
              </p>
              <p className="animate-pulse mt-0.5 text-base italic">
                {getText("please_wait")}...
              </p>
              <Button
                onClick={handleCancel}
                type="button"
                variant={"outline"}
                className="mt-5 rounded-2xl border-destructive bg-destructive/20 hover:bg-destructive/20 hover:text-destructive text-destructive">
                <CircleX className="h-4 w-4 mr-1" />
                {getText("cancel_upload")}
              </Button>
            </div>
          </div>
        ) : preview ? (
          <MediaModal
            currentSrc={preview}
            mediaType={mediaType}
            className="relative overflow-hidden w-full max-h-[88rem]">
            {mediaType === "video" ? (
              <VideoPlayer
                src={preview}
                className="h-full w-full object-contain aspect-auto"
                showControls={false}
              />
            ) : (
              <MorphingDialogImage
                src={preview}
                alt="Uploaded File"
                className="h-full w-full object-contain aspect-video"
              />
            )}
            <div className="absolute inset-0 flex items-center justify-center group-hover:opacity-100 opacity-0 bg-black/50 transition-opacity duration-200 ease-in-out">
              {mediaType === "picture" && (
                <div className="flex flex-col items-center group-hover:animate-blur-in duration-75">
                  <div className="size-16 mb-2 bg-black/20 rounded-full flex items-center justify-center">
                    <Eye className="size-8 text-white" />
                  </div>
                  <p className="italic text-font">
                    {getText("click_to_view_image")}
                  </p>
                </div>
              )}
            </div>
          </MediaModal>
        ) : (
          <label
            htmlFor={name}
            className={cn("h-full w-full cursor-pointer", className)}>
            <div className="p-8">
              <ImagePreview
                isDragging={dragging}
                mediaType={mediaType}
                placeholder={placeholder}
              />
            </div>

            <input
              id={name}
              accept={accept || mediaType === "picture" ? "image/*" : "video/*"}
              disabled={isUploading}
              className="hidden"
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  if (croppingEnabled) {
                    setFile(file);
                    const reader = new FileReader();
                    reader.onload = (event) => {
                      if (event.target?.result) {
                        setPreview(event.target.result as string);
                        setOriginalImage(event.target.result as string);
                        setOpened(true);
                      }
                    };
                    reader.readAsDataURL(file);
                    return;
                  } else {
                    handleFileChange(file);
                  }
                }
              }}
            />
          </label>
        )}
      </div>
      <div className="flex items-center justify-between sm:flex-row flex-col pt-4">
        {fileUploadDescrtion && (
          <div className="text-sm text-muted-foreground py-3">
            {fileUploadDescrtion}
          </div>
        )}
        {!!preview && (
          <div className="flex gap-2 items-center sm:flex-nowrap flex-wrap sm:justify-end justify-center animate-slide-in-down">
            {canDelete && defaultImage && (
              <DeleteDialog
                name="Profile Image"
                onDelete={async () => {
                  const [res, err] = await authClient.deleteProfileImage({});

                  if (res) {
                    toast.success(res.message);
                    setPreview(null);
                    onDelete && onDelete();
                  }

                  if (err) {
                    toast.error(err.message);
                  }
                }}
                asChild>
                <Button
                  type="button"
                  disabled={isUploading || isPending}
                  size={"sm"}
                  variant={"outline"}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  {getText("delete")}
                </Button>
              </DeleteDialog>
            )}
            <Button
              onClick={handleReset}
              type="button"
              disabled={isUploading || preview === defaultImage || isPending}
              size={"sm"}
              variant={"outline"}>
              <X className="h-4 w-4 mr-2" />
              {getText("clear")}
            </Button>
            <label
              htmlFor={name}
              className={buttonVariants({
                variant: "dark",
                size: "sm",
              })}>
              <input
                id={name}
                accept={
                  accept || mediaType === "picture" ? "image/*" : "video/*"
                }
                disabled={isUploading || isPending}
                className="hidden"
                type="file"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    if (croppingEnabled) {
                      setFile(file);
                      const reader = new FileReader();
                      reader.onload = (event) => {
                        if (event.target?.result) {
                          setPreview(event.target.result as string);
                          setOriginalImage(event.target.result as string);
                          setOpened(true);
                        }
                      };
                      reader.readAsDataURL(file);
                      return;
                    } else {
                      handleFileChange(file);
                    }
                  }
                }}
              />
              <SquareDashedMousePointer className="h-4 w-4" />
              {getText("select_another")}
            </label>
            {croppingEnabled && (
              <Button
                type="button"
                onClick={() => setOpened((prev) => !prev)}
                disabled={isUploading || preview === defaultImage}
                size={"sm"}
                variant={"dark"}>
                <RotateCw className="h-4 w-4 mr-2" />
                {getText("recrop")}
              </Button>
            )}
            {croppingEnabled && (
              <Button
                onClick={() => {
                  if (croppedFile) {
                    handleFileChange(croppedFile);
                  }
                }}
                type="button"
                disabled={
                  isUploading || preview === defaultImage || !croppedFile
                }
                size={"sm"}
                variant={"dark"}>
                {isUploading ? (
                  <Spinner className="mr-2" />
                ) : (
                  <Upload className="h-4 w-4 mr-2" />
                )}

                {getText("upload")}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
const ImagePreview = ({
  isDragging,
  placeholder,
  mediaType,
}: {
  isDragging: boolean;
  placeholder?: React.ReactNode;
  mediaType?: string;
}) => {
  const { getText } = useLanguage();
  if (isDragging)
    return (
      <div className="h-[22dvh] w-full flex items-center justify-center text-font ">
        <div className="animate-blur-in duration-500 flex flex-col items-center gap-4">
          <ImagePlus className="h-12 w-12" />
          <p className="italic md:text-lg">{getText("drop_your_image_here")}</p>
        </div>
      </div>
    );

  if (placeholder) return <div>{placeholder}</div>;

  return (
    <div className="flex flex-col items-center">
      <div className="md:size-16 size-12 mb-4 bg-dark rounded-full flex items-center justify-center">
        <UploadCloud className="md:size-8 size-6 text-sub" />
      </div>
      <p className="md:text-lg text-base font-medium mb-2">
        {mediaType === "picture"
          ? getText("drop_your_image_here")
          : getText("drop_your_video_here")}
      </p>
      <p className="text-font md:text-sm text-xs mb-4 text-center">
        {getText("or_click_to_browse")}
        <br />
        {mediaType === "picture"
          ? getText("file_uploader_supported_formats")
          : getText("file_uploader_supported_video_formats")}
      </p>
      <p
        className={buttonVariants({
          variant: "outline-dark",
        })}>
        <Upload className="h-4 w-4 mr-2" />
        {mediaType === "picture"
          ? getText("select_image")
          : getText("select_video")}
      </p>
    </div>
  );
};
