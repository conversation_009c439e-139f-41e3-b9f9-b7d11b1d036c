export const dobFormatter = (value: string) => {
  const cleaned = value.replace(/\D/g, "");
  let formatted = cleaned;

  if (cleaned.length >= 4) {
    formatted = cleaned.substring(0, 4);
  }

  if (cleaned.length >= 6) {
    const month = cleaned.substring(4, 6);
    const monthNum = parseInt(month);

    // Only pad if the original input was already 2 digits or if we need to clamp the value
    let validMonth;
    if (monthNum > 12) {
      validMonth = "12";
    } else if (monthNum < 1) {
      validMonth = "01";
    } else {
      // Preserve original format - don't pad single digits unless they were already padded
      validMonth = month;
    }
    formatted += "-" + validMonth;
  } else if (cleaned.length > 4) {
    formatted += "-" + cleaned.substring(4);
  }

  if (cleaned.length >= 8) {
    const day = cleaned.substring(6, 8);
    const dayNum = parseInt(day);

    // Only pad if the original input was already 2 digits or if we need to clamp the value
    let validDay;
    if (dayNum > 31) {
      validDay = "31";
    } else if (dayNum < 1) {
      validDay = "01";
    } else {
      // Preserve original format - don't pad single digits unless they were already padded
      validDay = day;
    }
    formatted += "-" + validDay;
  } else if (cleaned.length > 6) {
    formatted += "-" + cleaned.substring(6);
  }

  return {
    formatted,
    validValue:
      cleaned.length >= 8
        ? cleaned.substring(0, 4) +
          (parseInt(cleaned.substring(4, 6)) > 12
            ? "12"
            : parseInt(cleaned.substring(4, 6)) < 1
              ? "01"
              : cleaned.substring(4, 6)) +
          (parseInt(cleaned.substring(6, 8)) > 31
            ? "31"
            : parseInt(cleaned.substring(6, 8)) < 1
              ? "01"
              : cleaned.substring(6, 8))
        : cleaned,
  };
};
