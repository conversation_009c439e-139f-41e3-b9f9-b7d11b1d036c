export const dobFormatter = (value: string) => {
  const cleaned = value.replace(/\D/g, "");
  let formatted = cleaned;

  if (cleaned.length >= 4) {
    formatted = cleaned.substring(0, 4);
  }

  if (cleaned.length >= 6) {
    const month = cleaned.substring(4, 6);
    const monthNum = parseInt(month);
    const validMonth =
      monthNum > 12 ? "12" : monthNum < 1 ? "01" : month.padStart(2, "0");
    formatted += "-" + validMonth;
  } else if (cleaned.length > 4) {
    formatted += "-" + cleaned.substring(4);
  }

  if (cleaned.length >= 8) {
    const day = cleaned.substring(6, 8);
    const dayNum = parseInt(day);
    const validDay =
      dayNum > 31 ? "31" : dayNum < 1 ? "01" : day.padStart(2, "0");
    formatted += "-" + validDay;
  } else if (cleaned.length > 6) {
    formatted += "-" + cleaned.substring(6);
  }

  return {
    formatted,
    validValue:
      cleaned.length >= 8
        ? cleaned.substring(0, 4) +
          (parseInt(cleaned.substring(4, 6)) > 12
            ? "12"
            : parseInt(cleaned.substring(4, 6)) < 1
              ? "01"
              : cleaned.substring(4, 6).padStart(2, "0")) +
          (parseInt(cleaned.substring(6, 8)) > 31
            ? "31"
            : parseInt(cleaned.substring(6, 8)) < 1
              ? "01"
              : cleaned.substring(6, 8).padStart(2, "0"))
        : cleaned,
  };
};
