import { Message } from "@bufbuild/protobuf";
import {
  Timestamp,
  timestampDate,
  timestampFromDate,
} from "@bufbuild/protobuf/wkt";
import { clsx, type ClassValue } from "clsx";
import { differenceInCalendarDays } from "date-fns";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getCdnUrl(path?: string | null) {
  if (path && path?.startsWith("http")) return path;
  return path ? `${process.env.CDN_URL}/${path}` : "";
}

export const CAN_USE_DOM = !!(
  typeof window !== "undefined" &&
  window.document &&
  window.document.createElement
);

const parseDataUrl = (dataUrl: string) => {
  let base64Data = dataUrl;

  if (dataUrl.startsWith("data:")) {
    const [_, dataPart] = dataUrl.split(",");
    base64Data = dataPart || "";
  }

  return { base64Data };
};

export const base64ToFile = ({
  base64String,
  mimeType,
  extension,
  fileName,
}: {
  base64String: string;
  mimeType?: string;
  extension?: string;
  fileName?: string;
}): File | null => {
  if (!CAN_USE_DOM) return null;
  const { base64Data } = parseDataUrl(base64String);

  const filename = fileName || `cropped-${Date.now()}${extension}`;

  const binaryString = window.atob(base64Data);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);

  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  const blob = new Blob([bytes], { type: mimeType });
  return new File([blob], filename, { type: mimeType });
};

export type GetProtoType<T extends Message> = Omit<T, keyof Message>;

export const convertToTimestamp = (raw: string): Timestamp => {
  const str = raw.toString();
  const year = parseInt(str.slice(0, 4), 10);
  const month = parseInt(str.slice(4, 6), 10) - 1;
  const day = parseInt(str.slice(6, 8), 10);

  const dob = new Date(Date.UTC(year, month, day));

  return timestampFromDate(dob);
};

export const checkDob = (raw: string) => {
  const str = raw.toString();
  const year = parseInt(str.slice(0, 4), 10);
  const currentYear = new Date().getFullYear();

  return year >= currentYear;
};

export const getProgress = (total: number, raised: number) => {
  const progress = Math.ceil((raised / total) * 100);
  if (progress > 100) return 100;
  return progress;
};

export const getRemainingDays = (end: Timestamp) => {
  const startDate = new Date();
  const endDate = timestampDate(end);

  const remainingDays =
    differenceInCalendarDays(endDate, startDate) < 0
      ? 0
      : differenceInCalendarDays(endDate, startDate);

  return remainingDays;
};
