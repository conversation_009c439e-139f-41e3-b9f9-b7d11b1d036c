import { Cookies } from "react-cookie";

export const cookies = new Cookies();

export function enableGlobals() {
  globalThis.getCookie = (name) => {
    return cookies.get(name) || "";
  };

  globalThis.setCookie = (name, value, options = { path: "/" }) => {
    return cookies.set(name, value, options);
  };

  globalThis.deleteCookie = (name, options = { path: "/" }) => {
    return cookies.remove(name, options);
  };
}
