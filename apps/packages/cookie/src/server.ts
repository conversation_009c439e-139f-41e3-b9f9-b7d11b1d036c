import {
  getCookie as getCookieH3,
  getEvent,
} from "@tanstack/react-start/server";

export function enableGlobals() {
  globalThis.getCookie = (name) => {
    return getCookieH3(name) || "";
  };

  globalThis.setCookie = (name, value, options = { path: "/" }) => {
    const request = getEvent();
    request.headers.set("Set-Cookie", `${name}=${value}`);
    // @ts-ignore
    request.modifiedCookies = {
      // @ts-ignore
      ...request.modifiedCookies,
      [name]: value,
    };
    return setCookie(name, value, options);
  };

  globalThis.deleteCookie = (name, options = { path: "/" }) => {
    return deleteCookie(name, options);
  };
}
