{"name": "@vtuber/cookie", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@tanstack/react-start": "latest", "react-cookie": "^7.2.2"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vtuber/eslint-config": "workspace:*", "@vtuber/typescript-config": "workspace:*", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "react": "^19.0.0", "tailwindcss": "^3.4.14", "typescript": "^5.8.3"}, "exports": {"./client": "./src/client.ts", "./server": "./src/server.ts", "./constants": "./src/index.ts"}}