{"name": "@vtuber/auth", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@vtuber/services": "workspace:*", "@vtuber/ui": "workspace:*", "@vtuber/cookie": "workspace:*", "@connectrpc/connect-query": "latest", "@vtuber/language": "workspace:*", "react-cookie": "^7.2.2", "react-hook-form": "^7.54.2", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vtuber/eslint-config": "workspace:*", "@vtuber/typescript-config": "workspace:*", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "react": "^19.0.0", "tailwindcss": "^3.4.14", "typescript": "^5.8.3"}, "exports": {"./provider": "./src/provider.tsx", "./hooks": "./src/hooks.ts", "./client": "./src/client.ts", "./server": "./src/server.ts"}}