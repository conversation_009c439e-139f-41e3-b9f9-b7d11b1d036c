interface CookieOptions {
  domain?: string | undefined;

  encode?(value: string): string;

  expires?: Date | undefined;

  httpOnly?: boolean | undefined;

  maxAge?: number | undefined;

  path?: string | undefined;

  priority?: "low" | "medium" | "high" | undefined;

  sameSite?: true | false | "lax" | "strict" | "none" | undefined;

  secure?: boolean | undefined;

  partitioned?: boolean;
}

declare function getCookie(name: string): string;
declare function setCookie(
  name: string,
  value: string,
  options?: CookieOptions,
): void;
declare function deleteCookie(name: string, options?: CookieOptions): void;
