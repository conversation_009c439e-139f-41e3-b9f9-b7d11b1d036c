import { Category } from "@vtuber/services/taxonomy";
import { GetSessionResponse } from "@vtuber/services/users";
import { createContext } from "react";

export interface IAuthContext {
  session: GetSessionResponse | null;
  isCreator: boolean;
  isAdmin: boolean;
  isLoading: boolean;
  setSesssion: React.Dispatch<React.SetStateAction<GetSessionResponse | null>>;
  isEmailVerified: boolean;
  hasVtuberProfile: boolean;
  logout: () => void;
}

export const AuthContext = createContext<IAuthContext>({
  session: null,
  isCreator: false,
  isAdmin: false,
  isLoading: true,
  setSesssion: () => {},
  isEmailVerified: false,
  logout: () => {},
  hasVtuberProfile: false,
});

export interface CategoryContext {
  categories: Category[];
  setCategories: (categories: Category[]) => void;
}
export const CategoryContext = createContext<CategoryContext>({
  categories: [],
  setCategories: () => {},
});

export interface CreatorParticipationContext {
  creatorParticipation: string[];
  setCreatorParticipation: (events: string[]) => void;
}

export const CreatorParticipationContext =
  createContext<CreatorParticipationContext>({
    creatorParticipation: [],
    setCreatorParticipation: () => {},
  });
