import { useContext, useEffect, useState } from "react";
import {
  AuthContext,
  CategoryContext,
  CreatorParticipationContext,
} from "./context";

export const useSession = () => {
  const { session } = useContext(AuthContext);
  return session;
};

export const useLogout = () => {
  const { logout } = useContext(AuthContext);
  return logout;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  return context;
};

export const useCategories = () => {
  const { categories, setCategories } = useContext(CategoryContext);
  const getCategoryById = (id: string) => categories.find((c) => c.id === id);
  const getMultipleCategories = (ids: string[]) => {
    const cats = ids
      .map((c) => categories.find((t) => t.id === c))
      .filter(Boolean);

    return cats;
  };

  const deleteCategoryById = (id: string) => {
    const newCategories = categories.filter((c) => c.id !== id);
    setCategories(newCategories);
  };

  return {
    categories,
    getCategoryById,
    getMultipleCategories,
    deleteCategoryById,
  };
};

export const useCreatorParticipatedEvents = () => {
  const { creatorParticipation, setCreatorParticipation } = useContext(
    CreatorParticipationContext,
  );

  return { creatorParticipation, setCreatorParticipation };
};

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener("change", listener);
    return () => media.removeEventListener("change", listener);
  }, [matches, query]);

  return matches;
}
