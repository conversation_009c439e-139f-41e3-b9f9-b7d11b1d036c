import { cookies } from "@vtuber/cookie/client";
import {
  JWT_COOKIE_NAME,
  JWT_REFRESH_COOKIE_NAME,
} from "@vtuber/cookie/constants";
import { authClient } from "@vtuber/services/client";
import { Category } from "@vtuber/services/taxonomy";
import { GetSessionResponse } from "@vtuber/services/users";
import { useEffect, useState } from "react";
import {
  AuthContext,
  CategoryContext,
  CreatorParticipationContext,
} from "./context";

type AuthProviderProps = {
  user: GetSessionResponse | null;
  children: React.ReactNode;
};

export const userPromise = Promise.withResolvers<GetSessionResponse | null>();

export function AuthProvider(props: AuthProviderProps) {
  const [user, setUser] = useState<GetSessionResponse | null>(props.user);

  useEffect(() => {
    if (props.user) {
      setUser(props.user);
    }
  }, [props.user]);

  const logout = () => {
    setUser(null);
    // authClient.logout({}).finally(() => {
    deleteCookie(JWT_COOKIE_NAME);
    deleteCookie(JWT_REFRESH_COOKIE_NAME);
    // });
  };

  useEffect(() => {
    if (typeof window != "undefined") {
      cookies.addChangeListener(async () => {
        const token = cookies.get(JWT_COOKIE_NAME);
        if (!token) {
          setUser(null);
        }
        const [data, err] = await authClient.getSession({});
        if (data?.user) {
          setUser(data);
        }
      });
    }

    return () => {
      if (typeof window != "undefined") {
        cookies.removeChangeListener(() => {});
      }
    };
  }, []);

  useEffect(() => {
    if (typeof window != "undefined") {
      if (user) {
        const token = cookies.get(JWT_COOKIE_NAME);
        if (!token) {
          setUser(null);
          return;
        }
        const base64Url = token.split(".")[1];
        const base64 = base64Url.replace("-", "+").replace("_", "/");
        const jsonPayload = decodeURIComponent(
          atob(base64)
            .split("")
            .map(function (c) {
              return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
            })
            .join(""),
        );
        try {
          const payload = JSON.parse(jsonPayload) as {
            ID: string;
            Email: string;
            Role: string;
            IsVtuber: boolean;
            VtuberId: string | null;
          };

          if (
            payload.Email != user.user?.email ||
            payload.Role != user.user?.role ||
            payload.IsVtuber != (user.user?.role === "vtuber") ||
            payload.VtuberId != (user.vtuber ? user.vtuber?.id : null)
          ) {
            authClient
              .refreshToken({
                refreshToken: cookies.get(JWT_REFRESH_COOKIE_NAME),
              })
              .then(([tokens, err]) => {
                if (tokens) {
                  setCookie(JWT_COOKIE_NAME, tokens.accessToken);
                  setCookie(JWT_REFRESH_COOKIE_NAME, tokens.refreshToken);
                } else {
                  setUser(null);
                }
              });
            return;
          }
        } catch (error) {
          setUser(null);
        }
      }
    }
  }, [user]);

  return (
    <AuthContext.Provider
      value={{
        isAdmin: user?.user?.role == "admin",
        isCreator: user?.user?.role === "vtuber" || false,
        isEmailVerified: user?.user?.emailVerified || false,
        isLoading: false,
        logout,
        setSesssion: setUser,
        session: user,
        hasVtuberProfile: user?.vtuber ? true : false,
      }}>
      {props.children}
    </AuthContext.Provider>
  );
}

type CategoriesProviderProps = {
  categories: Category[] | null;
  children: React.ReactNode;
};

export function CategoriesProvider(props: CategoriesProviderProps) {
  const [categories, setCategories] = useState<Category[]>(
    props.categories || [],
  );

  useEffect(() => {
    setCategories(props.categories || []);
  }, [props.categories]);

  return (
    <CategoryContext.Provider value={{ categories, setCategories }}>
      {props.children}
    </CategoryContext.Provider>
  );
}

type CreatorParticipatiedProviderProps = {
  creatorParticipatedEvents: string[] | null;
  children: React.ReactNode;
};

export function CreatorParticipatedEventProvider(
  props: CreatorParticipatiedProviderProps,
) {
  const [events, setEvents] = useState<string[]>(
    props.creatorParticipatedEvents || [],
  );

  return (
    <CreatorParticipationContext.Provider
      value={{
        creatorParticipation: events,
        setCreatorParticipation: setEvents,
      }}>
      {props.children}
    </CreatorParticipationContext.Provider>
  );
}
