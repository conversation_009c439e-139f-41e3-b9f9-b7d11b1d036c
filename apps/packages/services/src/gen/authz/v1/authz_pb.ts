// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file authz/v1/authz.proto (package api.authz.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenExtension,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { extDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { MethodOptions as MethodOptions$1 } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_descriptor } from "@bufbuild/protobuf/wkt";

/**
 * Describes the file authz/v1/authz.proto.
 */
export const file_authz_v1_authz: GenFile =
  /*@__PURE__*/
  fileDesc(
    "ChRhdXRoei92MS9hdXRoei5wcm90bxIMYXBpLmF1dGh6LnYxIqMBCg1NZXRob2RPcHRpb25zEhQKB3JlcXVpcmUYASABKAhIAIgBARIVCghpc19hZG1pbhgCIAEoCEgBiAEBEhYKCWlzX3Z0dWJlchgDIAEoCEgCiAEBEhcKCmV4cHJlc3Npb24YBCABKAlIA4gBAUIKCghfcmVxdWlyZUILCglfaXNfYWRtaW5CDAoKX2lzX3Z0dWJlckINCgtfZXhwcmVzc2lvbjpXCgdvcHRpb25zEh4uZ29vZ2xlLnByb3RvYnVmLk1ldGhvZE9wdGlvbnMY0IYDIAEoCzIbLmFwaS5hdXRoei52MS5NZXRob2RPcHRpb25zUgdvcHRpb25zQjBaLmdpdGh1Yi5jb20vbnNwLWluYy92dHViZXIvYXBpL2F1dGh6L3YxO2F1dGh6djFiBnByb3RvMw",
    [file_google_protobuf_descriptor],
  );

/**
 * @generated from message api.authz.v1.MethodOptions
 */
export type MethodOptions = Message<"api.authz.v1.MethodOptions"> & {
  /**
   * @generated from field: optional bool require = 1;
   */
  require?: boolean;

  /**
   * @generated from field: optional bool is_admin = 2;
   */
  isAdmin?: boolean;

  /**
   * @generated from field: optional bool is_vtuber = 3;
   */
  isVtuber?: boolean;

  /**
   * @generated from field: optional string expression = 4;
   */
  expression?: string;
};

/**
 * Describes the message api.authz.v1.MethodOptions.
 * Use `create(MethodOptionsSchema)` to create a new message.
 */
export const MethodOptionsSchema: GenMessage<MethodOptions> =
  /*@__PURE__*/
  messageDesc(file_authz_v1_authz, 0);

/**
 * @generated from extension: api.authz.v1.MethodOptions options = 50000;
 */
export const options: GenExtension<MethodOptions$1, MethodOptions> =
  /*@__PURE__*/
  extDesc(file_authz_v1_authz, 0);
