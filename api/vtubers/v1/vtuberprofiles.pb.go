// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: vtubers/v1/vtuberprofiles.proto

package vtubersv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddVtuberProfileRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DisplayName      string                 `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty" validate:"required"`
	Furigana         string                 `protobuf:"bytes,2,opt,name=furigana,proto3" json:"furigana,omitempty" validate:"required"`
	Image            string                 `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	BannerImage      string                 `protobuf:"bytes,4,opt,name=banner_image,json=bannerImage,proto3" json:"banner_image,omitempty"`
	Description      *string                `protobuf:"bytes,5,opt,name=description,proto3,oneof" json:"description,omitempty"`
	SocialMediaLinks *v1.SocialMediaLinks   `protobuf:"bytes,6,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	Username         string                 `protobuf:"bytes,7,opt,name=username,proto3" json:"username,omitempty" validate:"required"`
	Categories       []string               `protobuf:"bytes,8,rep,name=categories,proto3" json:"categories,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AddVtuberProfileRequest) Reset() {
	*x = AddVtuberProfileRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberProfileRequest) ProtoMessage() {}

func (x *AddVtuberProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberProfileRequest.ProtoReflect.Descriptor instead.
func (*AddVtuberProfileRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{0}
}

func (x *AddVtuberProfileRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *AddVtuberProfileRequest) GetFurigana() string {
	if x != nil {
		return x.Furigana
	}
	return ""
}

func (x *AddVtuberProfileRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *AddVtuberProfileRequest) GetBannerImage() string {
	if x != nil {
		return x.BannerImage
	}
	return ""
}

func (x *AddVtuberProfileRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AddVtuberProfileRequest) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

func (x *AddVtuberProfileRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *AddVtuberProfileRequest) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

type AddVtuberProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *VtuberProfile         `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberProfileResponse) Reset() {
	*x = AddVtuberProfileResponse{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberProfileResponse) ProtoMessage() {}

func (x *AddVtuberProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberProfileResponse.ProtoReflect.Descriptor instead.
func (*AddVtuberProfileResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{1}
}

func (x *AddVtuberProfileResponse) GetData() *VtuberProfile {
	if x != nil {
		return x.Data
	}
	return nil
}

type VtuberProfile struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId           string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DisplayName      string                 `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Furigana         string                 `protobuf:"bytes,4,opt,name=furigana,proto3" json:"furigana,omitempty"`
	Image            *string                `protobuf:"bytes,5,opt,name=image,proto3,oneof" json:"image,omitempty"`
	BannerImage      *string                `protobuf:"bytes,6,opt,name=banner_image,json=bannerImage,proto3,oneof" json:"banner_image,omitempty"`
	SocialMediaLinks *v1.SocialMediaLinks   `protobuf:"bytes,7,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	Description      *string                `protobuf:"bytes,8,opt,name=description,proto3,oneof" json:"description,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	IsUserSubscribed bool                   `protobuf:"varint,10,opt,name=is_user_subscribed,json=isUserSubscribed,proto3" json:"is_user_subscribed,omitempty"`
	HasLiked         bool                   `protobuf:"varint,11,opt,name=has_liked,json=hasLiked,proto3" json:"has_liked,omitempty"`
	Username         string                 `protobuf:"bytes,12,opt,name=username,proto3" json:"username,omitempty"`
	Categories       []string               `protobuf:"bytes,13,rep,name=categories,proto3" json:"categories,omitempty"`
	IsPlanActive     bool                   `protobuf:"varint,14,opt,name=is_plan_active,json=isPlanActive,proto3" json:"is_plan_active,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *VtuberProfile) Reset() {
	*x = VtuberProfile{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VtuberProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VtuberProfile) ProtoMessage() {}

func (x *VtuberProfile) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VtuberProfile.ProtoReflect.Descriptor instead.
func (*VtuberProfile) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{2}
}

func (x *VtuberProfile) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VtuberProfile) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *VtuberProfile) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *VtuberProfile) GetFurigana() string {
	if x != nil {
		return x.Furigana
	}
	return ""
}

func (x *VtuberProfile) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

func (x *VtuberProfile) GetBannerImage() string {
	if x != nil && x.BannerImage != nil {
		return *x.BannerImage
	}
	return ""
}

func (x *VtuberProfile) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

func (x *VtuberProfile) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *VtuberProfile) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VtuberProfile) GetIsUserSubscribed() bool {
	if x != nil {
		return x.IsUserSubscribed
	}
	return false
}

func (x *VtuberProfile) GetHasLiked() bool {
	if x != nil {
		return x.HasLiked
	}
	return false
}

func (x *VtuberProfile) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *VtuberProfile) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *VtuberProfile) GetIsPlanActive() bool {
	if x != nil {
		return x.IsPlanActive
	}
	return false
}

type GetVtuberProfileByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberProfileByIdRequest) Reset() {
	*x = GetVtuberProfileByIdRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberProfileByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberProfileByIdRequest) ProtoMessage() {}

func (x *GetVtuberProfileByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberProfileByIdRequest.ProtoReflect.Descriptor instead.
func (*GetVtuberProfileByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{3}
}

func (x *GetVtuberProfileByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetVtuberProfileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberProfileRequest) Reset() {
	*x = GetVtuberProfileRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberProfileRequest) ProtoMessage() {}

func (x *GetVtuberProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberProfileRequest.ProtoReflect.Descriptor instead.
func (*GetVtuberProfileRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{4}
}

type GetVtuberProfileByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *VtuberProfile         `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberProfileByIdResponse) Reset() {
	*x = GetVtuberProfileByIdResponse{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberProfileByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberProfileByIdResponse) ProtoMessage() {}

func (x *GetVtuberProfileByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberProfileByIdResponse.ProtoReflect.Descriptor instead.
func (*GetVtuberProfileByIdResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{5}
}

func (x *GetVtuberProfileByIdResponse) GetData() *VtuberProfile {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateVtuberProfileRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	DisplayName      string                 `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty" validate:"required"`
	Furigana         string                 `protobuf:"bytes,3,opt,name=furigana,proto3" json:"furigana,omitempty" validate:"required"`
	Image            string                 `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty" validate:"required"`
	BannerImage      string                 `protobuf:"bytes,5,opt,name=banner_image,json=bannerImage,proto3" json:"banner_image,omitempty" validate:"required"`
	Description      *string                `protobuf:"bytes,6,opt,name=description,proto3,oneof" json:"description,omitempty"`
	SocialMediaLinks *v1.SocialMediaLinks   `protobuf:"bytes,7,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	Categories       []string               `protobuf:"bytes,8,rep,name=categories,proto3" json:"categories,omitempty"`
	IsPlanActive     bool                   `protobuf:"varint,9,opt,name=is_plan_active,json=isPlanActive,proto3" json:"is_plan_active,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdateVtuberProfileRequest) Reset() {
	*x = UpdateVtuberProfileRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberProfileRequest) ProtoMessage() {}

func (x *UpdateVtuberProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberProfileRequest.ProtoReflect.Descriptor instead.
func (*UpdateVtuberProfileRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateVtuberProfileRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateVtuberProfileRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *UpdateVtuberProfileRequest) GetFurigana() string {
	if x != nil {
		return x.Furigana
	}
	return ""
}

func (x *UpdateVtuberProfileRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdateVtuberProfileRequest) GetBannerImage() string {
	if x != nil {
		return x.BannerImage
	}
	return ""
}

func (x *UpdateVtuberProfileRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateVtuberProfileRequest) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

func (x *UpdateVtuberProfileRequest) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *UpdateVtuberProfileRequest) GetIsPlanActive() bool {
	if x != nil {
		return x.IsPlanActive
	}
	return false
}

type GetAllVtuberProfilesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	DisplayName   *string                `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	CategoryId    *string                `protobuf:"bytes,3,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllVtuberProfilesRequest) Reset() {
	*x = GetAllVtuberProfilesRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberProfilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberProfilesRequest) ProtoMessage() {}

func (x *GetAllVtuberProfilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberProfilesRequest.ProtoReflect.Descriptor instead.
func (*GetAllVtuberProfilesRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{7}
}

func (x *GetAllVtuberProfilesRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAllVtuberProfilesRequest) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *GetAllVtuberProfilesRequest) GetCategoryId() string {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return ""
}

type GetAllVtuberProfilesResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*VtuberProfile       `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllVtuberProfilesResponse) Reset() {
	*x = GetAllVtuberProfilesResponse{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberProfilesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberProfilesResponse) ProtoMessage() {}

func (x *GetAllVtuberProfilesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberProfilesResponse.ProtoReflect.Descriptor instead.
func (*GetAllVtuberProfilesResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{8}
}

func (x *GetAllVtuberProfilesResponse) GetData() []*VtuberProfile {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllVtuberProfilesResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type DeleteVtuberProfileByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVtuberProfileByIdRequest) Reset() {
	*x = DeleteVtuberProfileByIdRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVtuberProfileByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVtuberProfileByIdRequest) ProtoMessage() {}

func (x *DeleteVtuberProfileByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVtuberProfileByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteVtuberProfileByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteVtuberProfileByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type VerifyVtuberProfileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyVtuberProfileRequest) Reset() {
	*x = VerifyVtuberProfileRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyVtuberProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyVtuberProfileRequest) ProtoMessage() {}

func (x *VerifyVtuberProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyVtuberProfileRequest.ProtoReflect.Descriptor instead.
func (*VerifyVtuberProfileRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{10}
}

func (x *VerifyVtuberProfileRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CreateVtuberProfileAccessRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Description   string                 `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateVtuberProfileAccessRequest) Reset() {
	*x = CreateVtuberProfileAccessRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVtuberProfileAccessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVtuberProfileAccessRequest) ProtoMessage() {}

func (x *CreateVtuberProfileAccessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVtuberProfileAccessRequest.ProtoReflect.Descriptor instead.
func (*CreateVtuberProfileAccessRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{11}
}

func (x *CreateVtuberProfileAccessRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type UpdateVtuberProfileAccessRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Description   string                 `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVtuberProfileAccessRequest) Reset() {
	*x = UpdateVtuberProfileAccessRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberProfileAccessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberProfileAccessRequest) ProtoMessage() {}

func (x *UpdateVtuberProfileAccessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberProfileAccessRequest.ProtoReflect.Descriptor instead.
func (*UpdateVtuberProfileAccessRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateVtuberProfileAccessRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type UpdateVtuberProfileAccessResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVtuberProfileAccessResponse) Reset() {
	*x = UpdateVtuberProfileAccessResponse{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberProfileAccessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberProfileAccessResponse) ProtoMessage() {}

func (x *UpdateVtuberProfileAccessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberProfileAccessResponse.ProtoReflect.Descriptor instead.
func (*UpdateVtuberProfileAccessResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateVtuberProfileAccessResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateVtuberProfileAccessResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetAllVtuberProfileAccessRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	Status        *string                `protobuf:"bytes,2,opt,name=status,proto3,oneof" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllVtuberProfileAccessRequest) Reset() {
	*x = GetAllVtuberProfileAccessRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberProfileAccessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberProfileAccessRequest) ProtoMessage() {}

func (x *GetAllVtuberProfileAccessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberProfileAccessRequest.ProtoReflect.Descriptor instead.
func (*GetAllVtuberProfileAccessRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{14}
}

func (x *GetAllVtuberProfileAccessRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAllVtuberProfileAccessRequest) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

type GetAllVtuberProfileAccessResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*VtuberAccessRequest `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllVtuberProfileAccessResponse) Reset() {
	*x = GetAllVtuberProfileAccessResponse{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberProfileAccessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberProfileAccessResponse) ProtoMessage() {}

func (x *GetAllVtuberProfileAccessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberProfileAccessResponse.ProtoReflect.Descriptor instead.
func (*GetAllVtuberProfileAccessResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{15}
}

func (x *GetAllVtuberProfileAccessResponse) GetData() []*VtuberAccessRequest {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllVtuberProfileAccessResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type VtuberAccessRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty" validate:"required"`
	Reason        *string                `protobuf:"bytes,4,opt,name=reason,proto3,oneof" json:"reason,omitempty" validate:"required"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UserId        string                 `protobuf:"bytes,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty" validate:"required"`
	Name          string                 `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VtuberAccessRequest) Reset() {
	*x = VtuberAccessRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VtuberAccessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VtuberAccessRequest) ProtoMessage() {}

func (x *VtuberAccessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VtuberAccessRequest.ProtoReflect.Descriptor instead.
func (*VtuberAccessRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{16}
}

func (x *VtuberAccessRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VtuberAccessRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VtuberAccessRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *VtuberAccessRequest) GetReason() string {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return ""
}

func (x *VtuberAccessRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VtuberAccessRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *VtuberAccessRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DenyVtuberProfileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DenyVtuberProfileRequest) Reset() {
	*x = DenyVtuberProfileRequest{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DenyVtuberProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DenyVtuberProfileRequest) ProtoMessage() {}

func (x *DenyVtuberProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DenyVtuberProfileRequest.ProtoReflect.Descriptor instead.
func (*DenyVtuberProfileRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{17}
}

func (x *DenyVtuberProfileRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DenyVtuberProfileRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type UpdateVtuberProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVtuberProfileResponse) Reset() {
	*x = UpdateVtuberProfileResponse{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberProfileResponse) ProtoMessage() {}

func (x *UpdateVtuberProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberProfileResponse.ProtoReflect.Descriptor instead.
func (*UpdateVtuberProfileResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateVtuberProfileResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateVtuberProfileResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type VerifyVtuberProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyVtuberProfileResponse) Reset() {
	*x = VerifyVtuberProfileResponse{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyVtuberProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyVtuberProfileResponse) ProtoMessage() {}

func (x *VerifyVtuberProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyVtuberProfileResponse.ProtoReflect.Descriptor instead.
func (*VerifyVtuberProfileResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{19}
}

func (x *VerifyVtuberProfileResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *VerifyVtuberProfileResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CreateVtuberProfileAccessResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateVtuberProfileAccessResponse) Reset() {
	*x = CreateVtuberProfileAccessResponse{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVtuberProfileAccessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVtuberProfileAccessResponse) ProtoMessage() {}

func (x *CreateVtuberProfileAccessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVtuberProfileAccessResponse.ProtoReflect.Descriptor instead.
func (*CreateVtuberProfileAccessResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{20}
}

func (x *CreateVtuberProfileAccessResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateVtuberProfileAccessResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DenyVtuberProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DenyVtuberProfileResponse) Reset() {
	*x = DenyVtuberProfileResponse{}
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DenyVtuberProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DenyVtuberProfileResponse) ProtoMessage() {}

func (x *DenyVtuberProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberprofiles_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DenyVtuberProfileResponse.ProtoReflect.Descriptor instead.
func (*DenyVtuberProfileResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP(), []int{21}
}

func (x *DenyVtuberProfileResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DenyVtuberProfileResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_vtubers_v1_vtuberprofiles_proto protoreflect.FileDescriptor

const file_vtubers_v1_vtuberprofiles_proto_rawDesc = "" +
	"\n" +
	"\x1fvtubers/v1/vtuberprofiles.proto\x12\x0eapi.vtubers.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\xd3\x02\n" +
	"\x17AddVtuberProfileRequest\x12!\n" +
	"\fdisplay_name\x18\x01 \x01(\tR\vdisplayName\x12\x1a\n" +
	"\bfurigana\x18\x02 \x01(\tR\bfurigana\x12\x14\n" +
	"\x05image\x18\x03 \x01(\tR\x05image\x12!\n" +
	"\fbanner_image\x18\x04 \x01(\tR\vbannerImage\x12%\n" +
	"\vdescription\x18\x05 \x01(\tH\x00R\vdescription\x88\x01\x01\x12M\n" +
	"\x12social_media_links\x18\x06 \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\x12\x1a\n" +
	"\busername\x18\a \x01(\tR\busername\x12\x1e\n" +
	"\n" +
	"categories\x18\b \x03(\tR\n" +
	"categoriesB\x0e\n" +
	"\f_description\"M\n" +
	"\x18AddVtuberProfileResponse\x121\n" +
	"\x04data\x18\x01 \x01(\v2\x1d.api.vtubers.v1.VtuberProfileR\x04data\"\xc3\x04\n" +
	"\rVtuberProfile\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12!\n" +
	"\fdisplay_name\x18\x03 \x01(\tR\vdisplayName\x12\x1a\n" +
	"\bfurigana\x18\x04 \x01(\tR\bfurigana\x12\x19\n" +
	"\x05image\x18\x05 \x01(\tH\x00R\x05image\x88\x01\x01\x12&\n" +
	"\fbanner_image\x18\x06 \x01(\tH\x01R\vbannerImage\x88\x01\x01\x12M\n" +
	"\x12social_media_links\x18\a \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\x12%\n" +
	"\vdescription\x18\b \x01(\tH\x02R\vdescription\x88\x01\x01\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12,\n" +
	"\x12is_user_subscribed\x18\n" +
	" \x01(\bR\x10isUserSubscribed\x12\x1b\n" +
	"\thas_liked\x18\v \x01(\bR\bhasLiked\x12\x1a\n" +
	"\busername\x18\f \x01(\tR\busername\x12\x1e\n" +
	"\n" +
	"categories\x18\r \x03(\tR\n" +
	"categories\x12$\n" +
	"\x0eis_plan_active\x18\x0e \x01(\bR\fisPlanActiveB\b\n" +
	"\x06_imageB\x0f\n" +
	"\r_banner_imageB\x0e\n" +
	"\f_description\"-\n" +
	"\x1bGetVtuberProfileByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\x19\n" +
	"\x17GetVtuberProfileRequest\"Q\n" +
	"\x1cGetVtuberProfileByIdResponse\x121\n" +
	"\x04data\x18\x01 \x01(\v2\x1d.api.vtubers.v1.VtuberProfileR\x04data\"\xf0\x02\n" +
	"\x1aUpdateVtuberProfileRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12!\n" +
	"\fdisplay_name\x18\x02 \x01(\tR\vdisplayName\x12\x1a\n" +
	"\bfurigana\x18\x03 \x01(\tR\bfurigana\x12\x14\n" +
	"\x05image\x18\x04 \x01(\tR\x05image\x12!\n" +
	"\fbanner_image\x18\x05 \x01(\tR\vbannerImage\x12%\n" +
	"\vdescription\x18\x06 \x01(\tH\x00R\vdescription\x88\x01\x01\x12M\n" +
	"\x12social_media_links\x18\a \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\x12\x1e\n" +
	"\n" +
	"categories\x18\b \x03(\tR\n" +
	"categories\x12$\n" +
	"\x0eis_plan_active\x18\t \x01(\bR\fisPlanActiveB\x0e\n" +
	"\f_description\"\xe2\x01\n" +
	"\x1bGetAllVtuberProfilesRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01\x12&\n" +
	"\fdisplay_name\x18\x02 \x01(\tH\x01R\vdisplayName\x88\x01\x01\x12$\n" +
	"\vcategory_id\x18\x03 \x01(\tH\x02R\n" +
	"categoryId\x88\x01\x01B\r\n" +
	"\v_paginationB\x0f\n" +
	"\r_display_nameB\x0e\n" +
	"\f_category_id\"\xa2\x01\n" +
	"\x1cGetAllVtuberProfilesResponse\x121\n" +
	"\x04data\x18\x01 \x03(\v2\x1d.api.vtubers.v1.VtuberProfileR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"0\n" +
	"\x1eDeleteVtuberProfileByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\",\n" +
	"\x1aVerifyVtuberProfileRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"D\n" +
	" CreateVtuberProfileAccessRequest\x12 \n" +
	"\vdescription\x18\x01 \x01(\tR\vdescription\"D\n" +
	" UpdateVtuberProfileAccessRequest\x12 \n" +
	"\vdescription\x18\x01 \x01(\tR\vdescription\"W\n" +
	"!UpdateVtuberProfileAccessResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xa0\x01\n" +
	" GetAllVtuberProfileAccessRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01\x12\x1b\n" +
	"\x06status\x18\x02 \x01(\tH\x01R\x06status\x88\x01\x01B\r\n" +
	"\v_paginationB\t\n" +
	"\a_status\"\xad\x01\n" +
	"!GetAllVtuberProfileAccessResponse\x127\n" +
	"\x04data\x18\x01 \x03(\v2#.api.vtubers.v1.VtuberAccessRequestR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\xef\x01\n" +
	"\x13VtuberAccessRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12\x1b\n" +
	"\x06reason\x18\x04 \x01(\tH\x00R\x06reason\x88\x01\x01\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x17\n" +
	"\auser_id\x18\x06 \x01(\tR\x06userId\x12\x12\n" +
	"\x04name\x18\a \x01(\tR\x04nameB\t\n" +
	"\a_reason\"B\n" +
	"\x18DenyVtuberProfileRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\"Q\n" +
	"\x1bUpdateVtuberProfileResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"Q\n" +
	"\x1bVerifyVtuberProfileResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"W\n" +
	"!CreateVtuberProfileAccessResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"O\n" +
	"\x19DenyVtuberProfileResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\x8e\v\n" +
	"\x15VtuberProfilesService\x12o\n" +
	"\x10AddVtuberProfile\x12'.api.vtubers.v1.AddVtuberProfileRequest\x1a(.api.vtubers.v1.AddVtuberProfileResponse\"\b\x82\xb5\x18\x04\b\x01\x18\x01\x12q\n" +
	"\x14GetVtuberProfileById\x12+.api.vtubers.v1.GetVtuberProfileByIdRequest\x1a,.api.vtubers.v1.GetVtuberProfileByIdResponse\x12s\n" +
	"\x10GetVtuberProfile\x12'.api.vtubers.v1.GetVtuberProfileRequest\x1a,.api.vtubers.v1.GetVtuberProfileByIdResponse\"\b\x82\xb5\x18\x04\b\x01\x18\x01\x12\xa1\x01\n" +
	"\x13UpdateVtuberProfile\x12*.api.vtubers.v1.UpdateVtuberProfileRequest\x1a+.api.vtubers.v1.UpdateVtuberProfileResponse\"1\x82\xb5\x18-\b\x01\")_user.vtuberId==id || _user.role=='admin'\x12s\n" +
	"\x14GetAllVtuberProfiles\x12+.api.vtubers.v1.GetAllVtuberProfilesRequest\x1a,.api.vtubers.v1.GetAllVtuberProfilesResponse\"\x00\x12x\n" +
	"\x13VerifyVtuberProfile\x12*.api.vtubers.v1.VerifyVtuberProfileRequest\x1a+.api.vtubers.v1.VerifyVtuberProfileResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12\x80\x01\n" +
	"\x11ApplyVtuberAccess\x120.api.vtubers.v1.CreateVtuberProfileAccessRequest\x1a1.api.vtubers.v1.CreateVtuberProfileAccessResponse\"\x06\x82\xb5\x18\x02\b\x01\x12z\n" +
	"\x19GetMyVtuberAccessRequests\x120.api.vtubers.v1.GetAllVtuberProfileAccessRequest\x1a#.api.vtubers.v1.VtuberAccessRequest\"\x06\x82\xb5\x18\x02\b\x01\x12\x8a\x01\n" +
	"\x19GetAllVtuberProfileAccess\x120.api.vtubers.v1.GetAllVtuberProfileAccessRequest\x1a1.api.vtubers.v1.GetAllVtuberProfileAccessResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12r\n" +
	"\x11DenyVtuberProfile\x12(.api.vtubers.v1.DenyVtuberProfileRequest\x1a).api.vtubers.v1.DenyVtuberProfileResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12\x88\x01\n" +
	"\x19UpdateVtuberAccessRequest\x120.api.vtubers.v1.UpdateVtuberProfileAccessRequest\x1a1.api.vtubers.v1.UpdateVtuberProfileAccessResponse\"\x06\x82\xb5\x18\x02\b\x01B4Z2github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1b\x06proto3"

var (
	file_vtubers_v1_vtuberprofiles_proto_rawDescOnce sync.Once
	file_vtubers_v1_vtuberprofiles_proto_rawDescData []byte
)

func file_vtubers_v1_vtuberprofiles_proto_rawDescGZIP() []byte {
	file_vtubers_v1_vtuberprofiles_proto_rawDescOnce.Do(func() {
		file_vtubers_v1_vtuberprofiles_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtuberprofiles_proto_rawDesc), len(file_vtubers_v1_vtuberprofiles_proto_rawDesc)))
	})
	return file_vtubers_v1_vtuberprofiles_proto_rawDescData
}

var file_vtubers_v1_vtuberprofiles_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_vtubers_v1_vtuberprofiles_proto_goTypes = []any{
	(*AddVtuberProfileRequest)(nil),           // 0: api.vtubers.v1.AddVtuberProfileRequest
	(*AddVtuberProfileResponse)(nil),          // 1: api.vtubers.v1.AddVtuberProfileResponse
	(*VtuberProfile)(nil),                     // 2: api.vtubers.v1.VtuberProfile
	(*GetVtuberProfileByIdRequest)(nil),       // 3: api.vtubers.v1.GetVtuberProfileByIdRequest
	(*GetVtuberProfileRequest)(nil),           // 4: api.vtubers.v1.GetVtuberProfileRequest
	(*GetVtuberProfileByIdResponse)(nil),      // 5: api.vtubers.v1.GetVtuberProfileByIdResponse
	(*UpdateVtuberProfileRequest)(nil),        // 6: api.vtubers.v1.UpdateVtuberProfileRequest
	(*GetAllVtuberProfilesRequest)(nil),       // 7: api.vtubers.v1.GetAllVtuberProfilesRequest
	(*GetAllVtuberProfilesResponse)(nil),      // 8: api.vtubers.v1.GetAllVtuberProfilesResponse
	(*DeleteVtuberProfileByIdRequest)(nil),    // 9: api.vtubers.v1.DeleteVtuberProfileByIdRequest
	(*VerifyVtuberProfileRequest)(nil),        // 10: api.vtubers.v1.VerifyVtuberProfileRequest
	(*CreateVtuberProfileAccessRequest)(nil),  // 11: api.vtubers.v1.CreateVtuberProfileAccessRequest
	(*UpdateVtuberProfileAccessRequest)(nil),  // 12: api.vtubers.v1.UpdateVtuberProfileAccessRequest
	(*UpdateVtuberProfileAccessResponse)(nil), // 13: api.vtubers.v1.UpdateVtuberProfileAccessResponse
	(*GetAllVtuberProfileAccessRequest)(nil),  // 14: api.vtubers.v1.GetAllVtuberProfileAccessRequest
	(*GetAllVtuberProfileAccessResponse)(nil), // 15: api.vtubers.v1.GetAllVtuberProfileAccessResponse
	(*VtuberAccessRequest)(nil),               // 16: api.vtubers.v1.VtuberAccessRequest
	(*DenyVtuberProfileRequest)(nil),          // 17: api.vtubers.v1.DenyVtuberProfileRequest
	(*UpdateVtuberProfileResponse)(nil),       // 18: api.vtubers.v1.UpdateVtuberProfileResponse
	(*VerifyVtuberProfileResponse)(nil),       // 19: api.vtubers.v1.VerifyVtuberProfileResponse
	(*CreateVtuberProfileAccessResponse)(nil), // 20: api.vtubers.v1.CreateVtuberProfileAccessResponse
	(*DenyVtuberProfileResponse)(nil),         // 21: api.vtubers.v1.DenyVtuberProfileResponse
	(*v1.SocialMediaLinks)(nil),               // 22: api.shared.v1.SocialMediaLinks
	(*timestamppb.Timestamp)(nil),             // 23: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),              // 24: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),              // 25: api.shared.v1.PaginationDetails
}
var file_vtubers_v1_vtuberprofiles_proto_depIdxs = []int32{
	22, // 0: api.vtubers.v1.AddVtuberProfileRequest.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	2,  // 1: api.vtubers.v1.AddVtuberProfileResponse.data:type_name -> api.vtubers.v1.VtuberProfile
	22, // 2: api.vtubers.v1.VtuberProfile.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	23, // 3: api.vtubers.v1.VtuberProfile.created_at:type_name -> google.protobuf.Timestamp
	2,  // 4: api.vtubers.v1.GetVtuberProfileByIdResponse.data:type_name -> api.vtubers.v1.VtuberProfile
	22, // 5: api.vtubers.v1.UpdateVtuberProfileRequest.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	24, // 6: api.vtubers.v1.GetAllVtuberProfilesRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 7: api.vtubers.v1.GetAllVtuberProfilesResponse.data:type_name -> api.vtubers.v1.VtuberProfile
	25, // 8: api.vtubers.v1.GetAllVtuberProfilesResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	24, // 9: api.vtubers.v1.GetAllVtuberProfileAccessRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	16, // 10: api.vtubers.v1.GetAllVtuberProfileAccessResponse.data:type_name -> api.vtubers.v1.VtuberAccessRequest
	25, // 11: api.vtubers.v1.GetAllVtuberProfileAccessResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	23, // 12: api.vtubers.v1.VtuberAccessRequest.created_at:type_name -> google.protobuf.Timestamp
	0,  // 13: api.vtubers.v1.VtuberProfilesService.AddVtuberProfile:input_type -> api.vtubers.v1.AddVtuberProfileRequest
	3,  // 14: api.vtubers.v1.VtuberProfilesService.GetVtuberProfileById:input_type -> api.vtubers.v1.GetVtuberProfileByIdRequest
	4,  // 15: api.vtubers.v1.VtuberProfilesService.GetVtuberProfile:input_type -> api.vtubers.v1.GetVtuberProfileRequest
	6,  // 16: api.vtubers.v1.VtuberProfilesService.UpdateVtuberProfile:input_type -> api.vtubers.v1.UpdateVtuberProfileRequest
	7,  // 17: api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfiles:input_type -> api.vtubers.v1.GetAllVtuberProfilesRequest
	10, // 18: api.vtubers.v1.VtuberProfilesService.VerifyVtuberProfile:input_type -> api.vtubers.v1.VerifyVtuberProfileRequest
	11, // 19: api.vtubers.v1.VtuberProfilesService.ApplyVtuberAccess:input_type -> api.vtubers.v1.CreateVtuberProfileAccessRequest
	14, // 20: api.vtubers.v1.VtuberProfilesService.GetMyVtuberAccessRequests:input_type -> api.vtubers.v1.GetAllVtuberProfileAccessRequest
	14, // 21: api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfileAccess:input_type -> api.vtubers.v1.GetAllVtuberProfileAccessRequest
	17, // 22: api.vtubers.v1.VtuberProfilesService.DenyVtuberProfile:input_type -> api.vtubers.v1.DenyVtuberProfileRequest
	12, // 23: api.vtubers.v1.VtuberProfilesService.UpdateVtuberAccessRequest:input_type -> api.vtubers.v1.UpdateVtuberProfileAccessRequest
	1,  // 24: api.vtubers.v1.VtuberProfilesService.AddVtuberProfile:output_type -> api.vtubers.v1.AddVtuberProfileResponse
	5,  // 25: api.vtubers.v1.VtuberProfilesService.GetVtuberProfileById:output_type -> api.vtubers.v1.GetVtuberProfileByIdResponse
	5,  // 26: api.vtubers.v1.VtuberProfilesService.GetVtuberProfile:output_type -> api.vtubers.v1.GetVtuberProfileByIdResponse
	18, // 27: api.vtubers.v1.VtuberProfilesService.UpdateVtuberProfile:output_type -> api.vtubers.v1.UpdateVtuberProfileResponse
	8,  // 28: api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfiles:output_type -> api.vtubers.v1.GetAllVtuberProfilesResponse
	19, // 29: api.vtubers.v1.VtuberProfilesService.VerifyVtuberProfile:output_type -> api.vtubers.v1.VerifyVtuberProfileResponse
	20, // 30: api.vtubers.v1.VtuberProfilesService.ApplyVtuberAccess:output_type -> api.vtubers.v1.CreateVtuberProfileAccessResponse
	16, // 31: api.vtubers.v1.VtuberProfilesService.GetMyVtuberAccessRequests:output_type -> api.vtubers.v1.VtuberAccessRequest
	15, // 32: api.vtubers.v1.VtuberProfilesService.GetAllVtuberProfileAccess:output_type -> api.vtubers.v1.GetAllVtuberProfileAccessResponse
	21, // 33: api.vtubers.v1.VtuberProfilesService.DenyVtuberProfile:output_type -> api.vtubers.v1.DenyVtuberProfileResponse
	13, // 34: api.vtubers.v1.VtuberProfilesService.UpdateVtuberAccessRequest:output_type -> api.vtubers.v1.UpdateVtuberProfileAccessResponse
	24, // [24:35] is the sub-list for method output_type
	13, // [13:24] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_vtubers_v1_vtuberprofiles_proto_init() }
func file_vtubers_v1_vtuberprofiles_proto_init() {
	if File_vtubers_v1_vtuberprofiles_proto != nil {
		return
	}
	file_vtubers_v1_vtuberprofiles_proto_msgTypes[0].OneofWrappers = []any{}
	file_vtubers_v1_vtuberprofiles_proto_msgTypes[2].OneofWrappers = []any{}
	file_vtubers_v1_vtuberprofiles_proto_msgTypes[6].OneofWrappers = []any{}
	file_vtubers_v1_vtuberprofiles_proto_msgTypes[7].OneofWrappers = []any{}
	file_vtubers_v1_vtuberprofiles_proto_msgTypes[14].OneofWrappers = []any{}
	file_vtubers_v1_vtuberprofiles_proto_msgTypes[16].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtuberprofiles_proto_rawDesc), len(file_vtubers_v1_vtuberprofiles_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vtubers_v1_vtuberprofiles_proto_goTypes,
		DependencyIndexes: file_vtubers_v1_vtuberprofiles_proto_depIdxs,
		MessageInfos:      file_vtubers_v1_vtuberprofiles_proto_msgTypes,
	}.Build()
	File_vtubers_v1_vtuberprofiles_proto = out.File
	file_vtubers_v1_vtuberprofiles_proto_goTypes = nil
	file_vtubers_v1_vtuberprofiles_proto_depIdxs = nil
}
