// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: vtubers/v1/vtubergallery.proto

package vtubersv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddVtuberGalleryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Media         string                 `protobuf:"bytes,1,opt,name=media,proto3" json:"media,omitempty" validate:"required"`
	MediaType     string                 `protobuf:"bytes,2,opt,name=media_type,json=mediaType,proto3" json:"media_type,omitempty" validate:"required,oneof=picture video"`
	Description   *string                `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberGalleryRequest) Reset() {
	*x = AddVtuberGalleryRequest{}
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberGalleryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberGalleryRequest) ProtoMessage() {}

func (x *AddVtuberGalleryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberGalleryRequest.ProtoReflect.Descriptor instead.
func (*AddVtuberGalleryRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubergallery_proto_rawDescGZIP(), []int{0}
}

func (x *AddVtuberGalleryRequest) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *AddVtuberGalleryRequest) GetMediaType() string {
	if x != nil {
		return x.MediaType
	}
	return ""
}

func (x *AddVtuberGalleryRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

type VtuberGallery struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	VtuberId      string                 `protobuf:"bytes,2,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	Media         string                 `protobuf:"bytes,3,opt,name=media,proto3" json:"media,omitempty"`
	MediaType     string                 `protobuf:"bytes,4,opt,name=media_type,json=mediaType,proto3" json:"media_type,omitempty"`
	Description   *string                `protobuf:"bytes,5,opt,name=description,proto3,oneof" json:"description,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VtuberGallery) Reset() {
	*x = VtuberGallery{}
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VtuberGallery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VtuberGallery) ProtoMessage() {}

func (x *VtuberGallery) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VtuberGallery.ProtoReflect.Descriptor instead.
func (*VtuberGallery) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubergallery_proto_rawDescGZIP(), []int{1}
}

func (x *VtuberGallery) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VtuberGallery) GetVtuberId() string {
	if x != nil {
		return x.VtuberId
	}
	return ""
}

func (x *VtuberGallery) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *VtuberGallery) GetMediaType() string {
	if x != nil {
		return x.MediaType
	}
	return ""
}

func (x *VtuberGallery) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *VtuberGallery) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VtuberGallery) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AddVtuberGalleryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *VtuberGallery         `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberGalleryResponse) Reset() {
	*x = AddVtuberGalleryResponse{}
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberGalleryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberGalleryResponse) ProtoMessage() {}

func (x *AddVtuberGalleryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberGalleryResponse.ProtoReflect.Descriptor instead.
func (*AddVtuberGalleryResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubergallery_proto_rawDescGZIP(), []int{2}
}

func (x *AddVtuberGalleryResponse) GetData() *VtuberGallery {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteVtuberGalleryByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVtuberGalleryByIdRequest) Reset() {
	*x = DeleteVtuberGalleryByIdRequest{}
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVtuberGalleryByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVtuberGalleryByIdRequest) ProtoMessage() {}

func (x *DeleteVtuberGalleryByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVtuberGalleryByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteVtuberGalleryByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubergallery_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteVtuberGalleryByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetVtuberGalleryByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberGalleryByIdRequest) Reset() {
	*x = GetVtuberGalleryByIdRequest{}
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberGalleryByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberGalleryByIdRequest) ProtoMessage() {}

func (x *GetVtuberGalleryByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberGalleryByIdRequest.ProtoReflect.Descriptor instead.
func (*GetVtuberGalleryByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubergallery_proto_rawDescGZIP(), []int{4}
}

func (x *GetVtuberGalleryByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetVtuberGalleryByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *VtuberGallery         `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberGalleryByIdResponse) Reset() {
	*x = GetVtuberGalleryByIdResponse{}
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberGalleryByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberGalleryByIdResponse) ProtoMessage() {}

func (x *GetVtuberGalleryByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberGalleryByIdResponse.ProtoReflect.Descriptor instead.
func (*GetVtuberGalleryByIdResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubergallery_proto_rawDescGZIP(), []int{5}
}

func (x *GetVtuberGalleryByIdResponse) GetData() *VtuberGallery {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetVtuberGalleriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VtuberId      string                 `protobuf:"bytes,1,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberGalleriesRequest) Reset() {
	*x = GetVtuberGalleriesRequest{}
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberGalleriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberGalleriesRequest) ProtoMessage() {}

func (x *GetVtuberGalleriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberGalleriesRequest.ProtoReflect.Descriptor instead.
func (*GetVtuberGalleriesRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubergallery_proto_rawDescGZIP(), []int{6}
}

func (x *GetVtuberGalleriesRequest) GetVtuberId() string {
	if x != nil {
		return x.VtuberId
	}
	return ""
}

func (x *GetVtuberGalleriesRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetVtuberGalleriesResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*VtuberGallery       `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetVtuberGalleriesResponse) Reset() {
	*x = GetVtuberGalleriesResponse{}
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberGalleriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberGalleriesResponse) ProtoMessage() {}

func (x *GetVtuberGalleriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberGalleriesResponse.ProtoReflect.Descriptor instead.
func (*GetVtuberGalleriesResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubergallery_proto_rawDescGZIP(), []int{7}
}

func (x *GetVtuberGalleriesResponse) GetData() []*VtuberGallery {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetVtuberGalleriesResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type UpdateVtuberGalleryByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Media         string                 `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty" validate:"required"`
	MediaType     string                 `protobuf:"bytes,3,opt,name=media_type,json=mediaType,proto3" json:"media_type,omitempty" validate:"required,oneof=picture video"`
	Description   *string                `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVtuberGalleryByIdRequest) Reset() {
	*x = UpdateVtuberGalleryByIdRequest{}
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberGalleryByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberGalleryByIdRequest) ProtoMessage() {}

func (x *UpdateVtuberGalleryByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubergallery_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberGalleryByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateVtuberGalleryByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubergallery_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateVtuberGalleryByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateVtuberGalleryByIdRequest) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *UpdateVtuberGalleryByIdRequest) GetMediaType() string {
	if x != nil {
		return x.MediaType
	}
	return ""
}

func (x *UpdateVtuberGalleryByIdRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

var File_vtubers_v1_vtubergallery_proto protoreflect.FileDescriptor

const file_vtubers_v1_vtubergallery_proto_rawDesc = "" +
	"\n" +
	"\x1evtubers/v1/vtubergallery.proto\x12\x0eapi.vtubers.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\x85\x01\n" +
	"\x17AddVtuberGalleryRequest\x12\x14\n" +
	"\x05media\x18\x01 \x01(\tR\x05media\x12\x1d\n" +
	"\n" +
	"media_type\x18\x02 \x01(\tR\tmediaType\x12%\n" +
	"\vdescription\x18\x03 \x01(\tH\x00R\vdescription\x88\x01\x01B\x0e\n" +
	"\f_description\"\x9e\x02\n" +
	"\rVtuberGallery\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1b\n" +
	"\tvtuber_id\x18\x02 \x01(\tR\bvtuberId\x12\x14\n" +
	"\x05media\x18\x03 \x01(\tR\x05media\x12\x1d\n" +
	"\n" +
	"media_type\x18\x04 \x01(\tR\tmediaType\x12%\n" +
	"\vdescription\x18\x05 \x01(\tH\x00R\vdescription\x88\x01\x01\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAtB\x0e\n" +
	"\f_description\"M\n" +
	"\x18AddVtuberGalleryResponse\x121\n" +
	"\x04data\x18\x01 \x01(\v2\x1d.api.vtubers.v1.VtuberGalleryR\x04data\"0\n" +
	"\x1eDeleteVtuberGalleryByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"-\n" +
	"\x1bGetVtuberGalleryByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"Q\n" +
	"\x1cGetVtuberGalleryByIdResponse\x121\n" +
	"\x04data\x18\x01 \x01(\v2\x1d.api.vtubers.v1.VtuberGalleryR\x04data\"\x8e\x01\n" +
	"\x19GetVtuberGalleriesRequest\x12\x1b\n" +
	"\tvtuber_id\x18\x01 \x01(\tR\bvtuberId\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\xa0\x01\n" +
	"\x1aGetVtuberGalleriesResponse\x121\n" +
	"\x04data\x18\x01 \x03(\v2\x1d.api.vtubers.v1.VtuberGalleryR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\x9c\x01\n" +
	"\x1eUpdateVtuberGalleryByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05media\x18\x02 \x01(\tR\x05media\x12\x1d\n" +
	"\n" +
	"media_type\x18\x03 \x01(\tR\tmediaType\x12%\n" +
	"\vdescription\x18\x04 \x01(\tH\x00R\vdescription\x88\x01\x01B\x0e\n" +
	"\f_description2\xd1\x04\n" +
	"\x14VtuberGalleryService\x12o\n" +
	"\x10AddVtuberGallery\x12'.api.vtubers.v1.AddVtuberGalleryRequest\x1a(.api.vtubers.v1.AddVtuberGalleryResponse\"\b\x82\xb5\x18\x04\b\x01\x18\x01\x12q\n" +
	"\x14GetVtuberGalleryById\x12+.api.vtubers.v1.GetVtuberGalleryByIdRequest\x1a,.api.vtubers.v1.GetVtuberGalleryByIdResponse\x12k\n" +
	"\x12GetVtuberGalleries\x12).api.vtubers.v1.GetVtuberGalleriesRequest\x1a*.api.vtubers.v1.GetVtuberGalleriesResponse\x12s\n" +
	"\x17DeleteVtuberGalleryById\x12..api.vtubers.v1.DeleteVtuberGalleryByIdRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x18\x01\x12s\n" +
	"\x17UpdateVtuberGalleryById\x12..api.vtubers.v1.UpdateVtuberGalleryByIdRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x18\x01B4Z2github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1b\x06proto3"

var (
	file_vtubers_v1_vtubergallery_proto_rawDescOnce sync.Once
	file_vtubers_v1_vtubergallery_proto_rawDescData []byte
)

func file_vtubers_v1_vtubergallery_proto_rawDescGZIP() []byte {
	file_vtubers_v1_vtubergallery_proto_rawDescOnce.Do(func() {
		file_vtubers_v1_vtubergallery_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtubergallery_proto_rawDesc), len(file_vtubers_v1_vtubergallery_proto_rawDesc)))
	})
	return file_vtubers_v1_vtubergallery_proto_rawDescData
}

var file_vtubers_v1_vtubergallery_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_vtubers_v1_vtubergallery_proto_goTypes = []any{
	(*AddVtuberGalleryRequest)(nil),        // 0: api.vtubers.v1.AddVtuberGalleryRequest
	(*VtuberGallery)(nil),                  // 1: api.vtubers.v1.VtuberGallery
	(*AddVtuberGalleryResponse)(nil),       // 2: api.vtubers.v1.AddVtuberGalleryResponse
	(*DeleteVtuberGalleryByIdRequest)(nil), // 3: api.vtubers.v1.DeleteVtuberGalleryByIdRequest
	(*GetVtuberGalleryByIdRequest)(nil),    // 4: api.vtubers.v1.GetVtuberGalleryByIdRequest
	(*GetVtuberGalleryByIdResponse)(nil),   // 5: api.vtubers.v1.GetVtuberGalleryByIdResponse
	(*GetVtuberGalleriesRequest)(nil),      // 6: api.vtubers.v1.GetVtuberGalleriesRequest
	(*GetVtuberGalleriesResponse)(nil),     // 7: api.vtubers.v1.GetVtuberGalleriesResponse
	(*UpdateVtuberGalleryByIdRequest)(nil), // 8: api.vtubers.v1.UpdateVtuberGalleryByIdRequest
	(*timestamppb.Timestamp)(nil),          // 9: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),           // 10: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),           // 11: api.shared.v1.PaginationDetails
	(*v1.GenericResponse)(nil),             // 12: api.shared.v1.GenericResponse
}
var file_vtubers_v1_vtubergallery_proto_depIdxs = []int32{
	9,  // 0: api.vtubers.v1.VtuberGallery.created_at:type_name -> google.protobuf.Timestamp
	9,  // 1: api.vtubers.v1.VtuberGallery.updated_at:type_name -> google.protobuf.Timestamp
	1,  // 2: api.vtubers.v1.AddVtuberGalleryResponse.data:type_name -> api.vtubers.v1.VtuberGallery
	1,  // 3: api.vtubers.v1.GetVtuberGalleryByIdResponse.data:type_name -> api.vtubers.v1.VtuberGallery
	10, // 4: api.vtubers.v1.GetVtuberGalleriesRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	1,  // 5: api.vtubers.v1.GetVtuberGalleriesResponse.data:type_name -> api.vtubers.v1.VtuberGallery
	11, // 6: api.vtubers.v1.GetVtuberGalleriesResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	0,  // 7: api.vtubers.v1.VtuberGalleryService.AddVtuberGallery:input_type -> api.vtubers.v1.AddVtuberGalleryRequest
	4,  // 8: api.vtubers.v1.VtuberGalleryService.GetVtuberGalleryById:input_type -> api.vtubers.v1.GetVtuberGalleryByIdRequest
	6,  // 9: api.vtubers.v1.VtuberGalleryService.GetVtuberGalleries:input_type -> api.vtubers.v1.GetVtuberGalleriesRequest
	3,  // 10: api.vtubers.v1.VtuberGalleryService.DeleteVtuberGalleryById:input_type -> api.vtubers.v1.DeleteVtuberGalleryByIdRequest
	8,  // 11: api.vtubers.v1.VtuberGalleryService.UpdateVtuberGalleryById:input_type -> api.vtubers.v1.UpdateVtuberGalleryByIdRequest
	2,  // 12: api.vtubers.v1.VtuberGalleryService.AddVtuberGallery:output_type -> api.vtubers.v1.AddVtuberGalleryResponse
	5,  // 13: api.vtubers.v1.VtuberGalleryService.GetVtuberGalleryById:output_type -> api.vtubers.v1.GetVtuberGalleryByIdResponse
	7,  // 14: api.vtubers.v1.VtuberGalleryService.GetVtuberGalleries:output_type -> api.vtubers.v1.GetVtuberGalleriesResponse
	12, // 15: api.vtubers.v1.VtuberGalleryService.DeleteVtuberGalleryById:output_type -> api.shared.v1.GenericResponse
	12, // 16: api.vtubers.v1.VtuberGalleryService.UpdateVtuberGalleryById:output_type -> api.shared.v1.GenericResponse
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_vtubers_v1_vtubergallery_proto_init() }
func file_vtubers_v1_vtubergallery_proto_init() {
	if File_vtubers_v1_vtubergallery_proto != nil {
		return
	}
	file_vtubers_v1_vtubergallery_proto_msgTypes[0].OneofWrappers = []any{}
	file_vtubers_v1_vtubergallery_proto_msgTypes[1].OneofWrappers = []any{}
	file_vtubers_v1_vtubergallery_proto_msgTypes[6].OneofWrappers = []any{}
	file_vtubers_v1_vtubergallery_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtubergallery_proto_rawDesc), len(file_vtubers_v1_vtubergallery_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vtubers_v1_vtubergallery_proto_goTypes,
		DependencyIndexes: file_vtubers_v1_vtubergallery_proto_depIdxs,
		MessageInfos:      file_vtubers_v1_vtubergallery_proto_msgTypes,
	}.Build()
	File_vtubers_v1_vtubergallery_proto = out.File
	file_vtubers_v1_vtubergallery_proto_goTypes = nil
	file_vtubers_v1_vtubergallery_proto_depIdxs = nil
}
