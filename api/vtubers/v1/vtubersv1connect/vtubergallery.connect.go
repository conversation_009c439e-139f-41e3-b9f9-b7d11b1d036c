// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: vtubers/v1/vtubergallery.proto

package vtubersv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// VtuberGalleryServiceName is the fully-qualified name of the VtuberGalleryService service.
	VtuberGalleryServiceName = "api.vtubers.v1.VtuberGalleryService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// VtuberGalleryServiceAddVtuberGalleryProcedure is the fully-qualified name of the
	// VtuberGalleryService's AddVtuberGallery RPC.
	VtuberGalleryServiceAddVtuberGalleryProcedure = "/api.vtubers.v1.VtuberGalleryService/AddVtuberGallery"
	// VtuberGalleryServiceGetVtuberGalleryByIdProcedure is the fully-qualified name of the
	// VtuberGalleryService's GetVtuberGalleryById RPC.
	VtuberGalleryServiceGetVtuberGalleryByIdProcedure = "/api.vtubers.v1.VtuberGalleryService/GetVtuberGalleryById"
	// VtuberGalleryServiceGetVtuberGalleriesProcedure is the fully-qualified name of the
	// VtuberGalleryService's GetVtuberGalleries RPC.
	VtuberGalleryServiceGetVtuberGalleriesProcedure = "/api.vtubers.v1.VtuberGalleryService/GetVtuberGalleries"
	// VtuberGalleryServiceDeleteVtuberGalleryByIdProcedure is the fully-qualified name of the
	// VtuberGalleryService's DeleteVtuberGalleryById RPC.
	VtuberGalleryServiceDeleteVtuberGalleryByIdProcedure = "/api.vtubers.v1.VtuberGalleryService/DeleteVtuberGalleryById"
	// VtuberGalleryServiceUpdateVtuberGalleryByIdProcedure is the fully-qualified name of the
	// VtuberGalleryService's UpdateVtuberGalleryById RPC.
	VtuberGalleryServiceUpdateVtuberGalleryByIdProcedure = "/api.vtubers.v1.VtuberGalleryService/UpdateVtuberGalleryById"
)

// VtuberGalleryServiceClient is a client for the api.vtubers.v1.VtuberGalleryService service.
type VtuberGalleryServiceClient interface {
	AddVtuberGallery(context.Context, *connect.Request[v1.AddVtuberGalleryRequest]) (*connect.Response[v1.AddVtuberGalleryResponse], error)
	GetVtuberGalleryById(context.Context, *connect.Request[v1.GetVtuberGalleryByIdRequest]) (*connect.Response[v1.GetVtuberGalleryByIdResponse], error)
	GetVtuberGalleries(context.Context, *connect.Request[v1.GetVtuberGalleriesRequest]) (*connect.Response[v1.GetVtuberGalleriesResponse], error)
	DeleteVtuberGalleryById(context.Context, *connect.Request[v1.DeleteVtuberGalleryByIdRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateVtuberGalleryById(context.Context, *connect.Request[v1.UpdateVtuberGalleryByIdRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewVtuberGalleryServiceClient constructs a client for the api.vtubers.v1.VtuberGalleryService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewVtuberGalleryServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) VtuberGalleryServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	vtuberGalleryServiceMethods := v1.File_vtubers_v1_vtubergallery_proto.Services().ByName("VtuberGalleryService").Methods()
	return &vtuberGalleryServiceClient{
		addVtuberGallery: connect.NewClient[v1.AddVtuberGalleryRequest, v1.AddVtuberGalleryResponse](
			httpClient,
			baseURL+VtuberGalleryServiceAddVtuberGalleryProcedure,
			connect.WithSchema(vtuberGalleryServiceMethods.ByName("AddVtuberGallery")),
			connect.WithClientOptions(opts...),
		),
		getVtuberGalleryById: connect.NewClient[v1.GetVtuberGalleryByIdRequest, v1.GetVtuberGalleryByIdResponse](
			httpClient,
			baseURL+VtuberGalleryServiceGetVtuberGalleryByIdProcedure,
			connect.WithSchema(vtuberGalleryServiceMethods.ByName("GetVtuberGalleryById")),
			connect.WithClientOptions(opts...),
		),
		getVtuberGalleries: connect.NewClient[v1.GetVtuberGalleriesRequest, v1.GetVtuberGalleriesResponse](
			httpClient,
			baseURL+VtuberGalleryServiceGetVtuberGalleriesProcedure,
			connect.WithSchema(vtuberGalleryServiceMethods.ByName("GetVtuberGalleries")),
			connect.WithClientOptions(opts...),
		),
		deleteVtuberGalleryById: connect.NewClient[v1.DeleteVtuberGalleryByIdRequest, v11.GenericResponse](
			httpClient,
			baseURL+VtuberGalleryServiceDeleteVtuberGalleryByIdProcedure,
			connect.WithSchema(vtuberGalleryServiceMethods.ByName("DeleteVtuberGalleryById")),
			connect.WithClientOptions(opts...),
		),
		updateVtuberGalleryById: connect.NewClient[v1.UpdateVtuberGalleryByIdRequest, v11.GenericResponse](
			httpClient,
			baseURL+VtuberGalleryServiceUpdateVtuberGalleryByIdProcedure,
			connect.WithSchema(vtuberGalleryServiceMethods.ByName("UpdateVtuberGalleryById")),
			connect.WithClientOptions(opts...),
		),
	}
}

// vtuberGalleryServiceClient implements VtuberGalleryServiceClient.
type vtuberGalleryServiceClient struct {
	addVtuberGallery        *connect.Client[v1.AddVtuberGalleryRequest, v1.AddVtuberGalleryResponse]
	getVtuberGalleryById    *connect.Client[v1.GetVtuberGalleryByIdRequest, v1.GetVtuberGalleryByIdResponse]
	getVtuberGalleries      *connect.Client[v1.GetVtuberGalleriesRequest, v1.GetVtuberGalleriesResponse]
	deleteVtuberGalleryById *connect.Client[v1.DeleteVtuberGalleryByIdRequest, v11.GenericResponse]
	updateVtuberGalleryById *connect.Client[v1.UpdateVtuberGalleryByIdRequest, v11.GenericResponse]
}

// AddVtuberGallery calls api.vtubers.v1.VtuberGalleryService.AddVtuberGallery.
func (c *vtuberGalleryServiceClient) AddVtuberGallery(ctx context.Context, req *connect.Request[v1.AddVtuberGalleryRequest]) (*connect.Response[v1.AddVtuberGalleryResponse], error) {
	return c.addVtuberGallery.CallUnary(ctx, req)
}

// GetVtuberGalleryById calls api.vtubers.v1.VtuberGalleryService.GetVtuberGalleryById.
func (c *vtuberGalleryServiceClient) GetVtuberGalleryById(ctx context.Context, req *connect.Request[v1.GetVtuberGalleryByIdRequest]) (*connect.Response[v1.GetVtuberGalleryByIdResponse], error) {
	return c.getVtuberGalleryById.CallUnary(ctx, req)
}

// GetVtuberGalleries calls api.vtubers.v1.VtuberGalleryService.GetVtuberGalleries.
func (c *vtuberGalleryServiceClient) GetVtuberGalleries(ctx context.Context, req *connect.Request[v1.GetVtuberGalleriesRequest]) (*connect.Response[v1.GetVtuberGalleriesResponse], error) {
	return c.getVtuberGalleries.CallUnary(ctx, req)
}

// DeleteVtuberGalleryById calls api.vtubers.v1.VtuberGalleryService.DeleteVtuberGalleryById.
func (c *vtuberGalleryServiceClient) DeleteVtuberGalleryById(ctx context.Context, req *connect.Request[v1.DeleteVtuberGalleryByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.deleteVtuberGalleryById.CallUnary(ctx, req)
}

// UpdateVtuberGalleryById calls api.vtubers.v1.VtuberGalleryService.UpdateVtuberGalleryById.
func (c *vtuberGalleryServiceClient) UpdateVtuberGalleryById(ctx context.Context, req *connect.Request[v1.UpdateVtuberGalleryByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.updateVtuberGalleryById.CallUnary(ctx, req)
}

// VtuberGalleryServiceHandler is an implementation of the api.vtubers.v1.VtuberGalleryService
// service.
type VtuberGalleryServiceHandler interface {
	AddVtuberGallery(context.Context, *connect.Request[v1.AddVtuberGalleryRequest]) (*connect.Response[v1.AddVtuberGalleryResponse], error)
	GetVtuberGalleryById(context.Context, *connect.Request[v1.GetVtuberGalleryByIdRequest]) (*connect.Response[v1.GetVtuberGalleryByIdResponse], error)
	GetVtuberGalleries(context.Context, *connect.Request[v1.GetVtuberGalleriesRequest]) (*connect.Response[v1.GetVtuberGalleriesResponse], error)
	DeleteVtuberGalleryById(context.Context, *connect.Request[v1.DeleteVtuberGalleryByIdRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateVtuberGalleryById(context.Context, *connect.Request[v1.UpdateVtuberGalleryByIdRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewVtuberGalleryServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewVtuberGalleryServiceHandler(svc VtuberGalleryServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	vtuberGalleryServiceMethods := v1.File_vtubers_v1_vtubergallery_proto.Services().ByName("VtuberGalleryService").Methods()
	vtuberGalleryServiceAddVtuberGalleryHandler := connect.NewUnaryHandler(
		VtuberGalleryServiceAddVtuberGalleryProcedure,
		svc.AddVtuberGallery,
		connect.WithSchema(vtuberGalleryServiceMethods.ByName("AddVtuberGallery")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberGalleryServiceGetVtuberGalleryByIdHandler := connect.NewUnaryHandler(
		VtuberGalleryServiceGetVtuberGalleryByIdProcedure,
		svc.GetVtuberGalleryById,
		connect.WithSchema(vtuberGalleryServiceMethods.ByName("GetVtuberGalleryById")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberGalleryServiceGetVtuberGalleriesHandler := connect.NewUnaryHandler(
		VtuberGalleryServiceGetVtuberGalleriesProcedure,
		svc.GetVtuberGalleries,
		connect.WithSchema(vtuberGalleryServiceMethods.ByName("GetVtuberGalleries")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberGalleryServiceDeleteVtuberGalleryByIdHandler := connect.NewUnaryHandler(
		VtuberGalleryServiceDeleteVtuberGalleryByIdProcedure,
		svc.DeleteVtuberGalleryById,
		connect.WithSchema(vtuberGalleryServiceMethods.ByName("DeleteVtuberGalleryById")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberGalleryServiceUpdateVtuberGalleryByIdHandler := connect.NewUnaryHandler(
		VtuberGalleryServiceUpdateVtuberGalleryByIdProcedure,
		svc.UpdateVtuberGalleryById,
		connect.WithSchema(vtuberGalleryServiceMethods.ByName("UpdateVtuberGalleryById")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.vtubers.v1.VtuberGalleryService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case VtuberGalleryServiceAddVtuberGalleryProcedure:
			vtuberGalleryServiceAddVtuberGalleryHandler.ServeHTTP(w, r)
		case VtuberGalleryServiceGetVtuberGalleryByIdProcedure:
			vtuberGalleryServiceGetVtuberGalleryByIdHandler.ServeHTTP(w, r)
		case VtuberGalleryServiceGetVtuberGalleriesProcedure:
			vtuberGalleryServiceGetVtuberGalleriesHandler.ServeHTTP(w, r)
		case VtuberGalleryServiceDeleteVtuberGalleryByIdProcedure:
			vtuberGalleryServiceDeleteVtuberGalleryByIdHandler.ServeHTTP(w, r)
		case VtuberGalleryServiceUpdateVtuberGalleryByIdProcedure:
			vtuberGalleryServiceUpdateVtuberGalleryByIdHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedVtuberGalleryServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedVtuberGalleryServiceHandler struct{}

func (UnimplementedVtuberGalleryServiceHandler) AddVtuberGallery(context.Context, *connect.Request[v1.AddVtuberGalleryRequest]) (*connect.Response[v1.AddVtuberGalleryResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberGalleryService.AddVtuberGallery is not implemented"))
}

func (UnimplementedVtuberGalleryServiceHandler) GetVtuberGalleryById(context.Context, *connect.Request[v1.GetVtuberGalleryByIdRequest]) (*connect.Response[v1.GetVtuberGalleryByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberGalleryService.GetVtuberGalleryById is not implemented"))
}

func (UnimplementedVtuberGalleryServiceHandler) GetVtuberGalleries(context.Context, *connect.Request[v1.GetVtuberGalleriesRequest]) (*connect.Response[v1.GetVtuberGalleriesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberGalleryService.GetVtuberGalleries is not implemented"))
}

func (UnimplementedVtuberGalleryServiceHandler) DeleteVtuberGalleryById(context.Context, *connect.Request[v1.DeleteVtuberGalleryByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberGalleryService.DeleteVtuberGalleryById is not implemented"))
}

func (UnimplementedVtuberGalleryServiceHandler) UpdateVtuberGalleryById(context.Context, *connect.Request[v1.UpdateVtuberGalleryByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberGalleryService.UpdateVtuberGalleryById is not implemented"))
}
