// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: vtubers/v1/vtuberbanner.proto

package vtubersv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// VtuberBannerServiceName is the fully-qualified name of the VtuberBannerService service.
	VtuberBannerServiceName = "api.vtubers.v1.VtuberBannerService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// VtuberBannerServiceAddVtuberBannerProcedure is the fully-qualified name of the
	// VtuberBannerService's AddVtuberBanner RPC.
	VtuberBannerServiceAddVtuberBannerProcedure = "/api.vtubers.v1.VtuberBannerService/AddVtuberBanner"
	// VtuberBannerServiceGetVtuberBannerByVtuberIdProcedure is the fully-qualified name of the
	// VtuberBannerService's GetVtuberBannerByVtuberId RPC.
	VtuberBannerServiceGetVtuberBannerByVtuberIdProcedure = "/api.vtubers.v1.VtuberBannerService/GetVtuberBannerByVtuberId"
	// VtuberBannerServiceDeleteVtuberBannerByIdProcedure is the fully-qualified name of the
	// VtuberBannerService's DeleteVtuberBannerById RPC.
	VtuberBannerServiceDeleteVtuberBannerByIdProcedure = "/api.vtubers.v1.VtuberBannerService/DeleteVtuberBannerById"
	// VtuberBannerServiceUpdateVtuberBannerByIdProcedure is the fully-qualified name of the
	// VtuberBannerService's UpdateVtuberBannerById RPC.
	VtuberBannerServiceUpdateVtuberBannerByIdProcedure = "/api.vtubers.v1.VtuberBannerService/UpdateVtuberBannerById"
)

// VtuberBannerServiceClient is a client for the api.vtubers.v1.VtuberBannerService service.
type VtuberBannerServiceClient interface {
	AddVtuberBanner(context.Context, *connect.Request[v1.AddVtuberBannerRequest]) (*connect.Response[v1.AddVtuberBannerResponse], error)
	GetVtuberBannerByVtuberId(context.Context, *connect.Request[v1.GetVtuberBannerByVtuberIdRequest]) (*connect.Response[v1.GetVtuberBannerByVtuberIdResponse], error)
	DeleteVtuberBannerById(context.Context, *connect.Request[v1.DeleteVtuberBannerByIdRequest]) (*connect.Response[v1.DeleteVtuberBannerByIdResponse], error)
	UpdateVtuberBannerById(context.Context, *connect.Request[v1.UpdateVtuberBannerByIdRequest]) (*connect.Response[v1.UpdateVtuberBannerByIdResponse], error)
}

// NewVtuberBannerServiceClient constructs a client for the api.vtubers.v1.VtuberBannerService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewVtuberBannerServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) VtuberBannerServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	vtuberBannerServiceMethods := v1.File_vtubers_v1_vtuberbanner_proto.Services().ByName("VtuberBannerService").Methods()
	return &vtuberBannerServiceClient{
		addVtuberBanner: connect.NewClient[v1.AddVtuberBannerRequest, v1.AddVtuberBannerResponse](
			httpClient,
			baseURL+VtuberBannerServiceAddVtuberBannerProcedure,
			connect.WithSchema(vtuberBannerServiceMethods.ByName("AddVtuberBanner")),
			connect.WithClientOptions(opts...),
		),
		getVtuberBannerByVtuberId: connect.NewClient[v1.GetVtuberBannerByVtuberIdRequest, v1.GetVtuberBannerByVtuberIdResponse](
			httpClient,
			baseURL+VtuberBannerServiceGetVtuberBannerByVtuberIdProcedure,
			connect.WithSchema(vtuberBannerServiceMethods.ByName("GetVtuberBannerByVtuberId")),
			connect.WithClientOptions(opts...),
		),
		deleteVtuberBannerById: connect.NewClient[v1.DeleteVtuberBannerByIdRequest, v1.DeleteVtuberBannerByIdResponse](
			httpClient,
			baseURL+VtuberBannerServiceDeleteVtuberBannerByIdProcedure,
			connect.WithSchema(vtuberBannerServiceMethods.ByName("DeleteVtuberBannerById")),
			connect.WithClientOptions(opts...),
		),
		updateVtuberBannerById: connect.NewClient[v1.UpdateVtuberBannerByIdRequest, v1.UpdateVtuberBannerByIdResponse](
			httpClient,
			baseURL+VtuberBannerServiceUpdateVtuberBannerByIdProcedure,
			connect.WithSchema(vtuberBannerServiceMethods.ByName("UpdateVtuberBannerById")),
			connect.WithClientOptions(opts...),
		),
	}
}

// vtuberBannerServiceClient implements VtuberBannerServiceClient.
type vtuberBannerServiceClient struct {
	addVtuberBanner           *connect.Client[v1.AddVtuberBannerRequest, v1.AddVtuberBannerResponse]
	getVtuberBannerByVtuberId *connect.Client[v1.GetVtuberBannerByVtuberIdRequest, v1.GetVtuberBannerByVtuberIdResponse]
	deleteVtuberBannerById    *connect.Client[v1.DeleteVtuberBannerByIdRequest, v1.DeleteVtuberBannerByIdResponse]
	updateVtuberBannerById    *connect.Client[v1.UpdateVtuberBannerByIdRequest, v1.UpdateVtuberBannerByIdResponse]
}

// AddVtuberBanner calls api.vtubers.v1.VtuberBannerService.AddVtuberBanner.
func (c *vtuberBannerServiceClient) AddVtuberBanner(ctx context.Context, req *connect.Request[v1.AddVtuberBannerRequest]) (*connect.Response[v1.AddVtuberBannerResponse], error) {
	return c.addVtuberBanner.CallUnary(ctx, req)
}

// GetVtuberBannerByVtuberId calls api.vtubers.v1.VtuberBannerService.GetVtuberBannerByVtuberId.
func (c *vtuberBannerServiceClient) GetVtuberBannerByVtuberId(ctx context.Context, req *connect.Request[v1.GetVtuberBannerByVtuberIdRequest]) (*connect.Response[v1.GetVtuberBannerByVtuberIdResponse], error) {
	return c.getVtuberBannerByVtuberId.CallUnary(ctx, req)
}

// DeleteVtuberBannerById calls api.vtubers.v1.VtuberBannerService.DeleteVtuberBannerById.
func (c *vtuberBannerServiceClient) DeleteVtuberBannerById(ctx context.Context, req *connect.Request[v1.DeleteVtuberBannerByIdRequest]) (*connect.Response[v1.DeleteVtuberBannerByIdResponse], error) {
	return c.deleteVtuberBannerById.CallUnary(ctx, req)
}

// UpdateVtuberBannerById calls api.vtubers.v1.VtuberBannerService.UpdateVtuberBannerById.
func (c *vtuberBannerServiceClient) UpdateVtuberBannerById(ctx context.Context, req *connect.Request[v1.UpdateVtuberBannerByIdRequest]) (*connect.Response[v1.UpdateVtuberBannerByIdResponse], error) {
	return c.updateVtuberBannerById.CallUnary(ctx, req)
}

// VtuberBannerServiceHandler is an implementation of the api.vtubers.v1.VtuberBannerService
// service.
type VtuberBannerServiceHandler interface {
	AddVtuberBanner(context.Context, *connect.Request[v1.AddVtuberBannerRequest]) (*connect.Response[v1.AddVtuberBannerResponse], error)
	GetVtuberBannerByVtuberId(context.Context, *connect.Request[v1.GetVtuberBannerByVtuberIdRequest]) (*connect.Response[v1.GetVtuberBannerByVtuberIdResponse], error)
	DeleteVtuberBannerById(context.Context, *connect.Request[v1.DeleteVtuberBannerByIdRequest]) (*connect.Response[v1.DeleteVtuberBannerByIdResponse], error)
	UpdateVtuberBannerById(context.Context, *connect.Request[v1.UpdateVtuberBannerByIdRequest]) (*connect.Response[v1.UpdateVtuberBannerByIdResponse], error)
}

// NewVtuberBannerServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewVtuberBannerServiceHandler(svc VtuberBannerServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	vtuberBannerServiceMethods := v1.File_vtubers_v1_vtuberbanner_proto.Services().ByName("VtuberBannerService").Methods()
	vtuberBannerServiceAddVtuberBannerHandler := connect.NewUnaryHandler(
		VtuberBannerServiceAddVtuberBannerProcedure,
		svc.AddVtuberBanner,
		connect.WithSchema(vtuberBannerServiceMethods.ByName("AddVtuberBanner")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberBannerServiceGetVtuberBannerByVtuberIdHandler := connect.NewUnaryHandler(
		VtuberBannerServiceGetVtuberBannerByVtuberIdProcedure,
		svc.GetVtuberBannerByVtuberId,
		connect.WithSchema(vtuberBannerServiceMethods.ByName("GetVtuberBannerByVtuberId")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberBannerServiceDeleteVtuberBannerByIdHandler := connect.NewUnaryHandler(
		VtuberBannerServiceDeleteVtuberBannerByIdProcedure,
		svc.DeleteVtuberBannerById,
		connect.WithSchema(vtuberBannerServiceMethods.ByName("DeleteVtuberBannerById")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberBannerServiceUpdateVtuberBannerByIdHandler := connect.NewUnaryHandler(
		VtuberBannerServiceUpdateVtuberBannerByIdProcedure,
		svc.UpdateVtuberBannerById,
		connect.WithSchema(vtuberBannerServiceMethods.ByName("UpdateVtuberBannerById")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.vtubers.v1.VtuberBannerService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case VtuberBannerServiceAddVtuberBannerProcedure:
			vtuberBannerServiceAddVtuberBannerHandler.ServeHTTP(w, r)
		case VtuberBannerServiceGetVtuberBannerByVtuberIdProcedure:
			vtuberBannerServiceGetVtuberBannerByVtuberIdHandler.ServeHTTP(w, r)
		case VtuberBannerServiceDeleteVtuberBannerByIdProcedure:
			vtuberBannerServiceDeleteVtuberBannerByIdHandler.ServeHTTP(w, r)
		case VtuberBannerServiceUpdateVtuberBannerByIdProcedure:
			vtuberBannerServiceUpdateVtuberBannerByIdHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedVtuberBannerServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedVtuberBannerServiceHandler struct{}

func (UnimplementedVtuberBannerServiceHandler) AddVtuberBanner(context.Context, *connect.Request[v1.AddVtuberBannerRequest]) (*connect.Response[v1.AddVtuberBannerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberBannerService.AddVtuberBanner is not implemented"))
}

func (UnimplementedVtuberBannerServiceHandler) GetVtuberBannerByVtuberId(context.Context, *connect.Request[v1.GetVtuberBannerByVtuberIdRequest]) (*connect.Response[v1.GetVtuberBannerByVtuberIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberBannerService.GetVtuberBannerByVtuberId is not implemented"))
}

func (UnimplementedVtuberBannerServiceHandler) DeleteVtuberBannerById(context.Context, *connect.Request[v1.DeleteVtuberBannerByIdRequest]) (*connect.Response[v1.DeleteVtuberBannerByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberBannerService.DeleteVtuberBannerById is not implemented"))
}

func (UnimplementedVtuberBannerServiceHandler) UpdateVtuberBannerById(context.Context, *connect.Request[v1.UpdateVtuberBannerByIdRequest]) (*connect.Response[v1.UpdateVtuberBannerByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberBannerService.UpdateVtuberBannerById is not implemented"))
}
