// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: vtubers/v1/vtuberplan.proto

package vtubersv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// VtuberPlanServiceName is the fully-qualified name of the VtuberPlanService service.
	VtuberPlanServiceName = "api.vtubers.v1.VtuberPlanService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// VtuberPlanServiceAddVtuberPlanProcedure is the fully-qualified name of the VtuberPlanService's
	// AddVtuberPlan RPC.
	VtuberPlanServiceAddVtuberPlanProcedure = "/api.vtubers.v1.VtuberPlanService/AddVtuberPlan"
	// VtuberPlanServiceGetAllVtuberPlansByVtuberIdProcedure is the fully-qualified name of the
	// VtuberPlanService's GetAllVtuberPlansByVtuberId RPC.
	VtuberPlanServiceGetAllVtuberPlansByVtuberIdProcedure = "/api.vtubers.v1.VtuberPlanService/GetAllVtuberPlansByVtuberId"
	// VtuberPlanServiceGetVtuberPlanByIdProcedure is the fully-qualified name of the
	// VtuberPlanService's GetVtuberPlanById RPC.
	VtuberPlanServiceGetVtuberPlanByIdProcedure = "/api.vtubers.v1.VtuberPlanService/GetVtuberPlanById"
	// VtuberPlanServiceDeleteVtuberPlanByIdProcedure is the fully-qualified name of the
	// VtuberPlanService's DeleteVtuberPlanById RPC.
	VtuberPlanServiceDeleteVtuberPlanByIdProcedure = "/api.vtubers.v1.VtuberPlanService/DeleteVtuberPlanById"
	// VtuberPlanServiceUpdateVtuberPlanByIdProcedure is the fully-qualified name of the
	// VtuberPlanService's UpdateVtuberPlanById RPC.
	VtuberPlanServiceUpdateVtuberPlanByIdProcedure = "/api.vtubers.v1.VtuberPlanService/UpdateVtuberPlanById"
)

// VtuberPlanServiceClient is a client for the api.vtubers.v1.VtuberPlanService service.
type VtuberPlanServiceClient interface {
	AddVtuberPlan(context.Context, *connect.Request[v1.AddVtuberPlanRequest]) (*connect.Response[v1.AddVtuberPlanResponse], error)
	GetAllVtuberPlansByVtuberId(context.Context, *connect.Request[v1.GetAllVtuberPlansByVtuberIdRequest]) (*connect.Response[v1.GetAllVtuberPlansResponse], error)
	GetVtuberPlanById(context.Context, *connect.Request[v1.GetVtuberPlanByIdRequest]) (*connect.Response[v1.GetVtuberPlanByIdResponse], error)
	DeleteVtuberPlanById(context.Context, *connect.Request[v1.DeleteVtuberPlanByIdRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateVtuberPlanById(context.Context, *connect.Request[v1.UpdateVtuberPlanByIdRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewVtuberPlanServiceClient constructs a client for the api.vtubers.v1.VtuberPlanService service.
// By default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped
// responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewVtuberPlanServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) VtuberPlanServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	vtuberPlanServiceMethods := v1.File_vtubers_v1_vtuberplan_proto.Services().ByName("VtuberPlanService").Methods()
	return &vtuberPlanServiceClient{
		addVtuberPlan: connect.NewClient[v1.AddVtuberPlanRequest, v1.AddVtuberPlanResponse](
			httpClient,
			baseURL+VtuberPlanServiceAddVtuberPlanProcedure,
			connect.WithSchema(vtuberPlanServiceMethods.ByName("AddVtuberPlan")),
			connect.WithClientOptions(opts...),
		),
		getAllVtuberPlansByVtuberId: connect.NewClient[v1.GetAllVtuberPlansByVtuberIdRequest, v1.GetAllVtuberPlansResponse](
			httpClient,
			baseURL+VtuberPlanServiceGetAllVtuberPlansByVtuberIdProcedure,
			connect.WithSchema(vtuberPlanServiceMethods.ByName("GetAllVtuberPlansByVtuberId")),
			connect.WithClientOptions(opts...),
		),
		getVtuberPlanById: connect.NewClient[v1.GetVtuberPlanByIdRequest, v1.GetVtuberPlanByIdResponse](
			httpClient,
			baseURL+VtuberPlanServiceGetVtuberPlanByIdProcedure,
			connect.WithSchema(vtuberPlanServiceMethods.ByName("GetVtuberPlanById")),
			connect.WithClientOptions(opts...),
		),
		deleteVtuberPlanById: connect.NewClient[v1.DeleteVtuberPlanByIdRequest, v11.GenericResponse](
			httpClient,
			baseURL+VtuberPlanServiceDeleteVtuberPlanByIdProcedure,
			connect.WithSchema(vtuberPlanServiceMethods.ByName("DeleteVtuberPlanById")),
			connect.WithClientOptions(opts...),
		),
		updateVtuberPlanById: connect.NewClient[v1.UpdateVtuberPlanByIdRequest, v11.GenericResponse](
			httpClient,
			baseURL+VtuberPlanServiceUpdateVtuberPlanByIdProcedure,
			connect.WithSchema(vtuberPlanServiceMethods.ByName("UpdateVtuberPlanById")),
			connect.WithClientOptions(opts...),
		),
	}
}

// vtuberPlanServiceClient implements VtuberPlanServiceClient.
type vtuberPlanServiceClient struct {
	addVtuberPlan               *connect.Client[v1.AddVtuberPlanRequest, v1.AddVtuberPlanResponse]
	getAllVtuberPlansByVtuberId *connect.Client[v1.GetAllVtuberPlansByVtuberIdRequest, v1.GetAllVtuberPlansResponse]
	getVtuberPlanById           *connect.Client[v1.GetVtuberPlanByIdRequest, v1.GetVtuberPlanByIdResponse]
	deleteVtuberPlanById        *connect.Client[v1.DeleteVtuberPlanByIdRequest, v11.GenericResponse]
	updateVtuberPlanById        *connect.Client[v1.UpdateVtuberPlanByIdRequest, v11.GenericResponse]
}

// AddVtuberPlan calls api.vtubers.v1.VtuberPlanService.AddVtuberPlan.
func (c *vtuberPlanServiceClient) AddVtuberPlan(ctx context.Context, req *connect.Request[v1.AddVtuberPlanRequest]) (*connect.Response[v1.AddVtuberPlanResponse], error) {
	return c.addVtuberPlan.CallUnary(ctx, req)
}

// GetAllVtuberPlansByVtuberId calls api.vtubers.v1.VtuberPlanService.GetAllVtuberPlansByVtuberId.
func (c *vtuberPlanServiceClient) GetAllVtuberPlansByVtuberId(ctx context.Context, req *connect.Request[v1.GetAllVtuberPlansByVtuberIdRequest]) (*connect.Response[v1.GetAllVtuberPlansResponse], error) {
	return c.getAllVtuberPlansByVtuberId.CallUnary(ctx, req)
}

// GetVtuberPlanById calls api.vtubers.v1.VtuberPlanService.GetVtuberPlanById.
func (c *vtuberPlanServiceClient) GetVtuberPlanById(ctx context.Context, req *connect.Request[v1.GetVtuberPlanByIdRequest]) (*connect.Response[v1.GetVtuberPlanByIdResponse], error) {
	return c.getVtuberPlanById.CallUnary(ctx, req)
}

// DeleteVtuberPlanById calls api.vtubers.v1.VtuberPlanService.DeleteVtuberPlanById.
func (c *vtuberPlanServiceClient) DeleteVtuberPlanById(ctx context.Context, req *connect.Request[v1.DeleteVtuberPlanByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.deleteVtuberPlanById.CallUnary(ctx, req)
}

// UpdateVtuberPlanById calls api.vtubers.v1.VtuberPlanService.UpdateVtuberPlanById.
func (c *vtuberPlanServiceClient) UpdateVtuberPlanById(ctx context.Context, req *connect.Request[v1.UpdateVtuberPlanByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.updateVtuberPlanById.CallUnary(ctx, req)
}

// VtuberPlanServiceHandler is an implementation of the api.vtubers.v1.VtuberPlanService service.
type VtuberPlanServiceHandler interface {
	AddVtuberPlan(context.Context, *connect.Request[v1.AddVtuberPlanRequest]) (*connect.Response[v1.AddVtuberPlanResponse], error)
	GetAllVtuberPlansByVtuberId(context.Context, *connect.Request[v1.GetAllVtuberPlansByVtuberIdRequest]) (*connect.Response[v1.GetAllVtuberPlansResponse], error)
	GetVtuberPlanById(context.Context, *connect.Request[v1.GetVtuberPlanByIdRequest]) (*connect.Response[v1.GetVtuberPlanByIdResponse], error)
	DeleteVtuberPlanById(context.Context, *connect.Request[v1.DeleteVtuberPlanByIdRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateVtuberPlanById(context.Context, *connect.Request[v1.UpdateVtuberPlanByIdRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewVtuberPlanServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewVtuberPlanServiceHandler(svc VtuberPlanServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	vtuberPlanServiceMethods := v1.File_vtubers_v1_vtuberplan_proto.Services().ByName("VtuberPlanService").Methods()
	vtuberPlanServiceAddVtuberPlanHandler := connect.NewUnaryHandler(
		VtuberPlanServiceAddVtuberPlanProcedure,
		svc.AddVtuberPlan,
		connect.WithSchema(vtuberPlanServiceMethods.ByName("AddVtuberPlan")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberPlanServiceGetAllVtuberPlansByVtuberIdHandler := connect.NewUnaryHandler(
		VtuberPlanServiceGetAllVtuberPlansByVtuberIdProcedure,
		svc.GetAllVtuberPlansByVtuberId,
		connect.WithSchema(vtuberPlanServiceMethods.ByName("GetAllVtuberPlansByVtuberId")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberPlanServiceGetVtuberPlanByIdHandler := connect.NewUnaryHandler(
		VtuberPlanServiceGetVtuberPlanByIdProcedure,
		svc.GetVtuberPlanById,
		connect.WithSchema(vtuberPlanServiceMethods.ByName("GetVtuberPlanById")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberPlanServiceDeleteVtuberPlanByIdHandler := connect.NewUnaryHandler(
		VtuberPlanServiceDeleteVtuberPlanByIdProcedure,
		svc.DeleteVtuberPlanById,
		connect.WithSchema(vtuberPlanServiceMethods.ByName("DeleteVtuberPlanById")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberPlanServiceUpdateVtuberPlanByIdHandler := connect.NewUnaryHandler(
		VtuberPlanServiceUpdateVtuberPlanByIdProcedure,
		svc.UpdateVtuberPlanById,
		connect.WithSchema(vtuberPlanServiceMethods.ByName("UpdateVtuberPlanById")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.vtubers.v1.VtuberPlanService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case VtuberPlanServiceAddVtuberPlanProcedure:
			vtuberPlanServiceAddVtuberPlanHandler.ServeHTTP(w, r)
		case VtuberPlanServiceGetAllVtuberPlansByVtuberIdProcedure:
			vtuberPlanServiceGetAllVtuberPlansByVtuberIdHandler.ServeHTTP(w, r)
		case VtuberPlanServiceGetVtuberPlanByIdProcedure:
			vtuberPlanServiceGetVtuberPlanByIdHandler.ServeHTTP(w, r)
		case VtuberPlanServiceDeleteVtuberPlanByIdProcedure:
			vtuberPlanServiceDeleteVtuberPlanByIdHandler.ServeHTTP(w, r)
		case VtuberPlanServiceUpdateVtuberPlanByIdProcedure:
			vtuberPlanServiceUpdateVtuberPlanByIdHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedVtuberPlanServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedVtuberPlanServiceHandler struct{}

func (UnimplementedVtuberPlanServiceHandler) AddVtuberPlan(context.Context, *connect.Request[v1.AddVtuberPlanRequest]) (*connect.Response[v1.AddVtuberPlanResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberPlanService.AddVtuberPlan is not implemented"))
}

func (UnimplementedVtuberPlanServiceHandler) GetAllVtuberPlansByVtuberId(context.Context, *connect.Request[v1.GetAllVtuberPlansByVtuberIdRequest]) (*connect.Response[v1.GetAllVtuberPlansResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberPlanService.GetAllVtuberPlansByVtuberId is not implemented"))
}

func (UnimplementedVtuberPlanServiceHandler) GetVtuberPlanById(context.Context, *connect.Request[v1.GetVtuberPlanByIdRequest]) (*connect.Response[v1.GetVtuberPlanByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberPlanService.GetVtuberPlanById is not implemented"))
}

func (UnimplementedVtuberPlanServiceHandler) DeleteVtuberPlanById(context.Context, *connect.Request[v1.DeleteVtuberPlanByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberPlanService.DeleteVtuberPlanById is not implemented"))
}

func (UnimplementedVtuberPlanServiceHandler) UpdateVtuberPlanById(context.Context, *connect.Request[v1.UpdateVtuberPlanByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberPlanService.UpdateVtuberPlanById is not implemented"))
}
