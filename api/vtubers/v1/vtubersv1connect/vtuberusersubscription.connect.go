// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: vtubers/v1/vtuberusersubscription.proto

package vtubersv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// VtuberUserSubscriptionServiceName is the fully-qualified name of the
	// VtuberUserSubscriptionService service.
	VtuberUserSubscriptionServiceName = "api.vtubers.v1.VtuberUserSubscriptionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// VtuberUserSubscriptionServiceAddVtuberUserSubscriptionProcedure is the fully-qualified name of
	// the VtuberUserSubscriptionService's AddVtuberUserSubscription RPC.
	VtuberUserSubscriptionServiceAddVtuberUserSubscriptionProcedure = "/api.vtubers.v1.VtuberUserSubscriptionService/AddVtuberUserSubscription"
	// VtuberUserSubscriptionServiceGetAllVtuberUserSubscriptionsProcedure is the fully-qualified name
	// of the VtuberUserSubscriptionService's GetAllVtuberUserSubscriptions RPC.
	VtuberUserSubscriptionServiceGetAllVtuberUserSubscriptionsProcedure = "/api.vtubers.v1.VtuberUserSubscriptionService/GetAllVtuberUserSubscriptions"
	// VtuberUserSubscriptionServiceGetVtuberUserSubscriptionByIdProcedure is the fully-qualified name
	// of the VtuberUserSubscriptionService's GetVtuberUserSubscriptionById RPC.
	VtuberUserSubscriptionServiceGetVtuberUserSubscriptionByIdProcedure = "/api.vtubers.v1.VtuberUserSubscriptionService/GetVtuberUserSubscriptionById"
	// VtuberUserSubscriptionServiceDeleteVtuberUserSubscriptionByIdProcedure is the fully-qualified
	// name of the VtuberUserSubscriptionService's DeleteVtuberUserSubscriptionById RPC.
	VtuberUserSubscriptionServiceDeleteVtuberUserSubscriptionByIdProcedure = "/api.vtubers.v1.VtuberUserSubscriptionService/DeleteVtuberUserSubscriptionById"
	// VtuberUserSubscriptionServiceUpdateVtuberUserSubscriptionByIdProcedure is the fully-qualified
	// name of the VtuberUserSubscriptionService's UpdateVtuberUserSubscriptionById RPC.
	VtuberUserSubscriptionServiceUpdateVtuberUserSubscriptionByIdProcedure = "/api.vtubers.v1.VtuberUserSubscriptionService/UpdateVtuberUserSubscriptionById"
	// VtuberUserSubscriptionServiceGetMyVtuberUserSubscriptionsProcedure is the fully-qualified name of
	// the VtuberUserSubscriptionService's GetMyVtuberUserSubscriptions RPC.
	VtuberUserSubscriptionServiceGetMyVtuberUserSubscriptionsProcedure = "/api.vtubers.v1.VtuberUserSubscriptionService/GetMyVtuberUserSubscriptions"
)

// VtuberUserSubscriptionServiceClient is a client for the
// api.vtubers.v1.VtuberUserSubscriptionService service.
type VtuberUserSubscriptionServiceClient interface {
	AddVtuberUserSubscription(context.Context, *connect.Request[v1.AddVtuberUserSubscriptionRequest]) (*connect.Response[v1.AddVtuberUserSubscriptionResponse], error)
	GetAllVtuberUserSubscriptions(context.Context, *connect.Request[v1.GetAllVtuberUserSubscriptionsRequest]) (*connect.Response[v1.GetAllVtuberUserSubscriptionsResponse], error)
	GetVtuberUserSubscriptionById(context.Context, *connect.Request[v1.GetVtuberUserSubscriptionByIdRequest]) (*connect.Response[v1.VtuberUserSubscription], error)
	DeleteVtuberUserSubscriptionById(context.Context, *connect.Request[v1.DeleteVtuberUserSubscriptionByIdRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateVtuberUserSubscriptionById(context.Context, *connect.Request[v1.UpdateVtuberUserSubscriptionRequest]) (*connect.Response[v11.GenericResponse], error)
	GetMyVtuberUserSubscriptions(context.Context, *connect.Request[v1.GetMyVtuberUserSubscriptionsRequest]) (*connect.Response[v1.GetAllVtuberUserSubscriptionsResponse], error)
}

// NewVtuberUserSubscriptionServiceClient constructs a client for the
// api.vtubers.v1.VtuberUserSubscriptionService service. By default, it uses the Connect protocol
// with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To
// use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb()
// options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewVtuberUserSubscriptionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) VtuberUserSubscriptionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	vtuberUserSubscriptionServiceMethods := v1.File_vtubers_v1_vtuberusersubscription_proto.Services().ByName("VtuberUserSubscriptionService").Methods()
	return &vtuberUserSubscriptionServiceClient{
		addVtuberUserSubscription: connect.NewClient[v1.AddVtuberUserSubscriptionRequest, v1.AddVtuberUserSubscriptionResponse](
			httpClient,
			baseURL+VtuberUserSubscriptionServiceAddVtuberUserSubscriptionProcedure,
			connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("AddVtuberUserSubscription")),
			connect.WithClientOptions(opts...),
		),
		getAllVtuberUserSubscriptions: connect.NewClient[v1.GetAllVtuberUserSubscriptionsRequest, v1.GetAllVtuberUserSubscriptionsResponse](
			httpClient,
			baseURL+VtuberUserSubscriptionServiceGetAllVtuberUserSubscriptionsProcedure,
			connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("GetAllVtuberUserSubscriptions")),
			connect.WithClientOptions(opts...),
		),
		getVtuberUserSubscriptionById: connect.NewClient[v1.GetVtuberUserSubscriptionByIdRequest, v1.VtuberUserSubscription](
			httpClient,
			baseURL+VtuberUserSubscriptionServiceGetVtuberUserSubscriptionByIdProcedure,
			connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("GetVtuberUserSubscriptionById")),
			connect.WithClientOptions(opts...),
		),
		deleteVtuberUserSubscriptionById: connect.NewClient[v1.DeleteVtuberUserSubscriptionByIdRequest, v11.GenericResponse](
			httpClient,
			baseURL+VtuberUserSubscriptionServiceDeleteVtuberUserSubscriptionByIdProcedure,
			connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("DeleteVtuberUserSubscriptionById")),
			connect.WithClientOptions(opts...),
		),
		updateVtuberUserSubscriptionById: connect.NewClient[v1.UpdateVtuberUserSubscriptionRequest, v11.GenericResponse](
			httpClient,
			baseURL+VtuberUserSubscriptionServiceUpdateVtuberUserSubscriptionByIdProcedure,
			connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("UpdateVtuberUserSubscriptionById")),
			connect.WithClientOptions(opts...),
		),
		getMyVtuberUserSubscriptions: connect.NewClient[v1.GetMyVtuberUserSubscriptionsRequest, v1.GetAllVtuberUserSubscriptionsResponse](
			httpClient,
			baseURL+VtuberUserSubscriptionServiceGetMyVtuberUserSubscriptionsProcedure,
			connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("GetMyVtuberUserSubscriptions")),
			connect.WithClientOptions(opts...),
		),
	}
}

// vtuberUserSubscriptionServiceClient implements VtuberUserSubscriptionServiceClient.
type vtuberUserSubscriptionServiceClient struct {
	addVtuberUserSubscription        *connect.Client[v1.AddVtuberUserSubscriptionRequest, v1.AddVtuberUserSubscriptionResponse]
	getAllVtuberUserSubscriptions    *connect.Client[v1.GetAllVtuberUserSubscriptionsRequest, v1.GetAllVtuberUserSubscriptionsResponse]
	getVtuberUserSubscriptionById    *connect.Client[v1.GetVtuberUserSubscriptionByIdRequest, v1.VtuberUserSubscription]
	deleteVtuberUserSubscriptionById *connect.Client[v1.DeleteVtuberUserSubscriptionByIdRequest, v11.GenericResponse]
	updateVtuberUserSubscriptionById *connect.Client[v1.UpdateVtuberUserSubscriptionRequest, v11.GenericResponse]
	getMyVtuberUserSubscriptions     *connect.Client[v1.GetMyVtuberUserSubscriptionsRequest, v1.GetAllVtuberUserSubscriptionsResponse]
}

// AddVtuberUserSubscription calls
// api.vtubers.v1.VtuberUserSubscriptionService.AddVtuberUserSubscription.
func (c *vtuberUserSubscriptionServiceClient) AddVtuberUserSubscription(ctx context.Context, req *connect.Request[v1.AddVtuberUserSubscriptionRequest]) (*connect.Response[v1.AddVtuberUserSubscriptionResponse], error) {
	return c.addVtuberUserSubscription.CallUnary(ctx, req)
}

// GetAllVtuberUserSubscriptions calls
// api.vtubers.v1.VtuberUserSubscriptionService.GetAllVtuberUserSubscriptions.
func (c *vtuberUserSubscriptionServiceClient) GetAllVtuberUserSubscriptions(ctx context.Context, req *connect.Request[v1.GetAllVtuberUserSubscriptionsRequest]) (*connect.Response[v1.GetAllVtuberUserSubscriptionsResponse], error) {
	return c.getAllVtuberUserSubscriptions.CallUnary(ctx, req)
}

// GetVtuberUserSubscriptionById calls
// api.vtubers.v1.VtuberUserSubscriptionService.GetVtuberUserSubscriptionById.
func (c *vtuberUserSubscriptionServiceClient) GetVtuberUserSubscriptionById(ctx context.Context, req *connect.Request[v1.GetVtuberUserSubscriptionByIdRequest]) (*connect.Response[v1.VtuberUserSubscription], error) {
	return c.getVtuberUserSubscriptionById.CallUnary(ctx, req)
}

// DeleteVtuberUserSubscriptionById calls
// api.vtubers.v1.VtuberUserSubscriptionService.DeleteVtuberUserSubscriptionById.
func (c *vtuberUserSubscriptionServiceClient) DeleteVtuberUserSubscriptionById(ctx context.Context, req *connect.Request[v1.DeleteVtuberUserSubscriptionByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.deleteVtuberUserSubscriptionById.CallUnary(ctx, req)
}

// UpdateVtuberUserSubscriptionById calls
// api.vtubers.v1.VtuberUserSubscriptionService.UpdateVtuberUserSubscriptionById.
func (c *vtuberUserSubscriptionServiceClient) UpdateVtuberUserSubscriptionById(ctx context.Context, req *connect.Request[v1.UpdateVtuberUserSubscriptionRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.updateVtuberUserSubscriptionById.CallUnary(ctx, req)
}

// GetMyVtuberUserSubscriptions calls
// api.vtubers.v1.VtuberUserSubscriptionService.GetMyVtuberUserSubscriptions.
func (c *vtuberUserSubscriptionServiceClient) GetMyVtuberUserSubscriptions(ctx context.Context, req *connect.Request[v1.GetMyVtuberUserSubscriptionsRequest]) (*connect.Response[v1.GetAllVtuberUserSubscriptionsResponse], error) {
	return c.getMyVtuberUserSubscriptions.CallUnary(ctx, req)
}

// VtuberUserSubscriptionServiceHandler is an implementation of the
// api.vtubers.v1.VtuberUserSubscriptionService service.
type VtuberUserSubscriptionServiceHandler interface {
	AddVtuberUserSubscription(context.Context, *connect.Request[v1.AddVtuberUserSubscriptionRequest]) (*connect.Response[v1.AddVtuberUserSubscriptionResponse], error)
	GetAllVtuberUserSubscriptions(context.Context, *connect.Request[v1.GetAllVtuberUserSubscriptionsRequest]) (*connect.Response[v1.GetAllVtuberUserSubscriptionsResponse], error)
	GetVtuberUserSubscriptionById(context.Context, *connect.Request[v1.GetVtuberUserSubscriptionByIdRequest]) (*connect.Response[v1.VtuberUserSubscription], error)
	DeleteVtuberUserSubscriptionById(context.Context, *connect.Request[v1.DeleteVtuberUserSubscriptionByIdRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateVtuberUserSubscriptionById(context.Context, *connect.Request[v1.UpdateVtuberUserSubscriptionRequest]) (*connect.Response[v11.GenericResponse], error)
	GetMyVtuberUserSubscriptions(context.Context, *connect.Request[v1.GetMyVtuberUserSubscriptionsRequest]) (*connect.Response[v1.GetAllVtuberUserSubscriptionsResponse], error)
}

// NewVtuberUserSubscriptionServiceHandler builds an HTTP handler from the service implementation.
// It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewVtuberUserSubscriptionServiceHandler(svc VtuberUserSubscriptionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	vtuberUserSubscriptionServiceMethods := v1.File_vtubers_v1_vtuberusersubscription_proto.Services().ByName("VtuberUserSubscriptionService").Methods()
	vtuberUserSubscriptionServiceAddVtuberUserSubscriptionHandler := connect.NewUnaryHandler(
		VtuberUserSubscriptionServiceAddVtuberUserSubscriptionProcedure,
		svc.AddVtuberUserSubscription,
		connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("AddVtuberUserSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberUserSubscriptionServiceGetAllVtuberUserSubscriptionsHandler := connect.NewUnaryHandler(
		VtuberUserSubscriptionServiceGetAllVtuberUserSubscriptionsProcedure,
		svc.GetAllVtuberUserSubscriptions,
		connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("GetAllVtuberUserSubscriptions")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberUserSubscriptionServiceGetVtuberUserSubscriptionByIdHandler := connect.NewUnaryHandler(
		VtuberUserSubscriptionServiceGetVtuberUserSubscriptionByIdProcedure,
		svc.GetVtuberUserSubscriptionById,
		connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("GetVtuberUserSubscriptionById")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberUserSubscriptionServiceDeleteVtuberUserSubscriptionByIdHandler := connect.NewUnaryHandler(
		VtuberUserSubscriptionServiceDeleteVtuberUserSubscriptionByIdProcedure,
		svc.DeleteVtuberUserSubscriptionById,
		connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("DeleteVtuberUserSubscriptionById")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberUserSubscriptionServiceUpdateVtuberUserSubscriptionByIdHandler := connect.NewUnaryHandler(
		VtuberUserSubscriptionServiceUpdateVtuberUserSubscriptionByIdProcedure,
		svc.UpdateVtuberUserSubscriptionById,
		connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("UpdateVtuberUserSubscriptionById")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberUserSubscriptionServiceGetMyVtuberUserSubscriptionsHandler := connect.NewUnaryHandler(
		VtuberUserSubscriptionServiceGetMyVtuberUserSubscriptionsProcedure,
		svc.GetMyVtuberUserSubscriptions,
		connect.WithSchema(vtuberUserSubscriptionServiceMethods.ByName("GetMyVtuberUserSubscriptions")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.vtubers.v1.VtuberUserSubscriptionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case VtuberUserSubscriptionServiceAddVtuberUserSubscriptionProcedure:
			vtuberUserSubscriptionServiceAddVtuberUserSubscriptionHandler.ServeHTTP(w, r)
		case VtuberUserSubscriptionServiceGetAllVtuberUserSubscriptionsProcedure:
			vtuberUserSubscriptionServiceGetAllVtuberUserSubscriptionsHandler.ServeHTTP(w, r)
		case VtuberUserSubscriptionServiceGetVtuberUserSubscriptionByIdProcedure:
			vtuberUserSubscriptionServiceGetVtuberUserSubscriptionByIdHandler.ServeHTTP(w, r)
		case VtuberUserSubscriptionServiceDeleteVtuberUserSubscriptionByIdProcedure:
			vtuberUserSubscriptionServiceDeleteVtuberUserSubscriptionByIdHandler.ServeHTTP(w, r)
		case VtuberUserSubscriptionServiceUpdateVtuberUserSubscriptionByIdProcedure:
			vtuberUserSubscriptionServiceUpdateVtuberUserSubscriptionByIdHandler.ServeHTTP(w, r)
		case VtuberUserSubscriptionServiceGetMyVtuberUserSubscriptionsProcedure:
			vtuberUserSubscriptionServiceGetMyVtuberUserSubscriptionsHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedVtuberUserSubscriptionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedVtuberUserSubscriptionServiceHandler struct{}

func (UnimplementedVtuberUserSubscriptionServiceHandler) AddVtuberUserSubscription(context.Context, *connect.Request[v1.AddVtuberUserSubscriptionRequest]) (*connect.Response[v1.AddVtuberUserSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberUserSubscriptionService.AddVtuberUserSubscription is not implemented"))
}

func (UnimplementedVtuberUserSubscriptionServiceHandler) GetAllVtuberUserSubscriptions(context.Context, *connect.Request[v1.GetAllVtuberUserSubscriptionsRequest]) (*connect.Response[v1.GetAllVtuberUserSubscriptionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberUserSubscriptionService.GetAllVtuberUserSubscriptions is not implemented"))
}

func (UnimplementedVtuberUserSubscriptionServiceHandler) GetVtuberUserSubscriptionById(context.Context, *connect.Request[v1.GetVtuberUserSubscriptionByIdRequest]) (*connect.Response[v1.VtuberUserSubscription], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberUserSubscriptionService.GetVtuberUserSubscriptionById is not implemented"))
}

func (UnimplementedVtuberUserSubscriptionServiceHandler) DeleteVtuberUserSubscriptionById(context.Context, *connect.Request[v1.DeleteVtuberUserSubscriptionByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberUserSubscriptionService.DeleteVtuberUserSubscriptionById is not implemented"))
}

func (UnimplementedVtuberUserSubscriptionServiceHandler) UpdateVtuberUserSubscriptionById(context.Context, *connect.Request[v1.UpdateVtuberUserSubscriptionRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberUserSubscriptionService.UpdateVtuberUserSubscriptionById is not implemented"))
}

func (UnimplementedVtuberUserSubscriptionServiceHandler) GetMyVtuberUserSubscriptions(context.Context, *connect.Request[v1.GetMyVtuberUserSubscriptionsRequest]) (*connect.Response[v1.GetAllVtuberUserSubscriptionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberUserSubscriptionService.GetMyVtuberUserSubscriptions is not implemented"))
}
