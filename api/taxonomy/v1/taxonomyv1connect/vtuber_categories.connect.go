// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: taxonomy/v1/vtuber_categories.proto

package taxonomyv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/taxonomy/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// VtuberCategoryServiceName is the fully-qualified name of the VtuberCategoryService service.
	VtuberCategoryServiceName = "api.taxonomy.v1.VtuberCategoryService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// VtuberCategoryServiceAddCategoryProcedure is the fully-qualified name of the
	// VtuberCategoryService's AddCategory RPC.
	VtuberCategoryServiceAddCategoryProcedure = "/api.taxonomy.v1.VtuberCategoryService/AddCategory"
	// VtuberCategoryServiceGetAllVtuberCategoriesProcedure is the fully-qualified name of the
	// VtuberCategoryService's GetAllVtuberCategories RPC.
	VtuberCategoryServiceGetAllVtuberCategoriesProcedure = "/api.taxonomy.v1.VtuberCategoryService/GetAllVtuberCategories"
	// VtuberCategoryServiceGetVtuberCategoryProcedure is the fully-qualified name of the
	// VtuberCategoryService's GetVtuberCategory RPC.
	VtuberCategoryServiceGetVtuberCategoryProcedure = "/api.taxonomy.v1.VtuberCategoryService/GetVtuberCategory"
	// VtuberCategoryServiceUpdateCategoryProcedure is the fully-qualified name of the
	// VtuberCategoryService's UpdateCategory RPC.
	VtuberCategoryServiceUpdateCategoryProcedure = "/api.taxonomy.v1.VtuberCategoryService/UpdateCategory"
	// VtuberCategoryServiceDeleteCategoryProcedure is the fully-qualified name of the
	// VtuberCategoryService's DeleteCategory RPC.
	VtuberCategoryServiceDeleteCategoryProcedure = "/api.taxonomy.v1.VtuberCategoryService/DeleteCategory"
	// VtuberCategoryServiceGetUsedCategoriesProcedure is the fully-qualified name of the
	// VtuberCategoryService's GetUsedCategories RPC.
	VtuberCategoryServiceGetUsedCategoriesProcedure = "/api.taxonomy.v1.VtuberCategoryService/GetUsedCategories"
)

// VtuberCategoryServiceClient is a client for the api.taxonomy.v1.VtuberCategoryService service.
type VtuberCategoryServiceClient interface {
	AddCategory(context.Context, *connect.Request[v1.AddVtuberCategoryRequest]) (*connect.Response[v1.AddVtuberCategoryResponse], error)
	GetAllVtuberCategories(context.Context, *connect.Request[v1.GetAllVtuberCategoriesRequest]) (*connect.Response[v1.GetAllVtuberCategoriesResponse], error)
	GetVtuberCategory(context.Context, *connect.Request[v1.GetVtuberCategoryRequest]) (*connect.Response[v1.GetVtuberCategoryResponse], error)
	UpdateCategory(context.Context, *connect.Request[v1.UpdateVtuberCategoryRequest]) (*connect.Response[v11.GenericResponse], error)
	DeleteCategory(context.Context, *connect.Request[v1.DeleteVtuberCategoryRequest]) (*connect.Response[v11.GenericResponse], error)
	GetUsedCategories(context.Context, *connect.Request[v1.GetUsedVtuberCategoriesRequest]) (*connect.Response[v1.GetUsedVtuberCategoriesResponse], error)
}

// NewVtuberCategoryServiceClient constructs a client for the api.taxonomy.v1.VtuberCategoryService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewVtuberCategoryServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) VtuberCategoryServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	vtuberCategoryServiceMethods := v1.File_taxonomy_v1_vtuber_categories_proto.Services().ByName("VtuberCategoryService").Methods()
	return &vtuberCategoryServiceClient{
		addCategory: connect.NewClient[v1.AddVtuberCategoryRequest, v1.AddVtuberCategoryResponse](
			httpClient,
			baseURL+VtuberCategoryServiceAddCategoryProcedure,
			connect.WithSchema(vtuberCategoryServiceMethods.ByName("AddCategory")),
			connect.WithClientOptions(opts...),
		),
		getAllVtuberCategories: connect.NewClient[v1.GetAllVtuberCategoriesRequest, v1.GetAllVtuberCategoriesResponse](
			httpClient,
			baseURL+VtuberCategoryServiceGetAllVtuberCategoriesProcedure,
			connect.WithSchema(vtuberCategoryServiceMethods.ByName("GetAllVtuberCategories")),
			connect.WithClientOptions(opts...),
		),
		getVtuberCategory: connect.NewClient[v1.GetVtuberCategoryRequest, v1.GetVtuberCategoryResponse](
			httpClient,
			baseURL+VtuberCategoryServiceGetVtuberCategoryProcedure,
			connect.WithSchema(vtuberCategoryServiceMethods.ByName("GetVtuberCategory")),
			connect.WithClientOptions(opts...),
		),
		updateCategory: connect.NewClient[v1.UpdateVtuberCategoryRequest, v11.GenericResponse](
			httpClient,
			baseURL+VtuberCategoryServiceUpdateCategoryProcedure,
			connect.WithSchema(vtuberCategoryServiceMethods.ByName("UpdateCategory")),
			connect.WithClientOptions(opts...),
		),
		deleteCategory: connect.NewClient[v1.DeleteVtuberCategoryRequest, v11.GenericResponse](
			httpClient,
			baseURL+VtuberCategoryServiceDeleteCategoryProcedure,
			connect.WithSchema(vtuberCategoryServiceMethods.ByName("DeleteCategory")),
			connect.WithClientOptions(opts...),
		),
		getUsedCategories: connect.NewClient[v1.GetUsedVtuberCategoriesRequest, v1.GetUsedVtuberCategoriesResponse](
			httpClient,
			baseURL+VtuberCategoryServiceGetUsedCategoriesProcedure,
			connect.WithSchema(vtuberCategoryServiceMethods.ByName("GetUsedCategories")),
			connect.WithClientOptions(opts...),
		),
	}
}

// vtuberCategoryServiceClient implements VtuberCategoryServiceClient.
type vtuberCategoryServiceClient struct {
	addCategory            *connect.Client[v1.AddVtuberCategoryRequest, v1.AddVtuberCategoryResponse]
	getAllVtuberCategories *connect.Client[v1.GetAllVtuberCategoriesRequest, v1.GetAllVtuberCategoriesResponse]
	getVtuberCategory      *connect.Client[v1.GetVtuberCategoryRequest, v1.GetVtuberCategoryResponse]
	updateCategory         *connect.Client[v1.UpdateVtuberCategoryRequest, v11.GenericResponse]
	deleteCategory         *connect.Client[v1.DeleteVtuberCategoryRequest, v11.GenericResponse]
	getUsedCategories      *connect.Client[v1.GetUsedVtuberCategoriesRequest, v1.GetUsedVtuberCategoriesResponse]
}

// AddCategory calls api.taxonomy.v1.VtuberCategoryService.AddCategory.
func (c *vtuberCategoryServiceClient) AddCategory(ctx context.Context, req *connect.Request[v1.AddVtuberCategoryRequest]) (*connect.Response[v1.AddVtuberCategoryResponse], error) {
	return c.addCategory.CallUnary(ctx, req)
}

// GetAllVtuberCategories calls api.taxonomy.v1.VtuberCategoryService.GetAllVtuberCategories.
func (c *vtuberCategoryServiceClient) GetAllVtuberCategories(ctx context.Context, req *connect.Request[v1.GetAllVtuberCategoriesRequest]) (*connect.Response[v1.GetAllVtuberCategoriesResponse], error) {
	return c.getAllVtuberCategories.CallUnary(ctx, req)
}

// GetVtuberCategory calls api.taxonomy.v1.VtuberCategoryService.GetVtuberCategory.
func (c *vtuberCategoryServiceClient) GetVtuberCategory(ctx context.Context, req *connect.Request[v1.GetVtuberCategoryRequest]) (*connect.Response[v1.GetVtuberCategoryResponse], error) {
	return c.getVtuberCategory.CallUnary(ctx, req)
}

// UpdateCategory calls api.taxonomy.v1.VtuberCategoryService.UpdateCategory.
func (c *vtuberCategoryServiceClient) UpdateCategory(ctx context.Context, req *connect.Request[v1.UpdateVtuberCategoryRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.updateCategory.CallUnary(ctx, req)
}

// DeleteCategory calls api.taxonomy.v1.VtuberCategoryService.DeleteCategory.
func (c *vtuberCategoryServiceClient) DeleteCategory(ctx context.Context, req *connect.Request[v1.DeleteVtuberCategoryRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.deleteCategory.CallUnary(ctx, req)
}

// GetUsedCategories calls api.taxonomy.v1.VtuberCategoryService.GetUsedCategories.
func (c *vtuberCategoryServiceClient) GetUsedCategories(ctx context.Context, req *connect.Request[v1.GetUsedVtuberCategoriesRequest]) (*connect.Response[v1.GetUsedVtuberCategoriesResponse], error) {
	return c.getUsedCategories.CallUnary(ctx, req)
}

// VtuberCategoryServiceHandler is an implementation of the api.taxonomy.v1.VtuberCategoryService
// service.
type VtuberCategoryServiceHandler interface {
	AddCategory(context.Context, *connect.Request[v1.AddVtuberCategoryRequest]) (*connect.Response[v1.AddVtuberCategoryResponse], error)
	GetAllVtuberCategories(context.Context, *connect.Request[v1.GetAllVtuberCategoriesRequest]) (*connect.Response[v1.GetAllVtuberCategoriesResponse], error)
	GetVtuberCategory(context.Context, *connect.Request[v1.GetVtuberCategoryRequest]) (*connect.Response[v1.GetVtuberCategoryResponse], error)
	UpdateCategory(context.Context, *connect.Request[v1.UpdateVtuberCategoryRequest]) (*connect.Response[v11.GenericResponse], error)
	DeleteCategory(context.Context, *connect.Request[v1.DeleteVtuberCategoryRequest]) (*connect.Response[v11.GenericResponse], error)
	GetUsedCategories(context.Context, *connect.Request[v1.GetUsedVtuberCategoriesRequest]) (*connect.Response[v1.GetUsedVtuberCategoriesResponse], error)
}

// NewVtuberCategoryServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewVtuberCategoryServiceHandler(svc VtuberCategoryServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	vtuberCategoryServiceMethods := v1.File_taxonomy_v1_vtuber_categories_proto.Services().ByName("VtuberCategoryService").Methods()
	vtuberCategoryServiceAddCategoryHandler := connect.NewUnaryHandler(
		VtuberCategoryServiceAddCategoryProcedure,
		svc.AddCategory,
		connect.WithSchema(vtuberCategoryServiceMethods.ByName("AddCategory")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberCategoryServiceGetAllVtuberCategoriesHandler := connect.NewUnaryHandler(
		VtuberCategoryServiceGetAllVtuberCategoriesProcedure,
		svc.GetAllVtuberCategories,
		connect.WithSchema(vtuberCategoryServiceMethods.ByName("GetAllVtuberCategories")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberCategoryServiceGetVtuberCategoryHandler := connect.NewUnaryHandler(
		VtuberCategoryServiceGetVtuberCategoryProcedure,
		svc.GetVtuberCategory,
		connect.WithSchema(vtuberCategoryServiceMethods.ByName("GetVtuberCategory")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberCategoryServiceUpdateCategoryHandler := connect.NewUnaryHandler(
		VtuberCategoryServiceUpdateCategoryProcedure,
		svc.UpdateCategory,
		connect.WithSchema(vtuberCategoryServiceMethods.ByName("UpdateCategory")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberCategoryServiceDeleteCategoryHandler := connect.NewUnaryHandler(
		VtuberCategoryServiceDeleteCategoryProcedure,
		svc.DeleteCategory,
		connect.WithSchema(vtuberCategoryServiceMethods.ByName("DeleteCategory")),
		connect.WithHandlerOptions(opts...),
	)
	vtuberCategoryServiceGetUsedCategoriesHandler := connect.NewUnaryHandler(
		VtuberCategoryServiceGetUsedCategoriesProcedure,
		svc.GetUsedCategories,
		connect.WithSchema(vtuberCategoryServiceMethods.ByName("GetUsedCategories")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.taxonomy.v1.VtuberCategoryService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case VtuberCategoryServiceAddCategoryProcedure:
			vtuberCategoryServiceAddCategoryHandler.ServeHTTP(w, r)
		case VtuberCategoryServiceGetAllVtuberCategoriesProcedure:
			vtuberCategoryServiceGetAllVtuberCategoriesHandler.ServeHTTP(w, r)
		case VtuberCategoryServiceGetVtuberCategoryProcedure:
			vtuberCategoryServiceGetVtuberCategoryHandler.ServeHTTP(w, r)
		case VtuberCategoryServiceUpdateCategoryProcedure:
			vtuberCategoryServiceUpdateCategoryHandler.ServeHTTP(w, r)
		case VtuberCategoryServiceDeleteCategoryProcedure:
			vtuberCategoryServiceDeleteCategoryHandler.ServeHTTP(w, r)
		case VtuberCategoryServiceGetUsedCategoriesProcedure:
			vtuberCategoryServiceGetUsedCategoriesHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedVtuberCategoryServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedVtuberCategoryServiceHandler struct{}

func (UnimplementedVtuberCategoryServiceHandler) AddCategory(context.Context, *connect.Request[v1.AddVtuberCategoryRequest]) (*connect.Response[v1.AddVtuberCategoryResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.VtuberCategoryService.AddCategory is not implemented"))
}

func (UnimplementedVtuberCategoryServiceHandler) GetAllVtuberCategories(context.Context, *connect.Request[v1.GetAllVtuberCategoriesRequest]) (*connect.Response[v1.GetAllVtuberCategoriesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.VtuberCategoryService.GetAllVtuberCategories is not implemented"))
}

func (UnimplementedVtuberCategoryServiceHandler) GetVtuberCategory(context.Context, *connect.Request[v1.GetVtuberCategoryRequest]) (*connect.Response[v1.GetVtuberCategoryResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.VtuberCategoryService.GetVtuberCategory is not implemented"))
}

func (UnimplementedVtuberCategoryServiceHandler) UpdateCategory(context.Context, *connect.Request[v1.UpdateVtuberCategoryRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.VtuberCategoryService.UpdateCategory is not implemented"))
}

func (UnimplementedVtuberCategoryServiceHandler) DeleteCategory(context.Context, *connect.Request[v1.DeleteVtuberCategoryRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.VtuberCategoryService.DeleteCategory is not implemented"))
}

func (UnimplementedVtuberCategoryServiceHandler) GetUsedCategories(context.Context, *connect.Request[v1.GetUsedVtuberCategoriesRequest]) (*connect.Response[v1.GetUsedVtuberCategoriesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.VtuberCategoryService.GetUsedCategories is not implemented"))
}
