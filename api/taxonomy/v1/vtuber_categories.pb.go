// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: taxonomy/v1/vtuber_categories.proto

package taxonomyv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddVtuberCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" validate:"required,min=1"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	Image         *string                `protobuf:"bytes,3,opt,name=image,proto3,oneof" json:"image,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberCategoryRequest) Reset() {
	*x = AddVtuberCategoryRequest{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberCategoryRequest) ProtoMessage() {}

func (x *AddVtuberCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberCategoryRequest.ProtoReflect.Descriptor instead.
func (*AddVtuberCategoryRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{0}
}

func (x *AddVtuberCategoryRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddVtuberCategoryRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddVtuberCategoryRequest) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

type AddVtuberCategoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *VtuberCategory        `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberCategoryResponse) Reset() {
	*x = AddVtuberCategoryResponse{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberCategoryResponse) ProtoMessage() {}

func (x *AddVtuberCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberCategoryResponse.ProtoReflect.Descriptor instead.
func (*AddVtuberCategoryResponse) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{1}
}

func (x *AddVtuberCategoryResponse) GetData() *VtuberCategory {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetVtuberCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberCategoryRequest) Reset() {
	*x = GetVtuberCategoryRequest{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberCategoryRequest) ProtoMessage() {}

func (x *GetVtuberCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberCategoryRequest.ProtoReflect.Descriptor instead.
func (*GetVtuberCategoryRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{2}
}

func (x *GetVtuberCategoryRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetVtuberCategoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *VtuberCategory        `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberCategoryResponse) Reset() {
	*x = GetVtuberCategoryResponse{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberCategoryResponse) ProtoMessage() {}

func (x *GetVtuberCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberCategoryResponse.ProtoReflect.Descriptor instead.
func (*GetVtuberCategoryResponse) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{3}
}

func (x *GetVtuberCategoryResponse) GetData() *VtuberCategory {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateVtuberCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" validate:"required"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	Image         string                 `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVtuberCategoryRequest) Reset() {
	*x = UpdateVtuberCategoryRequest{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberCategoryRequest) ProtoMessage() {}

func (x *UpdateVtuberCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberCategoryRequest.ProtoReflect.Descriptor instead.
func (*UpdateVtuberCategoryRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateVtuberCategoryRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateVtuberCategoryRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateVtuberCategoryRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateVtuberCategoryRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type DeleteVtuberCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVtuberCategoryRequest) Reset() {
	*x = DeleteVtuberCategoryRequest{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVtuberCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVtuberCategoryRequest) ProtoMessage() {}

func (x *DeleteVtuberCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVtuberCategoryRequest.ProtoReflect.Descriptor instead.
func (*DeleteVtuberCategoryRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteVtuberCategoryRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type VtuberCategory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Slug          string                 `protobuf:"bytes,5,opt,name=slug,proto3" json:"slug,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VtuberCategory) Reset() {
	*x = VtuberCategory{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VtuberCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VtuberCategory) ProtoMessage() {}

func (x *VtuberCategory) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VtuberCategory.ProtoReflect.Descriptor instead.
func (*VtuberCategory) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{6}
}

func (x *VtuberCategory) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VtuberCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VtuberCategory) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VtuberCategory) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VtuberCategory) GetSlug() string {
	if x != nil {
		return x.Slug
	}
	return ""
}

type GetAllVtuberCategoriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllVtuberCategoriesRequest) Reset() {
	*x = GetAllVtuberCategoriesRequest{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberCategoriesRequest) ProtoMessage() {}

func (x *GetAllVtuberCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetAllVtuberCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{7}
}

type GetAllVtuberCategoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Categories    []*VtuberCategory      `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllVtuberCategoriesResponse) Reset() {
	*x = GetAllVtuberCategoriesResponse{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberCategoriesResponse) ProtoMessage() {}

func (x *GetAllVtuberCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetAllVtuberCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{8}
}

func (x *GetAllVtuberCategoriesResponse) GetCategories() []*VtuberCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

type GetUsedVtuberCategoriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUsedVtuberCategoriesRequest) Reset() {
	*x = GetUsedVtuberCategoriesRequest{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUsedVtuberCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsedVtuberCategoriesRequest) ProtoMessage() {}

func (x *GetUsedVtuberCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsedVtuberCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetUsedVtuberCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{9}
}

type VtuberCategoryWithCount struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Category      *VtuberCategory        `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"` // Number of vtubers in this category
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VtuberCategoryWithCount) Reset() {
	*x = VtuberCategoryWithCount{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VtuberCategoryWithCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VtuberCategoryWithCount) ProtoMessage() {}

func (x *VtuberCategoryWithCount) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VtuberCategoryWithCount.ProtoReflect.Descriptor instead.
func (*VtuberCategoryWithCount) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{10}
}

func (x *VtuberCategoryWithCount) GetCategory() *VtuberCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *VtuberCategoryWithCount) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetUsedVtuberCategoriesResponse struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Categories    []*VtuberCategoryWithCount `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUsedVtuberCategoriesResponse) Reset() {
	*x = GetUsedVtuberCategoriesResponse{}
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUsedVtuberCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsedVtuberCategoriesResponse) ProtoMessage() {}

func (x *GetUsedVtuberCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_vtuber_categories_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsedVtuberCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetUsedVtuberCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP(), []int{11}
}

func (x *GetUsedVtuberCategoriesResponse) GetCategories() []*VtuberCategoryWithCount {
	if x != nil {
		return x.Categories
	}
	return nil
}

var File_taxonomy_v1_vtuber_categories_proto protoreflect.FileDescriptor

const file_taxonomy_v1_vtuber_categories_proto_rawDesc = "" +
	"\n" +
	"#taxonomy/v1/vtuber_categories.proto\x12\x0fapi.taxonomy.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"u\n" +
	"\x18AddVtuberCategoryRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x19\n" +
	"\x05image\x18\x03 \x01(\tH\x00R\x05image\x88\x01\x01B\b\n" +
	"\x06_image\"P\n" +
	"\x19AddVtuberCategoryResponse\x123\n" +
	"\x04data\x18\x01 \x01(\v2\x1f.api.taxonomy.v1.VtuberCategoryR\x04data\"*\n" +
	"\x18GetVtuberCategoryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"P\n" +
	"\x19GetVtuberCategoryResponse\x123\n" +
	"\x04data\x18\x01 \x01(\v2\x1f.api.taxonomy.v1.VtuberCategoryR\x04data\"y\n" +
	"\x1bUpdateVtuberCategoryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x14\n" +
	"\x05image\x18\x04 \x01(\tR\x05image\"-\n" +
	"\x1bDeleteVtuberCategoryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\xa5\x01\n" +
	"\x0eVtuberCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x12\n" +
	"\x04slug\x18\x05 \x01(\tR\x04slug\"\x1f\n" +
	"\x1dGetAllVtuberCategoriesRequest\"a\n" +
	"\x1eGetAllVtuberCategoriesResponse\x12?\n" +
	"\n" +
	"categories\x18\x01 \x03(\v2\x1f.api.taxonomy.v1.VtuberCategoryR\n" +
	"categories\" \n" +
	"\x1eGetUsedVtuberCategoriesRequest\"l\n" +
	"\x17VtuberCategoryWithCount\x12;\n" +
	"\bcategory\x18\x01 \x01(\v2\x1f.api.taxonomy.v1.VtuberCategoryR\bcategory\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"k\n" +
	"\x1fGetUsedVtuberCategoriesResponse\x12H\n" +
	"\n" +
	"categories\x18\x01 \x03(\v2(.api.taxonomy.v1.VtuberCategoryWithCountR\n" +
	"categories2\xc5\x05\n" +
	"\x15VtuberCategoryService\x12n\n" +
	"\vAddCategory\x12).api.taxonomy.v1.AddVtuberCategoryRequest\x1a*.api.taxonomy.v1.AddVtuberCategoryResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12y\n" +
	"\x16GetAllVtuberCategories\x12..api.taxonomy.v1.GetAllVtuberCategoriesRequest\x1a/.api.taxonomy.v1.GetAllVtuberCategoriesResponse\x12j\n" +
	"\x11GetVtuberCategory\x12).api.taxonomy.v1.GetVtuberCategoryRequest\x1a*.api.taxonomy.v1.GetVtuberCategoryResponse\x12h\n" +
	"\x0eUpdateCategory\x12,.api.taxonomy.v1.UpdateVtuberCategoryRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12h\n" +
	"\x0eDeleteCategory\x12,.api.taxonomy.v1.DeleteVtuberCategoryRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12\x80\x01\n" +
	"\x11GetUsedCategories\x12/.api.taxonomy.v1.GetUsedVtuberCategoriesRequest\x1a0.api.taxonomy.v1.GetUsedVtuberCategoriesResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01B6Z4github.com/nsp-inc/vtuber/api/taxonomy/v1;taxonomyv1b\x06proto3"

var (
	file_taxonomy_v1_vtuber_categories_proto_rawDescOnce sync.Once
	file_taxonomy_v1_vtuber_categories_proto_rawDescData []byte
)

func file_taxonomy_v1_vtuber_categories_proto_rawDescGZIP() []byte {
	file_taxonomy_v1_vtuber_categories_proto_rawDescOnce.Do(func() {
		file_taxonomy_v1_vtuber_categories_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_taxonomy_v1_vtuber_categories_proto_rawDesc), len(file_taxonomy_v1_vtuber_categories_proto_rawDesc)))
	})
	return file_taxonomy_v1_vtuber_categories_proto_rawDescData
}

var file_taxonomy_v1_vtuber_categories_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_taxonomy_v1_vtuber_categories_proto_goTypes = []any{
	(*AddVtuberCategoryRequest)(nil),        // 0: api.taxonomy.v1.AddVtuberCategoryRequest
	(*AddVtuberCategoryResponse)(nil),       // 1: api.taxonomy.v1.AddVtuberCategoryResponse
	(*GetVtuberCategoryRequest)(nil),        // 2: api.taxonomy.v1.GetVtuberCategoryRequest
	(*GetVtuberCategoryResponse)(nil),       // 3: api.taxonomy.v1.GetVtuberCategoryResponse
	(*UpdateVtuberCategoryRequest)(nil),     // 4: api.taxonomy.v1.UpdateVtuberCategoryRequest
	(*DeleteVtuberCategoryRequest)(nil),     // 5: api.taxonomy.v1.DeleteVtuberCategoryRequest
	(*VtuberCategory)(nil),                  // 6: api.taxonomy.v1.VtuberCategory
	(*GetAllVtuberCategoriesRequest)(nil),   // 7: api.taxonomy.v1.GetAllVtuberCategoriesRequest
	(*GetAllVtuberCategoriesResponse)(nil),  // 8: api.taxonomy.v1.GetAllVtuberCategoriesResponse
	(*GetUsedVtuberCategoriesRequest)(nil),  // 9: api.taxonomy.v1.GetUsedVtuberCategoriesRequest
	(*VtuberCategoryWithCount)(nil),         // 10: api.taxonomy.v1.VtuberCategoryWithCount
	(*GetUsedVtuberCategoriesResponse)(nil), // 11: api.taxonomy.v1.GetUsedVtuberCategoriesResponse
	(*timestamppb.Timestamp)(nil),           // 12: google.protobuf.Timestamp
	(*v1.GenericResponse)(nil),              // 13: api.shared.v1.GenericResponse
}
var file_taxonomy_v1_vtuber_categories_proto_depIdxs = []int32{
	6,  // 0: api.taxonomy.v1.AddVtuberCategoryResponse.data:type_name -> api.taxonomy.v1.VtuberCategory
	6,  // 1: api.taxonomy.v1.GetVtuberCategoryResponse.data:type_name -> api.taxonomy.v1.VtuberCategory
	12, // 2: api.taxonomy.v1.VtuberCategory.created_at:type_name -> google.protobuf.Timestamp
	6,  // 3: api.taxonomy.v1.GetAllVtuberCategoriesResponse.categories:type_name -> api.taxonomy.v1.VtuberCategory
	6,  // 4: api.taxonomy.v1.VtuberCategoryWithCount.category:type_name -> api.taxonomy.v1.VtuberCategory
	10, // 5: api.taxonomy.v1.GetUsedVtuberCategoriesResponse.categories:type_name -> api.taxonomy.v1.VtuberCategoryWithCount
	0,  // 6: api.taxonomy.v1.VtuberCategoryService.AddCategory:input_type -> api.taxonomy.v1.AddVtuberCategoryRequest
	7,  // 7: api.taxonomy.v1.VtuberCategoryService.GetAllVtuberCategories:input_type -> api.taxonomy.v1.GetAllVtuberCategoriesRequest
	2,  // 8: api.taxonomy.v1.VtuberCategoryService.GetVtuberCategory:input_type -> api.taxonomy.v1.GetVtuberCategoryRequest
	4,  // 9: api.taxonomy.v1.VtuberCategoryService.UpdateCategory:input_type -> api.taxonomy.v1.UpdateVtuberCategoryRequest
	5,  // 10: api.taxonomy.v1.VtuberCategoryService.DeleteCategory:input_type -> api.taxonomy.v1.DeleteVtuberCategoryRequest
	9,  // 11: api.taxonomy.v1.VtuberCategoryService.GetUsedCategories:input_type -> api.taxonomy.v1.GetUsedVtuberCategoriesRequest
	1,  // 12: api.taxonomy.v1.VtuberCategoryService.AddCategory:output_type -> api.taxonomy.v1.AddVtuberCategoryResponse
	8,  // 13: api.taxonomy.v1.VtuberCategoryService.GetAllVtuberCategories:output_type -> api.taxonomy.v1.GetAllVtuberCategoriesResponse
	3,  // 14: api.taxonomy.v1.VtuberCategoryService.GetVtuberCategory:output_type -> api.taxonomy.v1.GetVtuberCategoryResponse
	13, // 15: api.taxonomy.v1.VtuberCategoryService.UpdateCategory:output_type -> api.shared.v1.GenericResponse
	13, // 16: api.taxonomy.v1.VtuberCategoryService.DeleteCategory:output_type -> api.shared.v1.GenericResponse
	11, // 17: api.taxonomy.v1.VtuberCategoryService.GetUsedCategories:output_type -> api.taxonomy.v1.GetUsedVtuberCategoriesResponse
	12, // [12:18] is the sub-list for method output_type
	6,  // [6:12] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_taxonomy_v1_vtuber_categories_proto_init() }
func file_taxonomy_v1_vtuber_categories_proto_init() {
	if File_taxonomy_v1_vtuber_categories_proto != nil {
		return
	}
	file_taxonomy_v1_vtuber_categories_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_taxonomy_v1_vtuber_categories_proto_rawDesc), len(file_taxonomy_v1_vtuber_categories_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_taxonomy_v1_vtuber_categories_proto_goTypes,
		DependencyIndexes: file_taxonomy_v1_vtuber_categories_proto_depIdxs,
		MessageInfos:      file_taxonomy_v1_vtuber_categories_proto_msgTypes,
	}.Build()
	File_taxonomy_v1_vtuber_categories_proto = out.File
	file_taxonomy_v1_vtuber_categories_proto_goTypes = nil
	file_taxonomy_v1_vtuber_categories_proto_depIdxs = nil
}
