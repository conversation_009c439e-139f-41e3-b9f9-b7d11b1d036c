// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: notifications/v1/notifications.proto

package notificationsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/notifications/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// NotificationsServiceName is the fully-qualified name of the NotificationsService service.
	NotificationsServiceName = "api.notifications.v1.NotificationsService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// NotificationsServiceGetUserNotificationsProcedure is the fully-qualified name of the
	// NotificationsService's GetUserNotifications RPC.
	NotificationsServiceGetUserNotificationsProcedure = "/api.notifications.v1.NotificationsService/GetUserNotifications"
	// NotificationsServiceGetCreatorNotificationsProcedure is the fully-qualified name of the
	// NotificationsService's GetCreatorNotifications RPC.
	NotificationsServiceGetCreatorNotificationsProcedure = "/api.notifications.v1.NotificationsService/GetCreatorNotifications"
	// NotificationsServiceMarkNotificationAsReadProcedure is the fully-qualified name of the
	// NotificationsService's MarkNotificationAsRead RPC.
	NotificationsServiceMarkNotificationAsReadProcedure = "/api.notifications.v1.NotificationsService/MarkNotificationAsRead"
	// NotificationsServiceMarkNotificationAsUnReadProcedure is the fully-qualified name of the
	// NotificationsService's MarkNotificationAsUnRead RPC.
	NotificationsServiceMarkNotificationAsUnReadProcedure = "/api.notifications.v1.NotificationsService/MarkNotificationAsUnRead"
	// NotificationsServiceDeleteNotificationByIdProcedure is the fully-qualified name of the
	// NotificationsService's DeleteNotificationById RPC.
	NotificationsServiceDeleteNotificationByIdProcedure = "/api.notifications.v1.NotificationsService/DeleteNotificationById"
	// NotificationsServiceGetNotificationCountProcedure is the fully-qualified name of the
	// NotificationsService's GetNotificationCount RPC.
	NotificationsServiceGetNotificationCountProcedure = "/api.notifications.v1.NotificationsService/GetNotificationCount"
)

// NotificationsServiceClient is a client for the api.notifications.v1.NotificationsService service.
type NotificationsServiceClient interface {
	GetUserNotifications(context.Context, *connect.Request[v1.GetNotificationsRequest]) (*connect.Response[v1.GetUserNotificationsResponse], error)
	GetCreatorNotifications(context.Context, *connect.Request[v1.GetNotificationsRequest]) (*connect.Response[v1.GetUserNotificationsResponse], error)
	MarkNotificationAsRead(context.Context, *connect.Request[v1.MarkNotificationAsReadRequest]) (*connect.Response[v1.MarkNotificationAsReadResponse], error)
	MarkNotificationAsUnRead(context.Context, *connect.Request[v1.MarkNotificationAsUnReadRequest]) (*connect.Response[v1.MarkNotificationAsUnReadResponse], error)
	DeleteNotificationById(context.Context, *connect.Request[v1.DeleteNotificationByIdRequest]) (*connect.Response[v1.DeleteNotificationByIdResponse], error)
	GetNotificationCount(context.Context, *connect.Request[v1.NotificationCountRequest]) (*connect.Response[v1.NotificationCountResponse], error)
}

// NewNotificationsServiceClient constructs a client for the
// api.notifications.v1.NotificationsService service. By default, it uses the Connect protocol with
// the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To use
// the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewNotificationsServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) NotificationsServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	notificationsServiceMethods := v1.File_notifications_v1_notifications_proto.Services().ByName("NotificationsService").Methods()
	return &notificationsServiceClient{
		getUserNotifications: connect.NewClient[v1.GetNotificationsRequest, v1.GetUserNotificationsResponse](
			httpClient,
			baseURL+NotificationsServiceGetUserNotificationsProcedure,
			connect.WithSchema(notificationsServiceMethods.ByName("GetUserNotifications")),
			connect.WithClientOptions(opts...),
		),
		getCreatorNotifications: connect.NewClient[v1.GetNotificationsRequest, v1.GetUserNotificationsResponse](
			httpClient,
			baseURL+NotificationsServiceGetCreatorNotificationsProcedure,
			connect.WithSchema(notificationsServiceMethods.ByName("GetCreatorNotifications")),
			connect.WithClientOptions(opts...),
		),
		markNotificationAsRead: connect.NewClient[v1.MarkNotificationAsReadRequest, v1.MarkNotificationAsReadResponse](
			httpClient,
			baseURL+NotificationsServiceMarkNotificationAsReadProcedure,
			connect.WithSchema(notificationsServiceMethods.ByName("MarkNotificationAsRead")),
			connect.WithClientOptions(opts...),
		),
		markNotificationAsUnRead: connect.NewClient[v1.MarkNotificationAsUnReadRequest, v1.MarkNotificationAsUnReadResponse](
			httpClient,
			baseURL+NotificationsServiceMarkNotificationAsUnReadProcedure,
			connect.WithSchema(notificationsServiceMethods.ByName("MarkNotificationAsUnRead")),
			connect.WithClientOptions(opts...),
		),
		deleteNotificationById: connect.NewClient[v1.DeleteNotificationByIdRequest, v1.DeleteNotificationByIdResponse](
			httpClient,
			baseURL+NotificationsServiceDeleteNotificationByIdProcedure,
			connect.WithSchema(notificationsServiceMethods.ByName("DeleteNotificationById")),
			connect.WithClientOptions(opts...),
		),
		getNotificationCount: connect.NewClient[v1.NotificationCountRequest, v1.NotificationCountResponse](
			httpClient,
			baseURL+NotificationsServiceGetNotificationCountProcedure,
			connect.WithSchema(notificationsServiceMethods.ByName("GetNotificationCount")),
			connect.WithClientOptions(opts...),
		),
	}
}

// notificationsServiceClient implements NotificationsServiceClient.
type notificationsServiceClient struct {
	getUserNotifications     *connect.Client[v1.GetNotificationsRequest, v1.GetUserNotificationsResponse]
	getCreatorNotifications  *connect.Client[v1.GetNotificationsRequest, v1.GetUserNotificationsResponse]
	markNotificationAsRead   *connect.Client[v1.MarkNotificationAsReadRequest, v1.MarkNotificationAsReadResponse]
	markNotificationAsUnRead *connect.Client[v1.MarkNotificationAsUnReadRequest, v1.MarkNotificationAsUnReadResponse]
	deleteNotificationById   *connect.Client[v1.DeleteNotificationByIdRequest, v1.DeleteNotificationByIdResponse]
	getNotificationCount     *connect.Client[v1.NotificationCountRequest, v1.NotificationCountResponse]
}

// GetUserNotifications calls api.notifications.v1.NotificationsService.GetUserNotifications.
func (c *notificationsServiceClient) GetUserNotifications(ctx context.Context, req *connect.Request[v1.GetNotificationsRequest]) (*connect.Response[v1.GetUserNotificationsResponse], error) {
	return c.getUserNotifications.CallUnary(ctx, req)
}

// GetCreatorNotifications calls api.notifications.v1.NotificationsService.GetCreatorNotifications.
func (c *notificationsServiceClient) GetCreatorNotifications(ctx context.Context, req *connect.Request[v1.GetNotificationsRequest]) (*connect.Response[v1.GetUserNotificationsResponse], error) {
	return c.getCreatorNotifications.CallUnary(ctx, req)
}

// MarkNotificationAsRead calls api.notifications.v1.NotificationsService.MarkNotificationAsRead.
func (c *notificationsServiceClient) MarkNotificationAsRead(ctx context.Context, req *connect.Request[v1.MarkNotificationAsReadRequest]) (*connect.Response[v1.MarkNotificationAsReadResponse], error) {
	return c.markNotificationAsRead.CallUnary(ctx, req)
}

// MarkNotificationAsUnRead calls
// api.notifications.v1.NotificationsService.MarkNotificationAsUnRead.
func (c *notificationsServiceClient) MarkNotificationAsUnRead(ctx context.Context, req *connect.Request[v1.MarkNotificationAsUnReadRequest]) (*connect.Response[v1.MarkNotificationAsUnReadResponse], error) {
	return c.markNotificationAsUnRead.CallUnary(ctx, req)
}

// DeleteNotificationById calls api.notifications.v1.NotificationsService.DeleteNotificationById.
func (c *notificationsServiceClient) DeleteNotificationById(ctx context.Context, req *connect.Request[v1.DeleteNotificationByIdRequest]) (*connect.Response[v1.DeleteNotificationByIdResponse], error) {
	return c.deleteNotificationById.CallUnary(ctx, req)
}

// GetNotificationCount calls api.notifications.v1.NotificationsService.GetNotificationCount.
func (c *notificationsServiceClient) GetNotificationCount(ctx context.Context, req *connect.Request[v1.NotificationCountRequest]) (*connect.Response[v1.NotificationCountResponse], error) {
	return c.getNotificationCount.CallUnary(ctx, req)
}

// NotificationsServiceHandler is an implementation of the api.notifications.v1.NotificationsService
// service.
type NotificationsServiceHandler interface {
	GetUserNotifications(context.Context, *connect.Request[v1.GetNotificationsRequest]) (*connect.Response[v1.GetUserNotificationsResponse], error)
	GetCreatorNotifications(context.Context, *connect.Request[v1.GetNotificationsRequest]) (*connect.Response[v1.GetUserNotificationsResponse], error)
	MarkNotificationAsRead(context.Context, *connect.Request[v1.MarkNotificationAsReadRequest]) (*connect.Response[v1.MarkNotificationAsReadResponse], error)
	MarkNotificationAsUnRead(context.Context, *connect.Request[v1.MarkNotificationAsUnReadRequest]) (*connect.Response[v1.MarkNotificationAsUnReadResponse], error)
	DeleteNotificationById(context.Context, *connect.Request[v1.DeleteNotificationByIdRequest]) (*connect.Response[v1.DeleteNotificationByIdResponse], error)
	GetNotificationCount(context.Context, *connect.Request[v1.NotificationCountRequest]) (*connect.Response[v1.NotificationCountResponse], error)
}

// NewNotificationsServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewNotificationsServiceHandler(svc NotificationsServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	notificationsServiceMethods := v1.File_notifications_v1_notifications_proto.Services().ByName("NotificationsService").Methods()
	notificationsServiceGetUserNotificationsHandler := connect.NewUnaryHandler(
		NotificationsServiceGetUserNotificationsProcedure,
		svc.GetUserNotifications,
		connect.WithSchema(notificationsServiceMethods.ByName("GetUserNotifications")),
		connect.WithHandlerOptions(opts...),
	)
	notificationsServiceGetCreatorNotificationsHandler := connect.NewUnaryHandler(
		NotificationsServiceGetCreatorNotificationsProcedure,
		svc.GetCreatorNotifications,
		connect.WithSchema(notificationsServiceMethods.ByName("GetCreatorNotifications")),
		connect.WithHandlerOptions(opts...),
	)
	notificationsServiceMarkNotificationAsReadHandler := connect.NewUnaryHandler(
		NotificationsServiceMarkNotificationAsReadProcedure,
		svc.MarkNotificationAsRead,
		connect.WithSchema(notificationsServiceMethods.ByName("MarkNotificationAsRead")),
		connect.WithHandlerOptions(opts...),
	)
	notificationsServiceMarkNotificationAsUnReadHandler := connect.NewUnaryHandler(
		NotificationsServiceMarkNotificationAsUnReadProcedure,
		svc.MarkNotificationAsUnRead,
		connect.WithSchema(notificationsServiceMethods.ByName("MarkNotificationAsUnRead")),
		connect.WithHandlerOptions(opts...),
	)
	notificationsServiceDeleteNotificationByIdHandler := connect.NewUnaryHandler(
		NotificationsServiceDeleteNotificationByIdProcedure,
		svc.DeleteNotificationById,
		connect.WithSchema(notificationsServiceMethods.ByName("DeleteNotificationById")),
		connect.WithHandlerOptions(opts...),
	)
	notificationsServiceGetNotificationCountHandler := connect.NewUnaryHandler(
		NotificationsServiceGetNotificationCountProcedure,
		svc.GetNotificationCount,
		connect.WithSchema(notificationsServiceMethods.ByName("GetNotificationCount")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.notifications.v1.NotificationsService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case NotificationsServiceGetUserNotificationsProcedure:
			notificationsServiceGetUserNotificationsHandler.ServeHTTP(w, r)
		case NotificationsServiceGetCreatorNotificationsProcedure:
			notificationsServiceGetCreatorNotificationsHandler.ServeHTTP(w, r)
		case NotificationsServiceMarkNotificationAsReadProcedure:
			notificationsServiceMarkNotificationAsReadHandler.ServeHTTP(w, r)
		case NotificationsServiceMarkNotificationAsUnReadProcedure:
			notificationsServiceMarkNotificationAsUnReadHandler.ServeHTTP(w, r)
		case NotificationsServiceDeleteNotificationByIdProcedure:
			notificationsServiceDeleteNotificationByIdHandler.ServeHTTP(w, r)
		case NotificationsServiceGetNotificationCountProcedure:
			notificationsServiceGetNotificationCountHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedNotificationsServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedNotificationsServiceHandler struct{}

func (UnimplementedNotificationsServiceHandler) GetUserNotifications(context.Context, *connect.Request[v1.GetNotificationsRequest]) (*connect.Response[v1.GetUserNotificationsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.notifications.v1.NotificationsService.GetUserNotifications is not implemented"))
}

func (UnimplementedNotificationsServiceHandler) GetCreatorNotifications(context.Context, *connect.Request[v1.GetNotificationsRequest]) (*connect.Response[v1.GetUserNotificationsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.notifications.v1.NotificationsService.GetCreatorNotifications is not implemented"))
}

func (UnimplementedNotificationsServiceHandler) MarkNotificationAsRead(context.Context, *connect.Request[v1.MarkNotificationAsReadRequest]) (*connect.Response[v1.MarkNotificationAsReadResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.notifications.v1.NotificationsService.MarkNotificationAsRead is not implemented"))
}

func (UnimplementedNotificationsServiceHandler) MarkNotificationAsUnRead(context.Context, *connect.Request[v1.MarkNotificationAsUnReadRequest]) (*connect.Response[v1.MarkNotificationAsUnReadResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.notifications.v1.NotificationsService.MarkNotificationAsUnRead is not implemented"))
}

func (UnimplementedNotificationsServiceHandler) DeleteNotificationById(context.Context, *connect.Request[v1.DeleteNotificationByIdRequest]) (*connect.Response[v1.DeleteNotificationByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.notifications.v1.NotificationsService.DeleteNotificationById is not implemented"))
}

func (UnimplementedNotificationsServiceHandler) GetNotificationCount(context.Context, *connect.Request[v1.NotificationCountRequest]) (*connect.Response[v1.NotificationCountResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.notifications.v1.NotificationsService.GetNotificationCount is not implemented"))
}
