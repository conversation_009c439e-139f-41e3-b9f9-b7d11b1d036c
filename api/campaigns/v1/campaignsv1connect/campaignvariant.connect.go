// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: campaigns/v1/campaignvariant.proto

package campaignsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/campaigns/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CampaignVariantServiceName is the fully-qualified name of the CampaignVariantService service.
	CampaignVariantServiceName = "api.campaigns.v1.CampaignVariantService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CampaignVariantServiceAddCampaignVariantProcedure is the fully-qualified name of the
	// CampaignVariantService's AddCampaignVariant RPC.
	CampaignVariantServiceAddCampaignVariantProcedure = "/api.campaigns.v1.CampaignVariantService/AddCampaignVariant"
	// CampaignVariantServiceGetAllCampaignVariantsProcedure is the fully-qualified name of the
	// CampaignVariantService's GetAllCampaignVariants RPC.
	CampaignVariantServiceGetAllCampaignVariantsProcedure = "/api.campaigns.v1.CampaignVariantService/GetAllCampaignVariants"
	// CampaignVariantServiceGetCampaignVariantByIdProcedure is the fully-qualified name of the
	// CampaignVariantService's GetCampaignVariantById RPC.
	CampaignVariantServiceGetCampaignVariantByIdProcedure = "/api.campaigns.v1.CampaignVariantService/GetCampaignVariantById"
	// CampaignVariantServiceDeleteCampaignVariantByIdProcedure is the fully-qualified name of the
	// CampaignVariantService's DeleteCampaignVariantById RPC.
	CampaignVariantServiceDeleteCampaignVariantByIdProcedure = "/api.campaigns.v1.CampaignVariantService/DeleteCampaignVariantById"
	// CampaignVariantServiceUpdateCampaignVariantByIdProcedure is the fully-qualified name of the
	// CampaignVariantService's UpdateCampaignVariantById RPC.
	CampaignVariantServiceUpdateCampaignVariantByIdProcedure = "/api.campaigns.v1.CampaignVariantService/UpdateCampaignVariantById"
	// CampaignVariantServiceGetCampaignVariantSubsProcedure is the fully-qualified name of the
	// CampaignVariantService's GetCampaignVariantSubs RPC.
	CampaignVariantServiceGetCampaignVariantSubsProcedure = "/api.campaigns.v1.CampaignVariantService/GetCampaignVariantSubs"
)

// CampaignVariantServiceClient is a client for the api.campaigns.v1.CampaignVariantService service.
type CampaignVariantServiceClient interface {
	AddCampaignVariant(context.Context, *connect.Request[v1.AddCampaignVariantRequest]) (*connect.Response[v1.AddCampaignVariantResponse], error)
	GetAllCampaignVariants(context.Context, *connect.Request[v1.GetAllCampaignVariantsRequest]) (*connect.Response[v1.GetAllCampaignVariantsResponse], error)
	GetCampaignVariantById(context.Context, *connect.Request[v1.GetCampaignVariantByIdRequest]) (*connect.Response[v1.GetCampaignVariantByIdResponse], error)
	DeleteCampaignVariantById(context.Context, *connect.Request[v1.DeleteCampaignVariantByIdRequest]) (*connect.Response[v1.DeleteCampaignVariantByIdResponse], error)
	UpdateCampaignVariantById(context.Context, *connect.Request[v1.UpdateCampaignVariantByIdRequest]) (*connect.Response[v1.UpdateCampaignVariantByIdResponse], error)
	GetCampaignVariantSubs(context.Context, *connect.Request[v1.GetCampaignSubRequest]) (*connect.Response[v1.GetCampaignSubResponse], error)
}

// NewCampaignVariantServiceClient constructs a client for the
// api.campaigns.v1.CampaignVariantService service. By default, it uses the Connect protocol with
// the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To use
// the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCampaignVariantServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CampaignVariantServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	campaignVariantServiceMethods := v1.File_campaigns_v1_campaignvariant_proto.Services().ByName("CampaignVariantService").Methods()
	return &campaignVariantServiceClient{
		addCampaignVariant: connect.NewClient[v1.AddCampaignVariantRequest, v1.AddCampaignVariantResponse](
			httpClient,
			baseURL+CampaignVariantServiceAddCampaignVariantProcedure,
			connect.WithSchema(campaignVariantServiceMethods.ByName("AddCampaignVariant")),
			connect.WithClientOptions(opts...),
		),
		getAllCampaignVariants: connect.NewClient[v1.GetAllCampaignVariantsRequest, v1.GetAllCampaignVariantsResponse](
			httpClient,
			baseURL+CampaignVariantServiceGetAllCampaignVariantsProcedure,
			connect.WithSchema(campaignVariantServiceMethods.ByName("GetAllCampaignVariants")),
			connect.WithClientOptions(opts...),
		),
		getCampaignVariantById: connect.NewClient[v1.GetCampaignVariantByIdRequest, v1.GetCampaignVariantByIdResponse](
			httpClient,
			baseURL+CampaignVariantServiceGetCampaignVariantByIdProcedure,
			connect.WithSchema(campaignVariantServiceMethods.ByName("GetCampaignVariantById")),
			connect.WithClientOptions(opts...),
		),
		deleteCampaignVariantById: connect.NewClient[v1.DeleteCampaignVariantByIdRequest, v1.DeleteCampaignVariantByIdResponse](
			httpClient,
			baseURL+CampaignVariantServiceDeleteCampaignVariantByIdProcedure,
			connect.WithSchema(campaignVariantServiceMethods.ByName("DeleteCampaignVariantById")),
			connect.WithClientOptions(opts...),
		),
		updateCampaignVariantById: connect.NewClient[v1.UpdateCampaignVariantByIdRequest, v1.UpdateCampaignVariantByIdResponse](
			httpClient,
			baseURL+CampaignVariantServiceUpdateCampaignVariantByIdProcedure,
			connect.WithSchema(campaignVariantServiceMethods.ByName("UpdateCampaignVariantById")),
			connect.WithClientOptions(opts...),
		),
		getCampaignVariantSubs: connect.NewClient[v1.GetCampaignSubRequest, v1.GetCampaignSubResponse](
			httpClient,
			baseURL+CampaignVariantServiceGetCampaignVariantSubsProcedure,
			connect.WithSchema(campaignVariantServiceMethods.ByName("GetCampaignVariantSubs")),
			connect.WithClientOptions(opts...),
		),
	}
}

// campaignVariantServiceClient implements CampaignVariantServiceClient.
type campaignVariantServiceClient struct {
	addCampaignVariant        *connect.Client[v1.AddCampaignVariantRequest, v1.AddCampaignVariantResponse]
	getAllCampaignVariants    *connect.Client[v1.GetAllCampaignVariantsRequest, v1.GetAllCampaignVariantsResponse]
	getCampaignVariantById    *connect.Client[v1.GetCampaignVariantByIdRequest, v1.GetCampaignVariantByIdResponse]
	deleteCampaignVariantById *connect.Client[v1.DeleteCampaignVariantByIdRequest, v1.DeleteCampaignVariantByIdResponse]
	updateCampaignVariantById *connect.Client[v1.UpdateCampaignVariantByIdRequest, v1.UpdateCampaignVariantByIdResponse]
	getCampaignVariantSubs    *connect.Client[v1.GetCampaignSubRequest, v1.GetCampaignSubResponse]
}

// AddCampaignVariant calls api.campaigns.v1.CampaignVariantService.AddCampaignVariant.
func (c *campaignVariantServiceClient) AddCampaignVariant(ctx context.Context, req *connect.Request[v1.AddCampaignVariantRequest]) (*connect.Response[v1.AddCampaignVariantResponse], error) {
	return c.addCampaignVariant.CallUnary(ctx, req)
}

// GetAllCampaignVariants calls api.campaigns.v1.CampaignVariantService.GetAllCampaignVariants.
func (c *campaignVariantServiceClient) GetAllCampaignVariants(ctx context.Context, req *connect.Request[v1.GetAllCampaignVariantsRequest]) (*connect.Response[v1.GetAllCampaignVariantsResponse], error) {
	return c.getAllCampaignVariants.CallUnary(ctx, req)
}

// GetCampaignVariantById calls api.campaigns.v1.CampaignVariantService.GetCampaignVariantById.
func (c *campaignVariantServiceClient) GetCampaignVariantById(ctx context.Context, req *connect.Request[v1.GetCampaignVariantByIdRequest]) (*connect.Response[v1.GetCampaignVariantByIdResponse], error) {
	return c.getCampaignVariantById.CallUnary(ctx, req)
}

// DeleteCampaignVariantById calls
// api.campaigns.v1.CampaignVariantService.DeleteCampaignVariantById.
func (c *campaignVariantServiceClient) DeleteCampaignVariantById(ctx context.Context, req *connect.Request[v1.DeleteCampaignVariantByIdRequest]) (*connect.Response[v1.DeleteCampaignVariantByIdResponse], error) {
	return c.deleteCampaignVariantById.CallUnary(ctx, req)
}

// UpdateCampaignVariantById calls
// api.campaigns.v1.CampaignVariantService.UpdateCampaignVariantById.
func (c *campaignVariantServiceClient) UpdateCampaignVariantById(ctx context.Context, req *connect.Request[v1.UpdateCampaignVariantByIdRequest]) (*connect.Response[v1.UpdateCampaignVariantByIdResponse], error) {
	return c.updateCampaignVariantById.CallUnary(ctx, req)
}

// GetCampaignVariantSubs calls api.campaigns.v1.CampaignVariantService.GetCampaignVariantSubs.
func (c *campaignVariantServiceClient) GetCampaignVariantSubs(ctx context.Context, req *connect.Request[v1.GetCampaignSubRequest]) (*connect.Response[v1.GetCampaignSubResponse], error) {
	return c.getCampaignVariantSubs.CallUnary(ctx, req)
}

// CampaignVariantServiceHandler is an implementation of the api.campaigns.v1.CampaignVariantService
// service.
type CampaignVariantServiceHandler interface {
	AddCampaignVariant(context.Context, *connect.Request[v1.AddCampaignVariantRequest]) (*connect.Response[v1.AddCampaignVariantResponse], error)
	GetAllCampaignVariants(context.Context, *connect.Request[v1.GetAllCampaignVariantsRequest]) (*connect.Response[v1.GetAllCampaignVariantsResponse], error)
	GetCampaignVariantById(context.Context, *connect.Request[v1.GetCampaignVariantByIdRequest]) (*connect.Response[v1.GetCampaignVariantByIdResponse], error)
	DeleteCampaignVariantById(context.Context, *connect.Request[v1.DeleteCampaignVariantByIdRequest]) (*connect.Response[v1.DeleteCampaignVariantByIdResponse], error)
	UpdateCampaignVariantById(context.Context, *connect.Request[v1.UpdateCampaignVariantByIdRequest]) (*connect.Response[v1.UpdateCampaignVariantByIdResponse], error)
	GetCampaignVariantSubs(context.Context, *connect.Request[v1.GetCampaignSubRequest]) (*connect.Response[v1.GetCampaignSubResponse], error)
}

// NewCampaignVariantServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCampaignVariantServiceHandler(svc CampaignVariantServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	campaignVariantServiceMethods := v1.File_campaigns_v1_campaignvariant_proto.Services().ByName("CampaignVariantService").Methods()
	campaignVariantServiceAddCampaignVariantHandler := connect.NewUnaryHandler(
		CampaignVariantServiceAddCampaignVariantProcedure,
		svc.AddCampaignVariant,
		connect.WithSchema(campaignVariantServiceMethods.ByName("AddCampaignVariant")),
		connect.WithHandlerOptions(opts...),
	)
	campaignVariantServiceGetAllCampaignVariantsHandler := connect.NewUnaryHandler(
		CampaignVariantServiceGetAllCampaignVariantsProcedure,
		svc.GetAllCampaignVariants,
		connect.WithSchema(campaignVariantServiceMethods.ByName("GetAllCampaignVariants")),
		connect.WithHandlerOptions(opts...),
	)
	campaignVariantServiceGetCampaignVariantByIdHandler := connect.NewUnaryHandler(
		CampaignVariantServiceGetCampaignVariantByIdProcedure,
		svc.GetCampaignVariantById,
		connect.WithSchema(campaignVariantServiceMethods.ByName("GetCampaignVariantById")),
		connect.WithHandlerOptions(opts...),
	)
	campaignVariantServiceDeleteCampaignVariantByIdHandler := connect.NewUnaryHandler(
		CampaignVariantServiceDeleteCampaignVariantByIdProcedure,
		svc.DeleteCampaignVariantById,
		connect.WithSchema(campaignVariantServiceMethods.ByName("DeleteCampaignVariantById")),
		connect.WithHandlerOptions(opts...),
	)
	campaignVariantServiceUpdateCampaignVariantByIdHandler := connect.NewUnaryHandler(
		CampaignVariantServiceUpdateCampaignVariantByIdProcedure,
		svc.UpdateCampaignVariantById,
		connect.WithSchema(campaignVariantServiceMethods.ByName("UpdateCampaignVariantById")),
		connect.WithHandlerOptions(opts...),
	)
	campaignVariantServiceGetCampaignVariantSubsHandler := connect.NewUnaryHandler(
		CampaignVariantServiceGetCampaignVariantSubsProcedure,
		svc.GetCampaignVariantSubs,
		connect.WithSchema(campaignVariantServiceMethods.ByName("GetCampaignVariantSubs")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.campaigns.v1.CampaignVariantService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CampaignVariantServiceAddCampaignVariantProcedure:
			campaignVariantServiceAddCampaignVariantHandler.ServeHTTP(w, r)
		case CampaignVariantServiceGetAllCampaignVariantsProcedure:
			campaignVariantServiceGetAllCampaignVariantsHandler.ServeHTTP(w, r)
		case CampaignVariantServiceGetCampaignVariantByIdProcedure:
			campaignVariantServiceGetCampaignVariantByIdHandler.ServeHTTP(w, r)
		case CampaignVariantServiceDeleteCampaignVariantByIdProcedure:
			campaignVariantServiceDeleteCampaignVariantByIdHandler.ServeHTTP(w, r)
		case CampaignVariantServiceUpdateCampaignVariantByIdProcedure:
			campaignVariantServiceUpdateCampaignVariantByIdHandler.ServeHTTP(w, r)
		case CampaignVariantServiceGetCampaignVariantSubsProcedure:
			campaignVariantServiceGetCampaignVariantSubsHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCampaignVariantServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCampaignVariantServiceHandler struct{}

func (UnimplementedCampaignVariantServiceHandler) AddCampaignVariant(context.Context, *connect.Request[v1.AddCampaignVariantRequest]) (*connect.Response[v1.AddCampaignVariantResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignVariantService.AddCampaignVariant is not implemented"))
}

func (UnimplementedCampaignVariantServiceHandler) GetAllCampaignVariants(context.Context, *connect.Request[v1.GetAllCampaignVariantsRequest]) (*connect.Response[v1.GetAllCampaignVariantsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignVariantService.GetAllCampaignVariants is not implemented"))
}

func (UnimplementedCampaignVariantServiceHandler) GetCampaignVariantById(context.Context, *connect.Request[v1.GetCampaignVariantByIdRequest]) (*connect.Response[v1.GetCampaignVariantByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignVariantService.GetCampaignVariantById is not implemented"))
}

func (UnimplementedCampaignVariantServiceHandler) DeleteCampaignVariantById(context.Context, *connect.Request[v1.DeleteCampaignVariantByIdRequest]) (*connect.Response[v1.DeleteCampaignVariantByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignVariantService.DeleteCampaignVariantById is not implemented"))
}

func (UnimplementedCampaignVariantServiceHandler) UpdateCampaignVariantById(context.Context, *connect.Request[v1.UpdateCampaignVariantByIdRequest]) (*connect.Response[v1.UpdateCampaignVariantByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignVariantService.UpdateCampaignVariantById is not implemented"))
}

func (UnimplementedCampaignVariantServiceHandler) GetCampaignVariantSubs(context.Context, *connect.Request[v1.GetCampaignSubRequest]) (*connect.Response[v1.GetCampaignSubResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignVariantService.GetCampaignVariantSubs is not implemented"))
}
