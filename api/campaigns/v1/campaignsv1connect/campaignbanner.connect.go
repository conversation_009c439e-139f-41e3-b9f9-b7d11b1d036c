// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: campaigns/v1/campaignbanner.proto

package campaignsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/campaigns/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CampaignBannerServiceName is the fully-qualified name of the CampaignBannerService service.
	CampaignBannerServiceName = "api.campaigns.v1.CampaignBannerService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CampaignBannerServiceAddCampaignBannerProcedure is the fully-qualified name of the
	// CampaignBannerService's AddCampaignBanner RPC.
	CampaignBannerServiceAddCampaignBannerProcedure = "/api.campaigns.v1.CampaignBannerService/AddCampaignBanner"
	// CampaignBannerServiceGetCampaignBannerByIdProcedure is the fully-qualified name of the
	// CampaignBannerService's GetCampaignBannerById RPC.
	CampaignBannerServiceGetCampaignBannerByIdProcedure = "/api.campaigns.v1.CampaignBannerService/GetCampaignBannerById"
	// CampaignBannerServiceDeleteCampaignBannerByIdProcedure is the fully-qualified name of the
	// CampaignBannerService's DeleteCampaignBannerById RPC.
	CampaignBannerServiceDeleteCampaignBannerByIdProcedure = "/api.campaigns.v1.CampaignBannerService/DeleteCampaignBannerById"
	// CampaignBannerServiceUpdateCampaignBannerByIdProcedure is the fully-qualified name of the
	// CampaignBannerService's UpdateCampaignBannerById RPC.
	CampaignBannerServiceUpdateCampaignBannerByIdProcedure = "/api.campaigns.v1.CampaignBannerService/UpdateCampaignBannerById"
	// CampaignBannerServiceGetBannerByCampaignIdProcedure is the fully-qualified name of the
	// CampaignBannerService's GetBannerByCampaignId RPC.
	CampaignBannerServiceGetBannerByCampaignIdProcedure = "/api.campaigns.v1.CampaignBannerService/GetBannerByCampaignId"
)

// CampaignBannerServiceClient is a client for the api.campaigns.v1.CampaignBannerService service.
type CampaignBannerServiceClient interface {
	AddCampaignBanner(context.Context, *connect.Request[v1.AddCampaignBannerRequest]) (*connect.Response[v1.AddCampaignBannerResponse], error)
	GetCampaignBannerById(context.Context, *connect.Request[v1.GetCampaignBannerByIdRequest]) (*connect.Response[v1.GetCampaignBannerByIdResponse], error)
	DeleteCampaignBannerById(context.Context, *connect.Request[v1.DeleteCampaignBannerByIdRequest]) (*connect.Response[v1.DeleteCampaignBannerResponse], error)
	UpdateCampaignBannerById(context.Context, *connect.Request[v1.UpdateCampaignBannerByIdRequest]) (*connect.Response[v1.UpdateCampaignBannerResponse], error)
	GetBannerByCampaignId(context.Context, *connect.Request[v1.GetBannerByCampaignIdRequest]) (*connect.Response[v1.GetBannerByCampaignIdResponse], error)
}

// NewCampaignBannerServiceClient constructs a client for the api.campaigns.v1.CampaignBannerService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCampaignBannerServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CampaignBannerServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	campaignBannerServiceMethods := v1.File_campaigns_v1_campaignbanner_proto.Services().ByName("CampaignBannerService").Methods()
	return &campaignBannerServiceClient{
		addCampaignBanner: connect.NewClient[v1.AddCampaignBannerRequest, v1.AddCampaignBannerResponse](
			httpClient,
			baseURL+CampaignBannerServiceAddCampaignBannerProcedure,
			connect.WithSchema(campaignBannerServiceMethods.ByName("AddCampaignBanner")),
			connect.WithClientOptions(opts...),
		),
		getCampaignBannerById: connect.NewClient[v1.GetCampaignBannerByIdRequest, v1.GetCampaignBannerByIdResponse](
			httpClient,
			baseURL+CampaignBannerServiceGetCampaignBannerByIdProcedure,
			connect.WithSchema(campaignBannerServiceMethods.ByName("GetCampaignBannerById")),
			connect.WithClientOptions(opts...),
		),
		deleteCampaignBannerById: connect.NewClient[v1.DeleteCampaignBannerByIdRequest, v1.DeleteCampaignBannerResponse](
			httpClient,
			baseURL+CampaignBannerServiceDeleteCampaignBannerByIdProcedure,
			connect.WithSchema(campaignBannerServiceMethods.ByName("DeleteCampaignBannerById")),
			connect.WithClientOptions(opts...),
		),
		updateCampaignBannerById: connect.NewClient[v1.UpdateCampaignBannerByIdRequest, v1.UpdateCampaignBannerResponse](
			httpClient,
			baseURL+CampaignBannerServiceUpdateCampaignBannerByIdProcedure,
			connect.WithSchema(campaignBannerServiceMethods.ByName("UpdateCampaignBannerById")),
			connect.WithClientOptions(opts...),
		),
		getBannerByCampaignId: connect.NewClient[v1.GetBannerByCampaignIdRequest, v1.GetBannerByCampaignIdResponse](
			httpClient,
			baseURL+CampaignBannerServiceGetBannerByCampaignIdProcedure,
			connect.WithSchema(campaignBannerServiceMethods.ByName("GetBannerByCampaignId")),
			connect.WithClientOptions(opts...),
		),
	}
}

// campaignBannerServiceClient implements CampaignBannerServiceClient.
type campaignBannerServiceClient struct {
	addCampaignBanner        *connect.Client[v1.AddCampaignBannerRequest, v1.AddCampaignBannerResponse]
	getCampaignBannerById    *connect.Client[v1.GetCampaignBannerByIdRequest, v1.GetCampaignBannerByIdResponse]
	deleteCampaignBannerById *connect.Client[v1.DeleteCampaignBannerByIdRequest, v1.DeleteCampaignBannerResponse]
	updateCampaignBannerById *connect.Client[v1.UpdateCampaignBannerByIdRequest, v1.UpdateCampaignBannerResponse]
	getBannerByCampaignId    *connect.Client[v1.GetBannerByCampaignIdRequest, v1.GetBannerByCampaignIdResponse]
}

// AddCampaignBanner calls api.campaigns.v1.CampaignBannerService.AddCampaignBanner.
func (c *campaignBannerServiceClient) AddCampaignBanner(ctx context.Context, req *connect.Request[v1.AddCampaignBannerRequest]) (*connect.Response[v1.AddCampaignBannerResponse], error) {
	return c.addCampaignBanner.CallUnary(ctx, req)
}

// GetCampaignBannerById calls api.campaigns.v1.CampaignBannerService.GetCampaignBannerById.
func (c *campaignBannerServiceClient) GetCampaignBannerById(ctx context.Context, req *connect.Request[v1.GetCampaignBannerByIdRequest]) (*connect.Response[v1.GetCampaignBannerByIdResponse], error) {
	return c.getCampaignBannerById.CallUnary(ctx, req)
}

// DeleteCampaignBannerById calls api.campaigns.v1.CampaignBannerService.DeleteCampaignBannerById.
func (c *campaignBannerServiceClient) DeleteCampaignBannerById(ctx context.Context, req *connect.Request[v1.DeleteCampaignBannerByIdRequest]) (*connect.Response[v1.DeleteCampaignBannerResponse], error) {
	return c.deleteCampaignBannerById.CallUnary(ctx, req)
}

// UpdateCampaignBannerById calls api.campaigns.v1.CampaignBannerService.UpdateCampaignBannerById.
func (c *campaignBannerServiceClient) UpdateCampaignBannerById(ctx context.Context, req *connect.Request[v1.UpdateCampaignBannerByIdRequest]) (*connect.Response[v1.UpdateCampaignBannerResponse], error) {
	return c.updateCampaignBannerById.CallUnary(ctx, req)
}

// GetBannerByCampaignId calls api.campaigns.v1.CampaignBannerService.GetBannerByCampaignId.
func (c *campaignBannerServiceClient) GetBannerByCampaignId(ctx context.Context, req *connect.Request[v1.GetBannerByCampaignIdRequest]) (*connect.Response[v1.GetBannerByCampaignIdResponse], error) {
	return c.getBannerByCampaignId.CallUnary(ctx, req)
}

// CampaignBannerServiceHandler is an implementation of the api.campaigns.v1.CampaignBannerService
// service.
type CampaignBannerServiceHandler interface {
	AddCampaignBanner(context.Context, *connect.Request[v1.AddCampaignBannerRequest]) (*connect.Response[v1.AddCampaignBannerResponse], error)
	GetCampaignBannerById(context.Context, *connect.Request[v1.GetCampaignBannerByIdRequest]) (*connect.Response[v1.GetCampaignBannerByIdResponse], error)
	DeleteCampaignBannerById(context.Context, *connect.Request[v1.DeleteCampaignBannerByIdRequest]) (*connect.Response[v1.DeleteCampaignBannerResponse], error)
	UpdateCampaignBannerById(context.Context, *connect.Request[v1.UpdateCampaignBannerByIdRequest]) (*connect.Response[v1.UpdateCampaignBannerResponse], error)
	GetBannerByCampaignId(context.Context, *connect.Request[v1.GetBannerByCampaignIdRequest]) (*connect.Response[v1.GetBannerByCampaignIdResponse], error)
}

// NewCampaignBannerServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCampaignBannerServiceHandler(svc CampaignBannerServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	campaignBannerServiceMethods := v1.File_campaigns_v1_campaignbanner_proto.Services().ByName("CampaignBannerService").Methods()
	campaignBannerServiceAddCampaignBannerHandler := connect.NewUnaryHandler(
		CampaignBannerServiceAddCampaignBannerProcedure,
		svc.AddCampaignBanner,
		connect.WithSchema(campaignBannerServiceMethods.ByName("AddCampaignBanner")),
		connect.WithHandlerOptions(opts...),
	)
	campaignBannerServiceGetCampaignBannerByIdHandler := connect.NewUnaryHandler(
		CampaignBannerServiceGetCampaignBannerByIdProcedure,
		svc.GetCampaignBannerById,
		connect.WithSchema(campaignBannerServiceMethods.ByName("GetCampaignBannerById")),
		connect.WithHandlerOptions(opts...),
	)
	campaignBannerServiceDeleteCampaignBannerByIdHandler := connect.NewUnaryHandler(
		CampaignBannerServiceDeleteCampaignBannerByIdProcedure,
		svc.DeleteCampaignBannerById,
		connect.WithSchema(campaignBannerServiceMethods.ByName("DeleteCampaignBannerById")),
		connect.WithHandlerOptions(opts...),
	)
	campaignBannerServiceUpdateCampaignBannerByIdHandler := connect.NewUnaryHandler(
		CampaignBannerServiceUpdateCampaignBannerByIdProcedure,
		svc.UpdateCampaignBannerById,
		connect.WithSchema(campaignBannerServiceMethods.ByName("UpdateCampaignBannerById")),
		connect.WithHandlerOptions(opts...),
	)
	campaignBannerServiceGetBannerByCampaignIdHandler := connect.NewUnaryHandler(
		CampaignBannerServiceGetBannerByCampaignIdProcedure,
		svc.GetBannerByCampaignId,
		connect.WithSchema(campaignBannerServiceMethods.ByName("GetBannerByCampaignId")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.campaigns.v1.CampaignBannerService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CampaignBannerServiceAddCampaignBannerProcedure:
			campaignBannerServiceAddCampaignBannerHandler.ServeHTTP(w, r)
		case CampaignBannerServiceGetCampaignBannerByIdProcedure:
			campaignBannerServiceGetCampaignBannerByIdHandler.ServeHTTP(w, r)
		case CampaignBannerServiceDeleteCampaignBannerByIdProcedure:
			campaignBannerServiceDeleteCampaignBannerByIdHandler.ServeHTTP(w, r)
		case CampaignBannerServiceUpdateCampaignBannerByIdProcedure:
			campaignBannerServiceUpdateCampaignBannerByIdHandler.ServeHTTP(w, r)
		case CampaignBannerServiceGetBannerByCampaignIdProcedure:
			campaignBannerServiceGetBannerByCampaignIdHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCampaignBannerServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCampaignBannerServiceHandler struct{}

func (UnimplementedCampaignBannerServiceHandler) AddCampaignBanner(context.Context, *connect.Request[v1.AddCampaignBannerRequest]) (*connect.Response[v1.AddCampaignBannerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignBannerService.AddCampaignBanner is not implemented"))
}

func (UnimplementedCampaignBannerServiceHandler) GetCampaignBannerById(context.Context, *connect.Request[v1.GetCampaignBannerByIdRequest]) (*connect.Response[v1.GetCampaignBannerByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignBannerService.GetCampaignBannerById is not implemented"))
}

func (UnimplementedCampaignBannerServiceHandler) DeleteCampaignBannerById(context.Context, *connect.Request[v1.DeleteCampaignBannerByIdRequest]) (*connect.Response[v1.DeleteCampaignBannerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignBannerService.DeleteCampaignBannerById is not implemented"))
}

func (UnimplementedCampaignBannerServiceHandler) UpdateCampaignBannerById(context.Context, *connect.Request[v1.UpdateCampaignBannerByIdRequest]) (*connect.Response[v1.UpdateCampaignBannerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignBannerService.UpdateCampaignBannerById is not implemented"))
}

func (UnimplementedCampaignBannerServiceHandler) GetBannerByCampaignId(context.Context, *connect.Request[v1.GetBannerByCampaignIdRequest]) (*connect.Response[v1.GetBannerByCampaignIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.campaigns.v1.CampaignBannerService.GetBannerByCampaignId is not implemented"))
}
