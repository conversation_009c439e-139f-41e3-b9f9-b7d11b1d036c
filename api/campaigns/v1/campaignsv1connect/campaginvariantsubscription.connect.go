// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: campaigns/v1/campaginvariantsubscription.proto

package campaignsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/campaigns/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CampaignVariantSubscriptionServiceName is the fully-qualified name of the
	// CampaignVariantSubscriptionService service.
	CampaignVariantSubscriptionServiceName = "api.camapigns.v1.CampaignVariantSubscriptionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CampaignVariantSubscriptionServiceAddCampaignVariantSubscriptionProcedure is the fully-qualified
	// name of the CampaignVariantSubscriptionService's AddCampaignVariantSubscription RPC.
	CampaignVariantSubscriptionServiceAddCampaignVariantSubscriptionProcedure = "/api.camapigns.v1.CampaignVariantSubscriptionService/AddCampaignVariantSubscription"
)

// CampaignVariantSubscriptionServiceClient is a client for the
// api.camapigns.v1.CampaignVariantSubscriptionService service.
type CampaignVariantSubscriptionServiceClient interface {
	AddCampaignVariantSubscription(context.Context, *connect.Request[v1.AddCampaignVariantSubscriptionRequest]) (*connect.Response[v1.AddCampaignVariantSubscriptionResponse], error)
}

// NewCampaignVariantSubscriptionServiceClient constructs a client for the
// api.camapigns.v1.CampaignVariantSubscriptionService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCampaignVariantSubscriptionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CampaignVariantSubscriptionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	campaignVariantSubscriptionServiceMethods := v1.File_campaigns_v1_campaginvariantsubscription_proto.Services().ByName("CampaignVariantSubscriptionService").Methods()
	return &campaignVariantSubscriptionServiceClient{
		addCampaignVariantSubscription: connect.NewClient[v1.AddCampaignVariantSubscriptionRequest, v1.AddCampaignVariantSubscriptionResponse](
			httpClient,
			baseURL+CampaignVariantSubscriptionServiceAddCampaignVariantSubscriptionProcedure,
			connect.WithSchema(campaignVariantSubscriptionServiceMethods.ByName("AddCampaignVariantSubscription")),
			connect.WithClientOptions(opts...),
		),
	}
}

// campaignVariantSubscriptionServiceClient implements CampaignVariantSubscriptionServiceClient.
type campaignVariantSubscriptionServiceClient struct {
	addCampaignVariantSubscription *connect.Client[v1.AddCampaignVariantSubscriptionRequest, v1.AddCampaignVariantSubscriptionResponse]
}

// AddCampaignVariantSubscription calls
// api.camapigns.v1.CampaignVariantSubscriptionService.AddCampaignVariantSubscription.
func (c *campaignVariantSubscriptionServiceClient) AddCampaignVariantSubscription(ctx context.Context, req *connect.Request[v1.AddCampaignVariantSubscriptionRequest]) (*connect.Response[v1.AddCampaignVariantSubscriptionResponse], error) {
	return c.addCampaignVariantSubscription.CallUnary(ctx, req)
}

// CampaignVariantSubscriptionServiceHandler is an implementation of the
// api.camapigns.v1.CampaignVariantSubscriptionService service.
type CampaignVariantSubscriptionServiceHandler interface {
	AddCampaignVariantSubscription(context.Context, *connect.Request[v1.AddCampaignVariantSubscriptionRequest]) (*connect.Response[v1.AddCampaignVariantSubscriptionResponse], error)
}

// NewCampaignVariantSubscriptionServiceHandler builds an HTTP handler from the service
// implementation. It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCampaignVariantSubscriptionServiceHandler(svc CampaignVariantSubscriptionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	campaignVariantSubscriptionServiceMethods := v1.File_campaigns_v1_campaginvariantsubscription_proto.Services().ByName("CampaignVariantSubscriptionService").Methods()
	campaignVariantSubscriptionServiceAddCampaignVariantSubscriptionHandler := connect.NewUnaryHandler(
		CampaignVariantSubscriptionServiceAddCampaignVariantSubscriptionProcedure,
		svc.AddCampaignVariantSubscription,
		connect.WithSchema(campaignVariantSubscriptionServiceMethods.ByName("AddCampaignVariantSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.camapigns.v1.CampaignVariantSubscriptionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CampaignVariantSubscriptionServiceAddCampaignVariantSubscriptionProcedure:
			campaignVariantSubscriptionServiceAddCampaignVariantSubscriptionHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCampaignVariantSubscriptionServiceHandler returns CodeUnimplemented from all
// methods.
type UnimplementedCampaignVariantSubscriptionServiceHandler struct{}

func (UnimplementedCampaignVariantSubscriptionServiceHandler) AddCampaignVariantSubscription(context.Context, *connect.Request[v1.AddCampaignVariantSubscriptionRequest]) (*connect.Response[v1.AddCampaignVariantSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.camapigns.v1.CampaignVariantSubscriptionService.AddCampaignVariantSubscription is not implemented"))
}
