// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: campaigns/v1/campaginvariantsubscription.proto

package campaignsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddCampaignVariantSubscriptionRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CampaignVariantId string                 `protobuf:"bytes,1,opt,name=campaign_variant_id,json=campaignVariantId,proto3" json:"campaign_variant_id,omitempty" validate:"required"`
	UserBillingInfoId string                 `protobuf:"bytes,2,opt,name=user_billing_info_id,json=userBillingInfoId,proto3" json:"user_billing_info_id,omitempty" validate:"required"`
	Comment           *string                `protobuf:"bytes,3,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AddCampaignVariantSubscriptionRequest) Reset() {
	*x = AddCampaignVariantSubscriptionRequest{}
	mi := &file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCampaignVariantSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCampaignVariantSubscriptionRequest) ProtoMessage() {}

func (x *AddCampaignVariantSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCampaignVariantSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*AddCampaignVariantSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaginvariantsubscription_proto_rawDescGZIP(), []int{0}
}

func (x *AddCampaignVariantSubscriptionRequest) GetCampaignVariantId() string {
	if x != nil {
		return x.CampaignVariantId
	}
	return ""
}

func (x *AddCampaignVariantSubscriptionRequest) GetUserBillingInfoId() string {
	if x != nil {
		return x.UserBillingInfoId
	}
	return ""
}

func (x *AddCampaignVariantSubscriptionRequest) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

type AddCampaignVariantSubscriptionResponse struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Data          *CampaignVariantSubscription `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCampaignVariantSubscriptionResponse) Reset() {
	*x = AddCampaignVariantSubscriptionResponse{}
	mi := &file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCampaignVariantSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCampaignVariantSubscriptionResponse) ProtoMessage() {}

func (x *AddCampaignVariantSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCampaignVariantSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*AddCampaignVariantSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaginvariantsubscription_proto_rawDescGZIP(), []int{1}
}

func (x *AddCampaignVariantSubscriptionResponse) GetData() *CampaignVariantSubscription {
	if x != nil {
		return x.Data
	}
	return nil
}

type CampaignVariantSubscription struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UserId            string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CampaignVariantId string                 `protobuf:"bytes,2,opt,name=campaign_variant_id,json=campaignVariantId,proto3" json:"campaign_variant_id,omitempty"`
	Profile           *v1.Profile            `protobuf:"bytes,3,opt,name=profile,proto3,oneof" json:"profile,omitempty"`
	Price             int32                  `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	VtuberId          string                 `protobuf:"bytes,6,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	Comment           *string                `protobuf:"bytes,7,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CampaignVariantSubscription) Reset() {
	*x = CampaignVariantSubscription{}
	mi := &file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CampaignVariantSubscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignVariantSubscription) ProtoMessage() {}

func (x *CampaignVariantSubscription) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignVariantSubscription.ProtoReflect.Descriptor instead.
func (*CampaignVariantSubscription) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaginvariantsubscription_proto_rawDescGZIP(), []int{2}
}

func (x *CampaignVariantSubscription) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CampaignVariantSubscription) GetCampaignVariantId() string {
	if x != nil {
		return x.CampaignVariantId
	}
	return ""
}

func (x *CampaignVariantSubscription) GetProfile() *v1.Profile {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *CampaignVariantSubscription) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CampaignVariantSubscription) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CampaignVariantSubscription) GetVtuberId() string {
	if x != nil {
		return x.VtuberId
	}
	return ""
}

func (x *CampaignVariantSubscription) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

type SendEmailToPurchaseRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Email             string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty" validate:"required,email"`
	CampaignVariantId string                 `protobuf:"bytes,2,opt,name=campaign_variant_id,json=campaignVariantId,proto3" json:"campaign_variant_id,omitempty" validate:"required"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SendEmailToPurchaseRequest) Reset() {
	*x = SendEmailToPurchaseRequest{}
	mi := &file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendEmailToPurchaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendEmailToPurchaseRequest) ProtoMessage() {}

func (x *SendEmailToPurchaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendEmailToPurchaseRequest.ProtoReflect.Descriptor instead.
func (*SendEmailToPurchaseRequest) Descriptor() ([]byte, []int) {
	return file_campaigns_v1_campaginvariantsubscription_proto_rawDescGZIP(), []int{3}
}

func (x *SendEmailToPurchaseRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SendEmailToPurchaseRequest) GetCampaignVariantId() string {
	if x != nil {
		return x.CampaignVariantId
	}
	return ""
}

var File_campaigns_v1_campaginvariantsubscription_proto protoreflect.FileDescriptor

const file_campaigns_v1_campaginvariantsubscription_proto_rawDesc = "" +
	"\n" +
	".campaigns/v1/campaginvariantsubscription.proto\x12\x10api.camapigns.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/profile.proto\"\xb3\x01\n" +
	"%AddCampaignVariantSubscriptionRequest\x12.\n" +
	"\x13campaign_variant_id\x18\x01 \x01(\tR\x11campaignVariantId\x12/\n" +
	"\x14user_billing_info_id\x18\x02 \x01(\tR\x11userBillingInfoId\x12\x1d\n" +
	"\acomment\x18\x03 \x01(\tH\x00R\acomment\x88\x01\x01B\n" +
	"\n" +
	"\b_comment\"k\n" +
	"&AddCampaignVariantSubscriptionResponse\x12A\n" +
	"\x04data\x18\x01 \x01(\v2-.api.camapigns.v1.CampaignVariantSubscriptionR\x04data\"\xc2\x02\n" +
	"\x1bCampaignVariantSubscription\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12.\n" +
	"\x13campaign_variant_id\x18\x02 \x01(\tR\x11campaignVariantId\x125\n" +
	"\aprofile\x18\x03 \x01(\v2\x16.api.shared.v1.ProfileH\x00R\aprofile\x88\x01\x01\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x05R\x05price\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1b\n" +
	"\tvtuber_id\x18\x06 \x01(\tR\bvtuberId\x12\x1d\n" +
	"\acomment\x18\a \x01(\tH\x01R\acomment\x88\x01\x01B\n" +
	"\n" +
	"\b_profileB\n" +
	"\n" +
	"\b_comment\"b\n" +
	"\x1aSendEmailToPurchaseRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12.\n" +
	"\x13campaign_variant_id\x18\x02 \x01(\tR\x11campaignVariantId2\xc2\x01\n" +
	"\"CampaignVariantSubscriptionService\x12\x9b\x01\n" +
	"\x1eAddCampaignVariantSubscription\x127.api.camapigns.v1.AddCampaignVariantSubscriptionRequest\x1a8.api.camapigns.v1.AddCampaignVariantSubscriptionResponse\"\x06\x82\xb5\x18\x02\b\x01B8Z6github.com/nsp-inc/vtuber/api/campaigns/v1;campaignsv1b\x06proto3"

var (
	file_campaigns_v1_campaginvariantsubscription_proto_rawDescOnce sync.Once
	file_campaigns_v1_campaginvariantsubscription_proto_rawDescData []byte
)

func file_campaigns_v1_campaginvariantsubscription_proto_rawDescGZIP() []byte {
	file_campaigns_v1_campaginvariantsubscription_proto_rawDescOnce.Do(func() {
		file_campaigns_v1_campaginvariantsubscription_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_campaigns_v1_campaginvariantsubscription_proto_rawDesc), len(file_campaigns_v1_campaginvariantsubscription_proto_rawDesc)))
	})
	return file_campaigns_v1_campaginvariantsubscription_proto_rawDescData
}

var file_campaigns_v1_campaginvariantsubscription_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_campaigns_v1_campaginvariantsubscription_proto_goTypes = []any{
	(*AddCampaignVariantSubscriptionRequest)(nil),  // 0: api.camapigns.v1.AddCampaignVariantSubscriptionRequest
	(*AddCampaignVariantSubscriptionResponse)(nil), // 1: api.camapigns.v1.AddCampaignVariantSubscriptionResponse
	(*CampaignVariantSubscription)(nil),            // 2: api.camapigns.v1.CampaignVariantSubscription
	(*SendEmailToPurchaseRequest)(nil),             // 3: api.camapigns.v1.SendEmailToPurchaseRequest
	(*v1.Profile)(nil),                             // 4: api.shared.v1.Profile
	(*timestamppb.Timestamp)(nil),                  // 5: google.protobuf.Timestamp
}
var file_campaigns_v1_campaginvariantsubscription_proto_depIdxs = []int32{
	2, // 0: api.camapigns.v1.AddCampaignVariantSubscriptionResponse.data:type_name -> api.camapigns.v1.CampaignVariantSubscription
	4, // 1: api.camapigns.v1.CampaignVariantSubscription.profile:type_name -> api.shared.v1.Profile
	5, // 2: api.camapigns.v1.CampaignVariantSubscription.created_at:type_name -> google.protobuf.Timestamp
	0, // 3: api.camapigns.v1.CampaignVariantSubscriptionService.AddCampaignVariantSubscription:input_type -> api.camapigns.v1.AddCampaignVariantSubscriptionRequest
	1, // 4: api.camapigns.v1.CampaignVariantSubscriptionService.AddCampaignVariantSubscription:output_type -> api.camapigns.v1.AddCampaignVariantSubscriptionResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_campaigns_v1_campaginvariantsubscription_proto_init() }
func file_campaigns_v1_campaginvariantsubscription_proto_init() {
	if File_campaigns_v1_campaginvariantsubscription_proto != nil {
		return
	}
	file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[0].OneofWrappers = []any{}
	file_campaigns_v1_campaginvariantsubscription_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_campaigns_v1_campaginvariantsubscription_proto_rawDesc), len(file_campaigns_v1_campaginvariantsubscription_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_campaigns_v1_campaginvariantsubscription_proto_goTypes,
		DependencyIndexes: file_campaigns_v1_campaginvariantsubscription_proto_depIdxs,
		MessageInfos:      file_campaigns_v1_campaginvariantsubscription_proto_msgTypes,
	}.Build()
	File_campaigns_v1_campaginvariantsubscription_proto = out.File
	file_campaigns_v1_campaginvariantsubscription_proto_goTypes = nil
	file_campaigns_v1_campaginvariantsubscription_proto_depIdxs = nil
}
