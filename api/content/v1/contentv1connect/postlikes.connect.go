// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: content/v1/postlikes.proto

package contentv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/content/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// PostLikeServiceName is the fully-qualified name of the PostLikeService service.
	PostLikeServiceName = "api.content.v1.PostLikeService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// PostLikeServiceAddPostLikeProcedure is the fully-qualified name of the PostLikeService's
	// AddPostLike RPC.
	PostLikeServiceAddPostLikeProcedure = "/api.content.v1.PostLikeService/AddPostLike"
	// PostLikeServiceGetPostLikeCountProcedure is the fully-qualified name of the PostLikeService's
	// GetPostLikeCount RPC.
	PostLikeServiceGetPostLikeCountProcedure = "/api.content.v1.PostLikeService/GetPostLikeCount"
	// PostLikeServiceGetPostLikesOfUserProcedure is the fully-qualified name of the PostLikeService's
	// GetPostLikesOfUser RPC.
	PostLikeServiceGetPostLikesOfUserProcedure = "/api.content.v1.PostLikeService/GetPostLikesOfUser"
	// PostLikeServiceDeletePostLikeByIdProcedure is the fully-qualified name of the PostLikeService's
	// DeletePostLikeById RPC.
	PostLikeServiceDeletePostLikeByIdProcedure = "/api.content.v1.PostLikeService/DeletePostLikeById"
)

// PostLikeServiceClient is a client for the api.content.v1.PostLikeService service.
type PostLikeServiceClient interface {
	AddPostLike(context.Context, *connect.Request[v1.AddPostLikeRequest]) (*connect.Response[v1.AddPostLikeResponse], error)
	GetPostLikeCount(context.Context, *connect.Request[v1.GetPostLikeCountRequest]) (*connect.Response[v1.GetPostLikeCountResponse], error)
	GetPostLikesOfUser(context.Context, *connect.Request[v1.GetAllPostLikeByUserRequest]) (*connect.Response[v1.GetAllPostLikeByUserResponse], error)
	DeletePostLikeById(context.Context, *connect.Request[v1.DeletePostLikeByIdRequest]) (*connect.Response[v1.DeletePostLikeByIdResponse], error)
}

// NewPostLikeServiceClient constructs a client for the api.content.v1.PostLikeService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewPostLikeServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) PostLikeServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	postLikeServiceMethods := v1.File_content_v1_postlikes_proto.Services().ByName("PostLikeService").Methods()
	return &postLikeServiceClient{
		addPostLike: connect.NewClient[v1.AddPostLikeRequest, v1.AddPostLikeResponse](
			httpClient,
			baseURL+PostLikeServiceAddPostLikeProcedure,
			connect.WithSchema(postLikeServiceMethods.ByName("AddPostLike")),
			connect.WithClientOptions(opts...),
		),
		getPostLikeCount: connect.NewClient[v1.GetPostLikeCountRequest, v1.GetPostLikeCountResponse](
			httpClient,
			baseURL+PostLikeServiceGetPostLikeCountProcedure,
			connect.WithSchema(postLikeServiceMethods.ByName("GetPostLikeCount")),
			connect.WithClientOptions(opts...),
		),
		getPostLikesOfUser: connect.NewClient[v1.GetAllPostLikeByUserRequest, v1.GetAllPostLikeByUserResponse](
			httpClient,
			baseURL+PostLikeServiceGetPostLikesOfUserProcedure,
			connect.WithSchema(postLikeServiceMethods.ByName("GetPostLikesOfUser")),
			connect.WithClientOptions(opts...),
		),
		deletePostLikeById: connect.NewClient[v1.DeletePostLikeByIdRequest, v1.DeletePostLikeByIdResponse](
			httpClient,
			baseURL+PostLikeServiceDeletePostLikeByIdProcedure,
			connect.WithSchema(postLikeServiceMethods.ByName("DeletePostLikeById")),
			connect.WithClientOptions(opts...),
		),
	}
}

// postLikeServiceClient implements PostLikeServiceClient.
type postLikeServiceClient struct {
	addPostLike        *connect.Client[v1.AddPostLikeRequest, v1.AddPostLikeResponse]
	getPostLikeCount   *connect.Client[v1.GetPostLikeCountRequest, v1.GetPostLikeCountResponse]
	getPostLikesOfUser *connect.Client[v1.GetAllPostLikeByUserRequest, v1.GetAllPostLikeByUserResponse]
	deletePostLikeById *connect.Client[v1.DeletePostLikeByIdRequest, v1.DeletePostLikeByIdResponse]
}

// AddPostLike calls api.content.v1.PostLikeService.AddPostLike.
func (c *postLikeServiceClient) AddPostLike(ctx context.Context, req *connect.Request[v1.AddPostLikeRequest]) (*connect.Response[v1.AddPostLikeResponse], error) {
	return c.addPostLike.CallUnary(ctx, req)
}

// GetPostLikeCount calls api.content.v1.PostLikeService.GetPostLikeCount.
func (c *postLikeServiceClient) GetPostLikeCount(ctx context.Context, req *connect.Request[v1.GetPostLikeCountRequest]) (*connect.Response[v1.GetPostLikeCountResponse], error) {
	return c.getPostLikeCount.CallUnary(ctx, req)
}

// GetPostLikesOfUser calls api.content.v1.PostLikeService.GetPostLikesOfUser.
func (c *postLikeServiceClient) GetPostLikesOfUser(ctx context.Context, req *connect.Request[v1.GetAllPostLikeByUserRequest]) (*connect.Response[v1.GetAllPostLikeByUserResponse], error) {
	return c.getPostLikesOfUser.CallUnary(ctx, req)
}

// DeletePostLikeById calls api.content.v1.PostLikeService.DeletePostLikeById.
func (c *postLikeServiceClient) DeletePostLikeById(ctx context.Context, req *connect.Request[v1.DeletePostLikeByIdRequest]) (*connect.Response[v1.DeletePostLikeByIdResponse], error) {
	return c.deletePostLikeById.CallUnary(ctx, req)
}

// PostLikeServiceHandler is an implementation of the api.content.v1.PostLikeService service.
type PostLikeServiceHandler interface {
	AddPostLike(context.Context, *connect.Request[v1.AddPostLikeRequest]) (*connect.Response[v1.AddPostLikeResponse], error)
	GetPostLikeCount(context.Context, *connect.Request[v1.GetPostLikeCountRequest]) (*connect.Response[v1.GetPostLikeCountResponse], error)
	GetPostLikesOfUser(context.Context, *connect.Request[v1.GetAllPostLikeByUserRequest]) (*connect.Response[v1.GetAllPostLikeByUserResponse], error)
	DeletePostLikeById(context.Context, *connect.Request[v1.DeletePostLikeByIdRequest]) (*connect.Response[v1.DeletePostLikeByIdResponse], error)
}

// NewPostLikeServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewPostLikeServiceHandler(svc PostLikeServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	postLikeServiceMethods := v1.File_content_v1_postlikes_proto.Services().ByName("PostLikeService").Methods()
	postLikeServiceAddPostLikeHandler := connect.NewUnaryHandler(
		PostLikeServiceAddPostLikeProcedure,
		svc.AddPostLike,
		connect.WithSchema(postLikeServiceMethods.ByName("AddPostLike")),
		connect.WithHandlerOptions(opts...),
	)
	postLikeServiceGetPostLikeCountHandler := connect.NewUnaryHandler(
		PostLikeServiceGetPostLikeCountProcedure,
		svc.GetPostLikeCount,
		connect.WithSchema(postLikeServiceMethods.ByName("GetPostLikeCount")),
		connect.WithHandlerOptions(opts...),
	)
	postLikeServiceGetPostLikesOfUserHandler := connect.NewUnaryHandler(
		PostLikeServiceGetPostLikesOfUserProcedure,
		svc.GetPostLikesOfUser,
		connect.WithSchema(postLikeServiceMethods.ByName("GetPostLikesOfUser")),
		connect.WithHandlerOptions(opts...),
	)
	postLikeServiceDeletePostLikeByIdHandler := connect.NewUnaryHandler(
		PostLikeServiceDeletePostLikeByIdProcedure,
		svc.DeletePostLikeById,
		connect.WithSchema(postLikeServiceMethods.ByName("DeletePostLikeById")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.content.v1.PostLikeService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case PostLikeServiceAddPostLikeProcedure:
			postLikeServiceAddPostLikeHandler.ServeHTTP(w, r)
		case PostLikeServiceGetPostLikeCountProcedure:
			postLikeServiceGetPostLikeCountHandler.ServeHTTP(w, r)
		case PostLikeServiceGetPostLikesOfUserProcedure:
			postLikeServiceGetPostLikesOfUserHandler.ServeHTTP(w, r)
		case PostLikeServiceDeletePostLikeByIdProcedure:
			postLikeServiceDeletePostLikeByIdHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedPostLikeServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedPostLikeServiceHandler struct{}

func (UnimplementedPostLikeServiceHandler) AddPostLike(context.Context, *connect.Request[v1.AddPostLikeRequest]) (*connect.Response[v1.AddPostLikeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostLikeService.AddPostLike is not implemented"))
}

func (UnimplementedPostLikeServiceHandler) GetPostLikeCount(context.Context, *connect.Request[v1.GetPostLikeCountRequest]) (*connect.Response[v1.GetPostLikeCountResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostLikeService.GetPostLikeCount is not implemented"))
}

func (UnimplementedPostLikeServiceHandler) GetPostLikesOfUser(context.Context, *connect.Request[v1.GetAllPostLikeByUserRequest]) (*connect.Response[v1.GetAllPostLikeByUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostLikeService.GetPostLikesOfUser is not implemented"))
}

func (UnimplementedPostLikeServiceHandler) DeletePostLikeById(context.Context, *connect.Request[v1.DeletePostLikeByIdRequest]) (*connect.Response[v1.DeletePostLikeByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostLikeService.DeletePostLikeById is not implemented"))
}
