// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: content/v1/postcomment.proto

package contentv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/content/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// PostCommentServiceName is the fully-qualified name of the PostCommentService service.
	PostCommentServiceName = "api.content.v1.PostCommentService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// PostCommentServiceAddPostCommentProcedure is the fully-qualified name of the PostCommentService's
	// AddPostComment RPC.
	PostCommentServiceAddPostCommentProcedure = "/api.content.v1.PostCommentService/AddPostComment"
	// PostCommentServiceGetAllPostCommentsProcedure is the fully-qualified name of the
	// PostCommentService's GetAllPostComments RPC.
	PostCommentServiceGetAllPostCommentsProcedure = "/api.content.v1.PostCommentService/GetAllPostComments"
	// PostCommentServiceGetAllReplesOfCommentProcedure is the fully-qualified name of the
	// PostCommentService's GetAllReplesOfComment RPC.
	PostCommentServiceGetAllReplesOfCommentProcedure = "/api.content.v1.PostCommentService/GetAllReplesOfComment"
	// PostCommentServiceGetPostCommentByIdProcedure is the fully-qualified name of the
	// PostCommentService's GetPostCommentById RPC.
	PostCommentServiceGetPostCommentByIdProcedure = "/api.content.v1.PostCommentService/GetPostCommentById"
	// PostCommentServiceDeletePostCommentByIdProcedure is the fully-qualified name of the
	// PostCommentService's DeletePostCommentById RPC.
	PostCommentServiceDeletePostCommentByIdProcedure = "/api.content.v1.PostCommentService/DeletePostCommentById"
	// PostCommentServiceUpdatePostCommentByIdProcedure is the fully-qualified name of the
	// PostCommentService's UpdatePostCommentById RPC.
	PostCommentServiceUpdatePostCommentByIdProcedure = "/api.content.v1.PostCommentService/UpdatePostCommentById"
)

// PostCommentServiceClient is a client for the api.content.v1.PostCommentService service.
type PostCommentServiceClient interface {
	AddPostComment(context.Context, *connect.Request[v1.AddPostCommentRequest]) (*connect.Response[v1.AddPostCommentResponse], error)
	GetAllPostComments(context.Context, *connect.Request[v1.GetAllPostCommentsRequest]) (*connect.Response[v1.GetAllPostCommentsResponse], error)
	GetAllReplesOfComment(context.Context, *connect.Request[v1.GetPostRepliesOfCommentRequest]) (*connect.Response[v1.GetAllPostCommentsResponse], error)
	GetPostCommentById(context.Context, *connect.Request[v1.GetPostCommentByIdRequest]) (*connect.Response[v1.GetPostCommentByIdResponse], error)
	DeletePostCommentById(context.Context, *connect.Request[v1.DeletePostCommentByIdRequest]) (*connect.Response[v1.DeletePostCommentByIdResponse], error)
	UpdatePostCommentById(context.Context, *connect.Request[v1.UpdatePostCommentByIdRequest]) (*connect.Response[v1.UpdatePostCommentByIdResponse], error)
}

// NewPostCommentServiceClient constructs a client for the api.content.v1.PostCommentService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewPostCommentServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) PostCommentServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	postCommentServiceMethods := v1.File_content_v1_postcomment_proto.Services().ByName("PostCommentService").Methods()
	return &postCommentServiceClient{
		addPostComment: connect.NewClient[v1.AddPostCommentRequest, v1.AddPostCommentResponse](
			httpClient,
			baseURL+PostCommentServiceAddPostCommentProcedure,
			connect.WithSchema(postCommentServiceMethods.ByName("AddPostComment")),
			connect.WithClientOptions(opts...),
		),
		getAllPostComments: connect.NewClient[v1.GetAllPostCommentsRequest, v1.GetAllPostCommentsResponse](
			httpClient,
			baseURL+PostCommentServiceGetAllPostCommentsProcedure,
			connect.WithSchema(postCommentServiceMethods.ByName("GetAllPostComments")),
			connect.WithClientOptions(opts...),
		),
		getAllReplesOfComment: connect.NewClient[v1.GetPostRepliesOfCommentRequest, v1.GetAllPostCommentsResponse](
			httpClient,
			baseURL+PostCommentServiceGetAllReplesOfCommentProcedure,
			connect.WithSchema(postCommentServiceMethods.ByName("GetAllReplesOfComment")),
			connect.WithClientOptions(opts...),
		),
		getPostCommentById: connect.NewClient[v1.GetPostCommentByIdRequest, v1.GetPostCommentByIdResponse](
			httpClient,
			baseURL+PostCommentServiceGetPostCommentByIdProcedure,
			connect.WithSchema(postCommentServiceMethods.ByName("GetPostCommentById")),
			connect.WithClientOptions(opts...),
		),
		deletePostCommentById: connect.NewClient[v1.DeletePostCommentByIdRequest, v1.DeletePostCommentByIdResponse](
			httpClient,
			baseURL+PostCommentServiceDeletePostCommentByIdProcedure,
			connect.WithSchema(postCommentServiceMethods.ByName("DeletePostCommentById")),
			connect.WithClientOptions(opts...),
		),
		updatePostCommentById: connect.NewClient[v1.UpdatePostCommentByIdRequest, v1.UpdatePostCommentByIdResponse](
			httpClient,
			baseURL+PostCommentServiceUpdatePostCommentByIdProcedure,
			connect.WithSchema(postCommentServiceMethods.ByName("UpdatePostCommentById")),
			connect.WithClientOptions(opts...),
		),
	}
}

// postCommentServiceClient implements PostCommentServiceClient.
type postCommentServiceClient struct {
	addPostComment        *connect.Client[v1.AddPostCommentRequest, v1.AddPostCommentResponse]
	getAllPostComments    *connect.Client[v1.GetAllPostCommentsRequest, v1.GetAllPostCommentsResponse]
	getAllReplesOfComment *connect.Client[v1.GetPostRepliesOfCommentRequest, v1.GetAllPostCommentsResponse]
	getPostCommentById    *connect.Client[v1.GetPostCommentByIdRequest, v1.GetPostCommentByIdResponse]
	deletePostCommentById *connect.Client[v1.DeletePostCommentByIdRequest, v1.DeletePostCommentByIdResponse]
	updatePostCommentById *connect.Client[v1.UpdatePostCommentByIdRequest, v1.UpdatePostCommentByIdResponse]
}

// AddPostComment calls api.content.v1.PostCommentService.AddPostComment.
func (c *postCommentServiceClient) AddPostComment(ctx context.Context, req *connect.Request[v1.AddPostCommentRequest]) (*connect.Response[v1.AddPostCommentResponse], error) {
	return c.addPostComment.CallUnary(ctx, req)
}

// GetAllPostComments calls api.content.v1.PostCommentService.GetAllPostComments.
func (c *postCommentServiceClient) GetAllPostComments(ctx context.Context, req *connect.Request[v1.GetAllPostCommentsRequest]) (*connect.Response[v1.GetAllPostCommentsResponse], error) {
	return c.getAllPostComments.CallUnary(ctx, req)
}

// GetAllReplesOfComment calls api.content.v1.PostCommentService.GetAllReplesOfComment.
func (c *postCommentServiceClient) GetAllReplesOfComment(ctx context.Context, req *connect.Request[v1.GetPostRepliesOfCommentRequest]) (*connect.Response[v1.GetAllPostCommentsResponse], error) {
	return c.getAllReplesOfComment.CallUnary(ctx, req)
}

// GetPostCommentById calls api.content.v1.PostCommentService.GetPostCommentById.
func (c *postCommentServiceClient) GetPostCommentById(ctx context.Context, req *connect.Request[v1.GetPostCommentByIdRequest]) (*connect.Response[v1.GetPostCommentByIdResponse], error) {
	return c.getPostCommentById.CallUnary(ctx, req)
}

// DeletePostCommentById calls api.content.v1.PostCommentService.DeletePostCommentById.
func (c *postCommentServiceClient) DeletePostCommentById(ctx context.Context, req *connect.Request[v1.DeletePostCommentByIdRequest]) (*connect.Response[v1.DeletePostCommentByIdResponse], error) {
	return c.deletePostCommentById.CallUnary(ctx, req)
}

// UpdatePostCommentById calls api.content.v1.PostCommentService.UpdatePostCommentById.
func (c *postCommentServiceClient) UpdatePostCommentById(ctx context.Context, req *connect.Request[v1.UpdatePostCommentByIdRequest]) (*connect.Response[v1.UpdatePostCommentByIdResponse], error) {
	return c.updatePostCommentById.CallUnary(ctx, req)
}

// PostCommentServiceHandler is an implementation of the api.content.v1.PostCommentService service.
type PostCommentServiceHandler interface {
	AddPostComment(context.Context, *connect.Request[v1.AddPostCommentRequest]) (*connect.Response[v1.AddPostCommentResponse], error)
	GetAllPostComments(context.Context, *connect.Request[v1.GetAllPostCommentsRequest]) (*connect.Response[v1.GetAllPostCommentsResponse], error)
	GetAllReplesOfComment(context.Context, *connect.Request[v1.GetPostRepliesOfCommentRequest]) (*connect.Response[v1.GetAllPostCommentsResponse], error)
	GetPostCommentById(context.Context, *connect.Request[v1.GetPostCommentByIdRequest]) (*connect.Response[v1.GetPostCommentByIdResponse], error)
	DeletePostCommentById(context.Context, *connect.Request[v1.DeletePostCommentByIdRequest]) (*connect.Response[v1.DeletePostCommentByIdResponse], error)
	UpdatePostCommentById(context.Context, *connect.Request[v1.UpdatePostCommentByIdRequest]) (*connect.Response[v1.UpdatePostCommentByIdResponse], error)
}

// NewPostCommentServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewPostCommentServiceHandler(svc PostCommentServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	postCommentServiceMethods := v1.File_content_v1_postcomment_proto.Services().ByName("PostCommentService").Methods()
	postCommentServiceAddPostCommentHandler := connect.NewUnaryHandler(
		PostCommentServiceAddPostCommentProcedure,
		svc.AddPostComment,
		connect.WithSchema(postCommentServiceMethods.ByName("AddPostComment")),
		connect.WithHandlerOptions(opts...),
	)
	postCommentServiceGetAllPostCommentsHandler := connect.NewUnaryHandler(
		PostCommentServiceGetAllPostCommentsProcedure,
		svc.GetAllPostComments,
		connect.WithSchema(postCommentServiceMethods.ByName("GetAllPostComments")),
		connect.WithHandlerOptions(opts...),
	)
	postCommentServiceGetAllReplesOfCommentHandler := connect.NewUnaryHandler(
		PostCommentServiceGetAllReplesOfCommentProcedure,
		svc.GetAllReplesOfComment,
		connect.WithSchema(postCommentServiceMethods.ByName("GetAllReplesOfComment")),
		connect.WithHandlerOptions(opts...),
	)
	postCommentServiceGetPostCommentByIdHandler := connect.NewUnaryHandler(
		PostCommentServiceGetPostCommentByIdProcedure,
		svc.GetPostCommentById,
		connect.WithSchema(postCommentServiceMethods.ByName("GetPostCommentById")),
		connect.WithHandlerOptions(opts...),
	)
	postCommentServiceDeletePostCommentByIdHandler := connect.NewUnaryHandler(
		PostCommentServiceDeletePostCommentByIdProcedure,
		svc.DeletePostCommentById,
		connect.WithSchema(postCommentServiceMethods.ByName("DeletePostCommentById")),
		connect.WithHandlerOptions(opts...),
	)
	postCommentServiceUpdatePostCommentByIdHandler := connect.NewUnaryHandler(
		PostCommentServiceUpdatePostCommentByIdProcedure,
		svc.UpdatePostCommentById,
		connect.WithSchema(postCommentServiceMethods.ByName("UpdatePostCommentById")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.content.v1.PostCommentService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case PostCommentServiceAddPostCommentProcedure:
			postCommentServiceAddPostCommentHandler.ServeHTTP(w, r)
		case PostCommentServiceGetAllPostCommentsProcedure:
			postCommentServiceGetAllPostCommentsHandler.ServeHTTP(w, r)
		case PostCommentServiceGetAllReplesOfCommentProcedure:
			postCommentServiceGetAllReplesOfCommentHandler.ServeHTTP(w, r)
		case PostCommentServiceGetPostCommentByIdProcedure:
			postCommentServiceGetPostCommentByIdHandler.ServeHTTP(w, r)
		case PostCommentServiceDeletePostCommentByIdProcedure:
			postCommentServiceDeletePostCommentByIdHandler.ServeHTTP(w, r)
		case PostCommentServiceUpdatePostCommentByIdProcedure:
			postCommentServiceUpdatePostCommentByIdHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedPostCommentServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedPostCommentServiceHandler struct{}

func (UnimplementedPostCommentServiceHandler) AddPostComment(context.Context, *connect.Request[v1.AddPostCommentRequest]) (*connect.Response[v1.AddPostCommentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostCommentService.AddPostComment is not implemented"))
}

func (UnimplementedPostCommentServiceHandler) GetAllPostComments(context.Context, *connect.Request[v1.GetAllPostCommentsRequest]) (*connect.Response[v1.GetAllPostCommentsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostCommentService.GetAllPostComments is not implemented"))
}

func (UnimplementedPostCommentServiceHandler) GetAllReplesOfComment(context.Context, *connect.Request[v1.GetPostRepliesOfCommentRequest]) (*connect.Response[v1.GetAllPostCommentsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostCommentService.GetAllReplesOfComment is not implemented"))
}

func (UnimplementedPostCommentServiceHandler) GetPostCommentById(context.Context, *connect.Request[v1.GetPostCommentByIdRequest]) (*connect.Response[v1.GetPostCommentByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostCommentService.GetPostCommentById is not implemented"))
}

func (UnimplementedPostCommentServiceHandler) DeletePostCommentById(context.Context, *connect.Request[v1.DeletePostCommentByIdRequest]) (*connect.Response[v1.DeletePostCommentByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostCommentService.DeletePostCommentById is not implemented"))
}

func (UnimplementedPostCommentServiceHandler) UpdatePostCommentById(context.Context, *connect.Request[v1.UpdatePostCommentByIdRequest]) (*connect.Response[v1.UpdatePostCommentByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostCommentService.UpdatePostCommentById is not implemented"))
}
