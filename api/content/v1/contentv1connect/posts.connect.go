// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: content/v1/posts.proto

package contentv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/content/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// PostServiceName is the fully-qualified name of the PostService service.
	PostServiceName = "api.content.v1.PostService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// PostServiceAddPostProcedure is the fully-qualified name of the PostService's AddPost RPC.
	PostServiceAddPostProcedure = "/api.content.v1.PostService/AddPost"
	// PostServiceGetAllPostsProcedure is the fully-qualified name of the PostService's GetAllPosts RPC.
	PostServiceGetAllPostsProcedure = "/api.content.v1.PostService/GetAllPosts"
	// PostServiceGetPostByIdProcedure is the fully-qualified name of the PostService's GetPostById RPC.
	PostServiceGetPostByIdProcedure = "/api.content.v1.PostService/GetPostById"
	// PostServiceDeletePostByIdProcedure is the fully-qualified name of the PostService's
	// DeletePostById RPC.
	PostServiceDeletePostByIdProcedure = "/api.content.v1.PostService/DeletePostById"
	// PostServiceGetMyPostsProcedure is the fully-qualified name of the PostService's GetMyPosts RPC.
	PostServiceGetMyPostsProcedure = "/api.content.v1.PostService/GetMyPosts"
	// PostServiceUpdatePostByIdProcedure is the fully-qualified name of the PostService's
	// UpdatePostById RPC.
	PostServiceUpdatePostByIdProcedure = "/api.content.v1.PostService/UpdatePostById"
	// PostServiceGetVtuberGalleriesProcedure is the fully-qualified name of the PostService's
	// GetVtuberGalleries RPC.
	PostServiceGetVtuberGalleriesProcedure = "/api.content.v1.PostService/GetVtuberGalleries"
)

// PostServiceClient is a client for the api.content.v1.PostService service.
type PostServiceClient interface {
	AddPost(context.Context, *connect.Request[v1.AddPostRequest]) (*connect.Response[v1.AddPostResponse], error)
	GetAllPosts(context.Context, *connect.Request[v1.GetAllPostsRequest]) (*connect.Response[v1.GetAllPostsResponse], error)
	GetPostById(context.Context, *connect.Request[v1.GetPostByIdRequest]) (*connect.Response[v1.GetPostByIdResponse], error)
	DeletePostById(context.Context, *connect.Request[v1.DeletePostByIdRequest]) (*connect.Response[v1.DeletePostByIdResponse], error)
	GetMyPosts(context.Context, *connect.Request[v1.GetMyPostsRequest]) (*connect.Response[v1.GetAllPostsResponse], error)
	UpdatePostById(context.Context, *connect.Request[v1.UpdatePostByIdRequest]) (*connect.Response[v1.UpdatePostByIdResponse], error)
	GetVtuberGalleries(context.Context, *connect.Request[v1.GetVtuberGalleryRequest]) (*connect.Response[v1.GetAllPostsResponse], error)
}

// NewPostServiceClient constructs a client for the api.content.v1.PostService service. By default,
// it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and
// sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC()
// or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewPostServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) PostServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	postServiceMethods := v1.File_content_v1_posts_proto.Services().ByName("PostService").Methods()
	return &postServiceClient{
		addPost: connect.NewClient[v1.AddPostRequest, v1.AddPostResponse](
			httpClient,
			baseURL+PostServiceAddPostProcedure,
			connect.WithSchema(postServiceMethods.ByName("AddPost")),
			connect.WithClientOptions(opts...),
		),
		getAllPosts: connect.NewClient[v1.GetAllPostsRequest, v1.GetAllPostsResponse](
			httpClient,
			baseURL+PostServiceGetAllPostsProcedure,
			connect.WithSchema(postServiceMethods.ByName("GetAllPosts")),
			connect.WithClientOptions(opts...),
		),
		getPostById: connect.NewClient[v1.GetPostByIdRequest, v1.GetPostByIdResponse](
			httpClient,
			baseURL+PostServiceGetPostByIdProcedure,
			connect.WithSchema(postServiceMethods.ByName("GetPostById")),
			connect.WithClientOptions(opts...),
		),
		deletePostById: connect.NewClient[v1.DeletePostByIdRequest, v1.DeletePostByIdResponse](
			httpClient,
			baseURL+PostServiceDeletePostByIdProcedure,
			connect.WithSchema(postServiceMethods.ByName("DeletePostById")),
			connect.WithClientOptions(opts...),
		),
		getMyPosts: connect.NewClient[v1.GetMyPostsRequest, v1.GetAllPostsResponse](
			httpClient,
			baseURL+PostServiceGetMyPostsProcedure,
			connect.WithSchema(postServiceMethods.ByName("GetMyPosts")),
			connect.WithClientOptions(opts...),
		),
		updatePostById: connect.NewClient[v1.UpdatePostByIdRequest, v1.UpdatePostByIdResponse](
			httpClient,
			baseURL+PostServiceUpdatePostByIdProcedure,
			connect.WithSchema(postServiceMethods.ByName("UpdatePostById")),
			connect.WithClientOptions(opts...),
		),
		getVtuberGalleries: connect.NewClient[v1.GetVtuberGalleryRequest, v1.GetAllPostsResponse](
			httpClient,
			baseURL+PostServiceGetVtuberGalleriesProcedure,
			connect.WithSchema(postServiceMethods.ByName("GetVtuberGalleries")),
			connect.WithClientOptions(opts...),
		),
	}
}

// postServiceClient implements PostServiceClient.
type postServiceClient struct {
	addPost            *connect.Client[v1.AddPostRequest, v1.AddPostResponse]
	getAllPosts        *connect.Client[v1.GetAllPostsRequest, v1.GetAllPostsResponse]
	getPostById        *connect.Client[v1.GetPostByIdRequest, v1.GetPostByIdResponse]
	deletePostById     *connect.Client[v1.DeletePostByIdRequest, v1.DeletePostByIdResponse]
	getMyPosts         *connect.Client[v1.GetMyPostsRequest, v1.GetAllPostsResponse]
	updatePostById     *connect.Client[v1.UpdatePostByIdRequest, v1.UpdatePostByIdResponse]
	getVtuberGalleries *connect.Client[v1.GetVtuberGalleryRequest, v1.GetAllPostsResponse]
}

// AddPost calls api.content.v1.PostService.AddPost.
func (c *postServiceClient) AddPost(ctx context.Context, req *connect.Request[v1.AddPostRequest]) (*connect.Response[v1.AddPostResponse], error) {
	return c.addPost.CallUnary(ctx, req)
}

// GetAllPosts calls api.content.v1.PostService.GetAllPosts.
func (c *postServiceClient) GetAllPosts(ctx context.Context, req *connect.Request[v1.GetAllPostsRequest]) (*connect.Response[v1.GetAllPostsResponse], error) {
	return c.getAllPosts.CallUnary(ctx, req)
}

// GetPostById calls api.content.v1.PostService.GetPostById.
func (c *postServiceClient) GetPostById(ctx context.Context, req *connect.Request[v1.GetPostByIdRequest]) (*connect.Response[v1.GetPostByIdResponse], error) {
	return c.getPostById.CallUnary(ctx, req)
}

// DeletePostById calls api.content.v1.PostService.DeletePostById.
func (c *postServiceClient) DeletePostById(ctx context.Context, req *connect.Request[v1.DeletePostByIdRequest]) (*connect.Response[v1.DeletePostByIdResponse], error) {
	return c.deletePostById.CallUnary(ctx, req)
}

// GetMyPosts calls api.content.v1.PostService.GetMyPosts.
func (c *postServiceClient) GetMyPosts(ctx context.Context, req *connect.Request[v1.GetMyPostsRequest]) (*connect.Response[v1.GetAllPostsResponse], error) {
	return c.getMyPosts.CallUnary(ctx, req)
}

// UpdatePostById calls api.content.v1.PostService.UpdatePostById.
func (c *postServiceClient) UpdatePostById(ctx context.Context, req *connect.Request[v1.UpdatePostByIdRequest]) (*connect.Response[v1.UpdatePostByIdResponse], error) {
	return c.updatePostById.CallUnary(ctx, req)
}

// GetVtuberGalleries calls api.content.v1.PostService.GetVtuberGalleries.
func (c *postServiceClient) GetVtuberGalleries(ctx context.Context, req *connect.Request[v1.GetVtuberGalleryRequest]) (*connect.Response[v1.GetAllPostsResponse], error) {
	return c.getVtuberGalleries.CallUnary(ctx, req)
}

// PostServiceHandler is an implementation of the api.content.v1.PostService service.
type PostServiceHandler interface {
	AddPost(context.Context, *connect.Request[v1.AddPostRequest]) (*connect.Response[v1.AddPostResponse], error)
	GetAllPosts(context.Context, *connect.Request[v1.GetAllPostsRequest]) (*connect.Response[v1.GetAllPostsResponse], error)
	GetPostById(context.Context, *connect.Request[v1.GetPostByIdRequest]) (*connect.Response[v1.GetPostByIdResponse], error)
	DeletePostById(context.Context, *connect.Request[v1.DeletePostByIdRequest]) (*connect.Response[v1.DeletePostByIdResponse], error)
	GetMyPosts(context.Context, *connect.Request[v1.GetMyPostsRequest]) (*connect.Response[v1.GetAllPostsResponse], error)
	UpdatePostById(context.Context, *connect.Request[v1.UpdatePostByIdRequest]) (*connect.Response[v1.UpdatePostByIdResponse], error)
	GetVtuberGalleries(context.Context, *connect.Request[v1.GetVtuberGalleryRequest]) (*connect.Response[v1.GetAllPostsResponse], error)
}

// NewPostServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewPostServiceHandler(svc PostServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	postServiceMethods := v1.File_content_v1_posts_proto.Services().ByName("PostService").Methods()
	postServiceAddPostHandler := connect.NewUnaryHandler(
		PostServiceAddPostProcedure,
		svc.AddPost,
		connect.WithSchema(postServiceMethods.ByName("AddPost")),
		connect.WithHandlerOptions(opts...),
	)
	postServiceGetAllPostsHandler := connect.NewUnaryHandler(
		PostServiceGetAllPostsProcedure,
		svc.GetAllPosts,
		connect.WithSchema(postServiceMethods.ByName("GetAllPosts")),
		connect.WithHandlerOptions(opts...),
	)
	postServiceGetPostByIdHandler := connect.NewUnaryHandler(
		PostServiceGetPostByIdProcedure,
		svc.GetPostById,
		connect.WithSchema(postServiceMethods.ByName("GetPostById")),
		connect.WithHandlerOptions(opts...),
	)
	postServiceDeletePostByIdHandler := connect.NewUnaryHandler(
		PostServiceDeletePostByIdProcedure,
		svc.DeletePostById,
		connect.WithSchema(postServiceMethods.ByName("DeletePostById")),
		connect.WithHandlerOptions(opts...),
	)
	postServiceGetMyPostsHandler := connect.NewUnaryHandler(
		PostServiceGetMyPostsProcedure,
		svc.GetMyPosts,
		connect.WithSchema(postServiceMethods.ByName("GetMyPosts")),
		connect.WithHandlerOptions(opts...),
	)
	postServiceUpdatePostByIdHandler := connect.NewUnaryHandler(
		PostServiceUpdatePostByIdProcedure,
		svc.UpdatePostById,
		connect.WithSchema(postServiceMethods.ByName("UpdatePostById")),
		connect.WithHandlerOptions(opts...),
	)
	postServiceGetVtuberGalleriesHandler := connect.NewUnaryHandler(
		PostServiceGetVtuberGalleriesProcedure,
		svc.GetVtuberGalleries,
		connect.WithSchema(postServiceMethods.ByName("GetVtuberGalleries")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.content.v1.PostService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case PostServiceAddPostProcedure:
			postServiceAddPostHandler.ServeHTTP(w, r)
		case PostServiceGetAllPostsProcedure:
			postServiceGetAllPostsHandler.ServeHTTP(w, r)
		case PostServiceGetPostByIdProcedure:
			postServiceGetPostByIdHandler.ServeHTTP(w, r)
		case PostServiceDeletePostByIdProcedure:
			postServiceDeletePostByIdHandler.ServeHTTP(w, r)
		case PostServiceGetMyPostsProcedure:
			postServiceGetMyPostsHandler.ServeHTTP(w, r)
		case PostServiceUpdatePostByIdProcedure:
			postServiceUpdatePostByIdHandler.ServeHTTP(w, r)
		case PostServiceGetVtuberGalleriesProcedure:
			postServiceGetVtuberGalleriesHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedPostServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedPostServiceHandler struct{}

func (UnimplementedPostServiceHandler) AddPost(context.Context, *connect.Request[v1.AddPostRequest]) (*connect.Response[v1.AddPostResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostService.AddPost is not implemented"))
}

func (UnimplementedPostServiceHandler) GetAllPosts(context.Context, *connect.Request[v1.GetAllPostsRequest]) (*connect.Response[v1.GetAllPostsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostService.GetAllPosts is not implemented"))
}

func (UnimplementedPostServiceHandler) GetPostById(context.Context, *connect.Request[v1.GetPostByIdRequest]) (*connect.Response[v1.GetPostByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostService.GetPostById is not implemented"))
}

func (UnimplementedPostServiceHandler) DeletePostById(context.Context, *connect.Request[v1.DeletePostByIdRequest]) (*connect.Response[v1.DeletePostByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostService.DeletePostById is not implemented"))
}

func (UnimplementedPostServiceHandler) GetMyPosts(context.Context, *connect.Request[v1.GetMyPostsRequest]) (*connect.Response[v1.GetAllPostsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostService.GetMyPosts is not implemented"))
}

func (UnimplementedPostServiceHandler) UpdatePostById(context.Context, *connect.Request[v1.UpdatePostByIdRequest]) (*connect.Response[v1.UpdatePostByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostService.UpdatePostById is not implemented"))
}

func (UnimplementedPostServiceHandler) GetVtuberGalleries(context.Context, *connect.Request[v1.GetVtuberGalleryRequest]) (*connect.Response[v1.GetAllPostsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.content.v1.PostService.GetVtuberGalleries is not implemented"))
}
