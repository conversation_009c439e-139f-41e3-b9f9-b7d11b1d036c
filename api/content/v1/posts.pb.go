// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: content/v1/posts.proto

package contentv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddPostRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Title            string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty" validate:"required"`
	Description      string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	Media            *string                `protobuf:"bytes,3,opt,name=media,proto3,oneof" json:"media,omitempty"`
	MediaType        *string                `protobuf:"bytes,4,opt,name=media_type,json=mediaType,proto3,oneof" json:"media_type,omitempty" validate:"omitempty,oneof=picture video"`
	Name             string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	MembershipOnly   bool                   `protobuf:"varint,6,opt,name=membership_only,json=membershipOnly,proto3" json:"membership_only,omitempty"`
	CategoryId       string                 `protobuf:"bytes,7,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" validate:"required"`
	ShortDescription string                 `protobuf:"bytes,8,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" validate:"required"`
	CampaignId       *string                `protobuf:"bytes,9,opt,name=campaign_id,json=campaignId,proto3,oneof" json:"campaign_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AddPostRequest) Reset() {
	*x = AddPostRequest{}
	mi := &file_content_v1_posts_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPostRequest) ProtoMessage() {}

func (x *AddPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPostRequest.ProtoReflect.Descriptor instead.
func (*AddPostRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{0}
}

func (x *AddPostRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AddPostRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddPostRequest) GetMedia() string {
	if x != nil && x.Media != nil {
		return *x.Media
	}
	return ""
}

func (x *AddPostRequest) GetMediaType() string {
	if x != nil && x.MediaType != nil {
		return *x.MediaType
	}
	return ""
}

func (x *AddPostRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddPostRequest) GetMembershipOnly() bool {
	if x != nil {
		return x.MembershipOnly
	}
	return false
}

func (x *AddPostRequest) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *AddPostRequest) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *AddPostRequest) GetCampaignId() string {
	if x != nil && x.CampaignId != nil {
		return *x.CampaignId
	}
	return ""
}

type AddPostResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Post                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPostResponse) Reset() {
	*x = AddPostResponse{}
	mi := &file_content_v1_posts_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPostResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPostResponse) ProtoMessage() {}

func (x *AddPostResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPostResponse.ProtoReflect.Descriptor instead.
func (*AddPostResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{1}
}

func (x *AddPostResponse) GetData() *Post {
	if x != nil {
		return x.Data
	}
	return nil
}

type Post struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title            string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description      string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Media            *string                `protobuf:"bytes,4,opt,name=media,proto3,oneof" json:"media,omitempty"`
	MediaType        *string                `protobuf:"bytes,5,opt,name=media_type,json=mediaType,proto3,oneof" json:"media_type,omitempty"`
	Name             string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	MembershipOnly   bool                   `protobuf:"varint,7,opt,name=membership_only,json=membershipOnly,proto3" json:"membership_only,omitempty"`
	CategoryId       string                 `protobuf:"bytes,8,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Vtuber           *v1.Profile            `protobuf:"bytes,9,opt,name=vtuber,proto3" json:"vtuber,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ShortDescription string                 `protobuf:"bytes,11,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty"`
	PostLikes        int64                  `protobuf:"varint,12,opt,name=post_likes,json=postLikes,proto3" json:"post_likes,omitempty"`
	HasLiked         bool                   `protobuf:"varint,13,opt,name=has_liked,json=hasLiked,proto3" json:"has_liked,omitempty"`
	PostComments     int64                  `protobuf:"varint,14,opt,name=post_comments,json=postComments,proto3" json:"post_comments,omitempty"`
	Slug             string                 `protobuf:"bytes,15,opt,name=slug,proto3" json:"slug,omitempty"`
	CampaignId       *string                `protobuf:"bytes,16,opt,name=campaign_id,json=campaignId,proto3,oneof" json:"campaign_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Post) Reset() {
	*x = Post{}
	mi := &file_content_v1_posts_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Post) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Post) ProtoMessage() {}

func (x *Post) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Post.ProtoReflect.Descriptor instead.
func (*Post) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{2}
}

func (x *Post) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Post) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Post) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Post) GetMedia() string {
	if x != nil && x.Media != nil {
		return *x.Media
	}
	return ""
}

func (x *Post) GetMediaType() string {
	if x != nil && x.MediaType != nil {
		return *x.MediaType
	}
	return ""
}

func (x *Post) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Post) GetMembershipOnly() bool {
	if x != nil {
		return x.MembershipOnly
	}
	return false
}

func (x *Post) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *Post) GetVtuber() *v1.Profile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

func (x *Post) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Post) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *Post) GetPostLikes() int64 {
	if x != nil {
		return x.PostLikes
	}
	return 0
}

func (x *Post) GetHasLiked() bool {
	if x != nil {
		return x.HasLiked
	}
	return false
}

func (x *Post) GetPostComments() int64 {
	if x != nil {
		return x.PostComments
	}
	return 0
}

func (x *Post) GetSlug() string {
	if x != nil {
		return x.Slug
	}
	return ""
}

func (x *Post) GetCampaignId() string {
	if x != nil && x.CampaignId != nil {
		return *x.CampaignId
	}
	return ""
}

type GetAllPostsRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	VtuberId       *string                `protobuf:"bytes,1,opt,name=vtuber_id,json=vtuberId,proto3,oneof" json:"vtuber_id,omitempty"`
	CategoryId     *string                `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	CampaignId     *string                `protobuf:"bytes,3,opt,name=campaign_id,json=campaignId,proto3,oneof" json:"campaign_id,omitempty"`
	Pagination     *v1.PaginationRequest  `protobuf:"bytes,4,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	VtuberUsername *string                `protobuf:"bytes,5,opt,name=vtuber_username,json=vtuberUsername,proto3,oneof" json:"vtuber_username,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetAllPostsRequest) Reset() {
	*x = GetAllPostsRequest{}
	mi := &file_content_v1_posts_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllPostsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllPostsRequest) ProtoMessage() {}

func (x *GetAllPostsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllPostsRequest.ProtoReflect.Descriptor instead.
func (*GetAllPostsRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllPostsRequest) GetVtuberId() string {
	if x != nil && x.VtuberId != nil {
		return *x.VtuberId
	}
	return ""
}

func (x *GetAllPostsRequest) GetCategoryId() string {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return ""
}

func (x *GetAllPostsRequest) GetCampaignId() string {
	if x != nil && x.CampaignId != nil {
		return *x.CampaignId
	}
	return ""
}

func (x *GetAllPostsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAllPostsRequest) GetVtuberUsername() string {
	if x != nil && x.VtuberUsername != nil {
		return *x.VtuberUsername
	}
	return ""
}

type GetAllPostsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*Post                `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllPostsResponse) Reset() {
	*x = GetAllPostsResponse{}
	mi := &file_content_v1_posts_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllPostsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllPostsResponse) ProtoMessage() {}

func (x *GetAllPostsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllPostsResponse.ProtoReflect.Descriptor instead.
func (*GetAllPostsResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllPostsResponse) GetData() []*Post {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllPostsResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetPostByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostByIdRequest) Reset() {
	*x = GetPostByIdRequest{}
	mi := &file_content_v1_posts_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostByIdRequest) ProtoMessage() {}

func (x *GetPostByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostByIdRequest.ProtoReflect.Descriptor instead.
func (*GetPostByIdRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{5}
}

func (x *GetPostByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetPostByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Post                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostByIdResponse) Reset() {
	*x = GetPostByIdResponse{}
	mi := &file_content_v1_posts_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostByIdResponse) ProtoMessage() {}

func (x *GetPostByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostByIdResponse.ProtoReflect.Descriptor instead.
func (*GetPostByIdResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{6}
}

func (x *GetPostByIdResponse) GetData() *Post {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeletePostByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePostByIdRequest) Reset() {
	*x = DeletePostByIdRequest{}
	mi := &file_content_v1_posts_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePostByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePostByIdRequest) ProtoMessage() {}

func (x *DeletePostByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePostByIdRequest.ProtoReflect.Descriptor instead.
func (*DeletePostByIdRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{7}
}

func (x *DeletePostByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetMyPostsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyPostsRequest) Reset() {
	*x = GetMyPostsRequest{}
	mi := &file_content_v1_posts_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyPostsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyPostsRequest) ProtoMessage() {}

func (x *GetMyPostsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyPostsRequest.ProtoReflect.Descriptor instead.
func (*GetMyPostsRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{8}
}

func (x *GetMyPostsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type UpdatePostByIdRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Title            string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty" validate:"required"`
	Description      string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	Media            *string                `protobuf:"bytes,3,opt,name=media,proto3,oneof" json:"media,omitempty"`
	MediaType        *string                `protobuf:"bytes,4,opt,name=media_type,json=mediaType,proto3,oneof" json:"media_type,omitempty" validate:"omitempty,oneof=picture video"`
	Name             string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty" validate:"required"`
	MembershipOnly   bool                   `protobuf:"varint,6,opt,name=membership_only,json=membershipOnly,proto3" json:"membership_only,omitempty"`
	CategoryId       string                 `protobuf:"bytes,7,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" validate:"required"`
	Id               string                 `protobuf:"bytes,9,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	ShortDescription string                 `protobuf:"bytes,10,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" validate:"required"`
	CampaignId       *string                `protobuf:"bytes,11,opt,name=campaign_id,json=campaignId,proto3,oneof" json:"campaign_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdatePostByIdRequest) Reset() {
	*x = UpdatePostByIdRequest{}
	mi := &file_content_v1_posts_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePostByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePostByIdRequest) ProtoMessage() {}

func (x *UpdatePostByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePostByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdatePostByIdRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{9}
}

func (x *UpdatePostByIdRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdatePostByIdRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdatePostByIdRequest) GetMedia() string {
	if x != nil && x.Media != nil {
		return *x.Media
	}
	return ""
}

func (x *UpdatePostByIdRequest) GetMediaType() string {
	if x != nil && x.MediaType != nil {
		return *x.MediaType
	}
	return ""
}

func (x *UpdatePostByIdRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdatePostByIdRequest) GetMembershipOnly() bool {
	if x != nil {
		return x.MembershipOnly
	}
	return false
}

func (x *UpdatePostByIdRequest) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *UpdatePostByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdatePostByIdRequest) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *UpdatePostByIdRequest) GetCampaignId() string {
	if x != nil && x.CampaignId != nil {
		return *x.CampaignId
	}
	return ""
}

type GetVtuberGalleryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VtuberId      string                 `protobuf:"bytes,1,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberGalleryRequest) Reset() {
	*x = GetVtuberGalleryRequest{}
	mi := &file_content_v1_posts_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberGalleryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberGalleryRequest) ProtoMessage() {}

func (x *GetVtuberGalleryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberGalleryRequest.ProtoReflect.Descriptor instead.
func (*GetVtuberGalleryRequest) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{10}
}

func (x *GetVtuberGalleryRequest) GetVtuberId() string {
	if x != nil {
		return x.VtuberId
	}
	return ""
}

func (x *GetVtuberGalleryRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type DeletePostByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePostByIdResponse) Reset() {
	*x = DeletePostByIdResponse{}
	mi := &file_content_v1_posts_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePostByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePostByIdResponse) ProtoMessage() {}

func (x *DeletePostByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePostByIdResponse.ProtoReflect.Descriptor instead.
func (*DeletePostByIdResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{11}
}

func (x *DeletePostByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeletePostByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdatePostByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePostByIdResponse) Reset() {
	*x = UpdatePostByIdResponse{}
	mi := &file_content_v1_posts_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePostByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePostByIdResponse) ProtoMessage() {}

func (x *UpdatePostByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_content_v1_posts_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePostByIdResponse.ProtoReflect.Descriptor instead.
func (*UpdatePostByIdResponse) Descriptor() ([]byte, []int) {
	return file_content_v1_posts_proto_rawDescGZIP(), []int{12}
}

func (x *UpdatePostByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdatePostByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_content_v1_posts_proto protoreflect.FileDescriptor

const file_content_v1_posts_proto_rawDesc = "" +
	"\n" +
	"\x16content/v1/posts.proto\x12\x0eapi.content.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\xe1\x02\n" +
	"\x0eAddPostRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x19\n" +
	"\x05media\x18\x03 \x01(\tH\x00R\x05media\x88\x01\x01\x12\"\n" +
	"\n" +
	"media_type\x18\x04 \x01(\tH\x01R\tmediaType\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12'\n" +
	"\x0fmembership_only\x18\x06 \x01(\bR\x0emembershipOnly\x12\x1f\n" +
	"\vcategory_id\x18\a \x01(\tR\n" +
	"categoryId\x12+\n" +
	"\x11short_description\x18\b \x01(\tR\x10shortDescription\x12$\n" +
	"\vcampaign_id\x18\t \x01(\tH\x02R\n" +
	"campaignId\x88\x01\x01B\b\n" +
	"\x06_mediaB\r\n" +
	"\v_media_typeB\x0e\n" +
	"\f_campaign_id\";\n" +
	"\x0fAddPostResponse\x12(\n" +
	"\x04data\x18\x01 \x01(\v2\x14.api.content.v1.PostR\x04data\"\xc7\x04\n" +
	"\x04Post\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x19\n" +
	"\x05media\x18\x04 \x01(\tH\x00R\x05media\x88\x01\x01\x12\"\n" +
	"\n" +
	"media_type\x18\x05 \x01(\tH\x01R\tmediaType\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12'\n" +
	"\x0fmembership_only\x18\a \x01(\bR\x0emembershipOnly\x12\x1f\n" +
	"\vcategory_id\x18\b \x01(\tR\n" +
	"categoryId\x12.\n" +
	"\x06vtuber\x18\t \x01(\v2\x16.api.shared.v1.ProfileR\x06vtuber\x129\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12+\n" +
	"\x11short_description\x18\v \x01(\tR\x10shortDescription\x12\x1d\n" +
	"\n" +
	"post_likes\x18\f \x01(\x03R\tpostLikes\x12\x1b\n" +
	"\thas_liked\x18\r \x01(\bR\bhasLiked\x12#\n" +
	"\rpost_comments\x18\x0e \x01(\x03R\fpostComments\x12\x12\n" +
	"\x04slug\x18\x0f \x01(\tR\x04slug\x12$\n" +
	"\vcampaign_id\x18\x10 \x01(\tH\x02R\n" +
	"campaignId\x88\x01\x01B\b\n" +
	"\x06_mediaB\r\n" +
	"\v_media_typeB\x0e\n" +
	"\f_campaign_id\"\xc8\x02\n" +
	"\x12GetAllPostsRequest\x12 \n" +
	"\tvtuber_id\x18\x01 \x01(\tH\x00R\bvtuberId\x88\x01\x01\x12$\n" +
	"\vcategory_id\x18\x02 \x01(\tH\x01R\n" +
	"categoryId\x88\x01\x01\x12$\n" +
	"\vcampaign_id\x18\x03 \x01(\tH\x02R\n" +
	"campaignId\x88\x01\x01\x12E\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2 .api.shared.v1.PaginationRequestH\x03R\n" +
	"pagination\x88\x01\x01\x12,\n" +
	"\x0fvtuber_username\x18\x05 \x01(\tH\x04R\x0evtuberUsername\x88\x01\x01B\f\n" +
	"\n" +
	"_vtuber_idB\x0e\n" +
	"\f_category_idB\x0e\n" +
	"\f_campaign_idB\r\n" +
	"\v_paginationB\x12\n" +
	"\x10_vtuber_username\"\x90\x01\n" +
	"\x13GetAllPostsResponse\x12(\n" +
	"\x04data\x18\x01 \x03(\v2\x14.api.content.v1.PostR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"$\n" +
	"\x12GetPostByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"?\n" +
	"\x13GetPostByIdResponse\x12(\n" +
	"\x04data\x18\x01 \x01(\v2\x14.api.content.v1.PostR\x04data\"'\n" +
	"\x15DeletePostByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"i\n" +
	"\x11GetMyPostsRequest\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\xf8\x02\n" +
	"\x15UpdatePostByIdRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x19\n" +
	"\x05media\x18\x03 \x01(\tH\x00R\x05media\x88\x01\x01\x12\"\n" +
	"\n" +
	"media_type\x18\x04 \x01(\tH\x01R\tmediaType\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12'\n" +
	"\x0fmembership_only\x18\x06 \x01(\bR\x0emembershipOnly\x12\x1f\n" +
	"\vcategory_id\x18\a \x01(\tR\n" +
	"categoryId\x12\x0e\n" +
	"\x02id\x18\t \x01(\tR\x02id\x12+\n" +
	"\x11short_description\x18\n" +
	" \x01(\tR\x10shortDescription\x12$\n" +
	"\vcampaign_id\x18\v \x01(\tH\x02R\n" +
	"campaignId\x88\x01\x01B\b\n" +
	"\x06_mediaB\r\n" +
	"\v_media_typeB\x0e\n" +
	"\f_campaign_id\"\x8c\x01\n" +
	"\x17GetVtuberGalleryRequest\x12\x1b\n" +
	"\tvtuber_id\x18\x01 \x01(\tR\bvtuberId\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"L\n" +
	"\x16DeletePostByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"L\n" +
	"\x16UpdatePostByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\x89\x06\n" +
	"\vPostService\x12h\n" +
	"\aAddPost\x12\x1e.api.content.v1.AddPostRequest\x1a\x1f.api.content.v1.AddPostResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=null\x12X\n" +
	"\vGetAllPosts\x12\".api.content.v1.GetAllPostsRequest\x1a#.api.content.v1.GetAllPostsResponse\"\x00\x12^\n" +
	"\vGetPostById\x12\".api.content.v1.GetPostByIdRequest\x1a#.api.content.v1.GetPostByIdResponse\"\x06\x82\xb5\x18\x02\b\x01\x12}\n" +
	"\x0eDeletePostById\x12%.api.content.v1.DeletePostByIdRequest\x1a&.api.content.v1.DeletePostByIdResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=null\x12r\n" +
	"\n" +
	"GetMyPosts\x12!.api.content.v1.GetMyPostsRequest\x1a#.api.content.v1.GetAllPostsResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=null\x12}\n" +
	"\x0eUpdatePostById\x12%.api.content.v1.UpdatePostByIdRequest\x1a&.api.content.v1.UpdatePostByIdResponse\"\x1c\x82\xb5\x18\x18\b\x01\"\x14_user.vtuberId!=null\x12d\n" +
	"\x12GetVtuberGalleries\x12'.api.content.v1.GetVtuberGalleryRequest\x1a#.api.content.v1.GetAllPostsResponse\"\x00B4Z2github.com/nsp-inc/vtuber/api/content/v1;contentv1b\x06proto3"

var (
	file_content_v1_posts_proto_rawDescOnce sync.Once
	file_content_v1_posts_proto_rawDescData []byte
)

func file_content_v1_posts_proto_rawDescGZIP() []byte {
	file_content_v1_posts_proto_rawDescOnce.Do(func() {
		file_content_v1_posts_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_content_v1_posts_proto_rawDesc), len(file_content_v1_posts_proto_rawDesc)))
	})
	return file_content_v1_posts_proto_rawDescData
}

var file_content_v1_posts_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_content_v1_posts_proto_goTypes = []any{
	(*AddPostRequest)(nil),          // 0: api.content.v1.AddPostRequest
	(*AddPostResponse)(nil),         // 1: api.content.v1.AddPostResponse
	(*Post)(nil),                    // 2: api.content.v1.Post
	(*GetAllPostsRequest)(nil),      // 3: api.content.v1.GetAllPostsRequest
	(*GetAllPostsResponse)(nil),     // 4: api.content.v1.GetAllPostsResponse
	(*GetPostByIdRequest)(nil),      // 5: api.content.v1.GetPostByIdRequest
	(*GetPostByIdResponse)(nil),     // 6: api.content.v1.GetPostByIdResponse
	(*DeletePostByIdRequest)(nil),   // 7: api.content.v1.DeletePostByIdRequest
	(*GetMyPostsRequest)(nil),       // 8: api.content.v1.GetMyPostsRequest
	(*UpdatePostByIdRequest)(nil),   // 9: api.content.v1.UpdatePostByIdRequest
	(*GetVtuberGalleryRequest)(nil), // 10: api.content.v1.GetVtuberGalleryRequest
	(*DeletePostByIdResponse)(nil),  // 11: api.content.v1.DeletePostByIdResponse
	(*UpdatePostByIdResponse)(nil),  // 12: api.content.v1.UpdatePostByIdResponse
	(*v1.Profile)(nil),              // 13: api.shared.v1.Profile
	(*timestamppb.Timestamp)(nil),   // 14: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),    // 15: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),    // 16: api.shared.v1.PaginationDetails
}
var file_content_v1_posts_proto_depIdxs = []int32{
	2,  // 0: api.content.v1.AddPostResponse.data:type_name -> api.content.v1.Post
	13, // 1: api.content.v1.Post.vtuber:type_name -> api.shared.v1.Profile
	14, // 2: api.content.v1.Post.created_at:type_name -> google.protobuf.Timestamp
	15, // 3: api.content.v1.GetAllPostsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 4: api.content.v1.GetAllPostsResponse.data:type_name -> api.content.v1.Post
	16, // 5: api.content.v1.GetAllPostsResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	2,  // 6: api.content.v1.GetPostByIdResponse.data:type_name -> api.content.v1.Post
	15, // 7: api.content.v1.GetMyPostsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	15, // 8: api.content.v1.GetVtuberGalleryRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	0,  // 9: api.content.v1.PostService.AddPost:input_type -> api.content.v1.AddPostRequest
	3,  // 10: api.content.v1.PostService.GetAllPosts:input_type -> api.content.v1.GetAllPostsRequest
	5,  // 11: api.content.v1.PostService.GetPostById:input_type -> api.content.v1.GetPostByIdRequest
	7,  // 12: api.content.v1.PostService.DeletePostById:input_type -> api.content.v1.DeletePostByIdRequest
	8,  // 13: api.content.v1.PostService.GetMyPosts:input_type -> api.content.v1.GetMyPostsRequest
	9,  // 14: api.content.v1.PostService.UpdatePostById:input_type -> api.content.v1.UpdatePostByIdRequest
	10, // 15: api.content.v1.PostService.GetVtuberGalleries:input_type -> api.content.v1.GetVtuberGalleryRequest
	1,  // 16: api.content.v1.PostService.AddPost:output_type -> api.content.v1.AddPostResponse
	4,  // 17: api.content.v1.PostService.GetAllPosts:output_type -> api.content.v1.GetAllPostsResponse
	6,  // 18: api.content.v1.PostService.GetPostById:output_type -> api.content.v1.GetPostByIdResponse
	11, // 19: api.content.v1.PostService.DeletePostById:output_type -> api.content.v1.DeletePostByIdResponse
	4,  // 20: api.content.v1.PostService.GetMyPosts:output_type -> api.content.v1.GetAllPostsResponse
	12, // 21: api.content.v1.PostService.UpdatePostById:output_type -> api.content.v1.UpdatePostByIdResponse
	4,  // 22: api.content.v1.PostService.GetVtuberGalleries:output_type -> api.content.v1.GetAllPostsResponse
	16, // [16:23] is the sub-list for method output_type
	9,  // [9:16] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_content_v1_posts_proto_init() }
func file_content_v1_posts_proto_init() {
	if File_content_v1_posts_proto != nil {
		return
	}
	file_content_v1_posts_proto_msgTypes[0].OneofWrappers = []any{}
	file_content_v1_posts_proto_msgTypes[2].OneofWrappers = []any{}
	file_content_v1_posts_proto_msgTypes[3].OneofWrappers = []any{}
	file_content_v1_posts_proto_msgTypes[8].OneofWrappers = []any{}
	file_content_v1_posts_proto_msgTypes[9].OneofWrappers = []any{}
	file_content_v1_posts_proto_msgTypes[10].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_content_v1_posts_proto_rawDesc), len(file_content_v1_posts_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_content_v1_posts_proto_goTypes,
		DependencyIndexes: file_content_v1_posts_proto_depIdxs,
		MessageInfos:      file_content_v1_posts_proto_msgTypes,
	}.Build()
	File_content_v1_posts_proto = out.File
	file_content_v1_posts_proto_goTypes = nil
	file_content_v1_posts_proto_depIdxs = nil
}
