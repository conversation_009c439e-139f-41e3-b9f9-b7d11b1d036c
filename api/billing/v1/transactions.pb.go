// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/v1/transactions.proto

package billingv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddTransactionRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Amount                int32                  `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty" validate:"required"`
	CampVariantId         *string                `protobuf:"bytes,2,opt,name=camp_variant_id,json=campVariantId,proto3,oneof" json:"camp_variant_id,omitempty"`
	CreatorSubscriptionId *string                `protobuf:"bytes,3,opt,name=creator_subscription_id,json=creatorSubscriptionId,proto3,oneof" json:"creator_subscription_id,omitempty"`
	Status                string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty" validate:"required"`
	IsRecurring           bool                   `protobuf:"varint,5,opt,name=is_recurring,json=isRecurring,proto3" json:"is_recurring,omitempty"`
	ExpiresOn             *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=expires_on,json=expiresOn,proto3" json:"expires_on,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AddTransactionRequest) Reset() {
	*x = AddTransactionRequest{}
	mi := &file_billing_v1_transactions_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTransactionRequest) ProtoMessage() {}

func (x *AddTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_transactions_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTransactionRequest.ProtoReflect.Descriptor instead.
func (*AddTransactionRequest) Descriptor() ([]byte, []int) {
	return file_billing_v1_transactions_proto_rawDescGZIP(), []int{0}
}

func (x *AddTransactionRequest) GetAmount() int32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *AddTransactionRequest) GetCampVariantId() string {
	if x != nil && x.CampVariantId != nil {
		return *x.CampVariantId
	}
	return ""
}

func (x *AddTransactionRequest) GetCreatorSubscriptionId() string {
	if x != nil && x.CreatorSubscriptionId != nil {
		return *x.CreatorSubscriptionId
	}
	return ""
}

func (x *AddTransactionRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AddTransactionRequest) GetIsRecurring() bool {
	if x != nil {
		return x.IsRecurring
	}
	return false
}

func (x *AddTransactionRequest) GetExpiresOn() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresOn
	}
	return nil
}

type AddTransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Transaction           `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddTransactionResponse) Reset() {
	*x = AddTransactionResponse{}
	mi := &file_billing_v1_transactions_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTransactionResponse) ProtoMessage() {}

func (x *AddTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_transactions_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTransactionResponse.ProtoReflect.Descriptor instead.
func (*AddTransactionResponse) Descriptor() ([]byte, []int) {
	return file_billing_v1_transactions_proto_rawDescGZIP(), []int{1}
}

func (x *AddTransactionResponse) GetData() *Transaction {
	if x != nil {
		return x.Data
	}
	return nil
}

type Transaction struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId                int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Amount                int32                  `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Type                  string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	CampVariantId         *string                `protobuf:"bytes,5,opt,name=camp_variant_id,json=campVariantId,proto3,oneof" json:"camp_variant_id,omitempty"`
	CreatorSubscriptionId *string                `protobuf:"bytes,6,opt,name=creator_subscription_id,json=creatorSubscriptionId,proto3,oneof" json:"creator_subscription_id,omitempty"`
	Status                string                 `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt             *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	mi := &file_billing_v1_transactions_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_transactions_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_billing_v1_transactions_proto_rawDescGZIP(), []int{2}
}

func (x *Transaction) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Transaction) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Transaction) GetAmount() int32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Transaction) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Transaction) GetCampVariantId() string {
	if x != nil && x.CampVariantId != nil {
		return *x.CampVariantId
	}
	return ""
}

func (x *Transaction) GetCreatorSubscriptionId() string {
	if x != nil && x.CreatorSubscriptionId != nil {
		return *x.CreatorSubscriptionId
	}
	return ""
}

func (x *Transaction) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Transaction) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type GetAllTransactionsRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	CreatorSubscriptionId *string                `protobuf:"bytes,1,opt,name=creator_subscription_id,json=creatorSubscriptionId,proto3,oneof" json:"creator_subscription_id,omitempty"`
	UserId                *string                `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`
	CampVariantId         *string                `protobuf:"bytes,3,opt,name=camp_variant_id,json=campVariantId,proto3,oneof" json:"camp_variant_id,omitempty"`
	Pagination            *v1.PaginationRequest  `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetAllTransactionsRequest) Reset() {
	*x = GetAllTransactionsRequest{}
	mi := &file_billing_v1_transactions_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllTransactionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllTransactionsRequest) ProtoMessage() {}

func (x *GetAllTransactionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_transactions_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllTransactionsRequest.ProtoReflect.Descriptor instead.
func (*GetAllTransactionsRequest) Descriptor() ([]byte, []int) {
	return file_billing_v1_transactions_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllTransactionsRequest) GetCreatorSubscriptionId() string {
	if x != nil && x.CreatorSubscriptionId != nil {
		return *x.CreatorSubscriptionId
	}
	return ""
}

func (x *GetAllTransactionsRequest) GetUserId() string {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return ""
}

func (x *GetAllTransactionsRequest) GetCampVariantId() string {
	if x != nil && x.CampVariantId != nil {
		return *x.CampVariantId
	}
	return ""
}

func (x *GetAllTransactionsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetAllTransactionsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*Transaction         `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllTransactionsResponse) Reset() {
	*x = GetAllTransactionsResponse{}
	mi := &file_billing_v1_transactions_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllTransactionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllTransactionsResponse) ProtoMessage() {}

func (x *GetAllTransactionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_transactions_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllTransactionsResponse.ProtoReflect.Descriptor instead.
func (*GetAllTransactionsResponse) Descriptor() ([]byte, []int) {
	return file_billing_v1_transactions_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllTransactionsResponse) GetData() []*Transaction {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllTransactionsResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetAllTransactionsOfUserRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	CreatorSubscriptionId *string                `protobuf:"bytes,1,opt,name=creator_subscription_id,json=creatorSubscriptionId,proto3,oneof" json:"creator_subscription_id,omitempty"`
	CampVariantId         *string                `protobuf:"bytes,3,opt,name=camp_variant_id,json=campVariantId,proto3,oneof" json:"camp_variant_id,omitempty"`
	Pagination            *v1.PaginationRequest  `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetAllTransactionsOfUserRequest) Reset() {
	*x = GetAllTransactionsOfUserRequest{}
	mi := &file_billing_v1_transactions_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllTransactionsOfUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllTransactionsOfUserRequest) ProtoMessage() {}

func (x *GetAllTransactionsOfUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_transactions_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllTransactionsOfUserRequest.ProtoReflect.Descriptor instead.
func (*GetAllTransactionsOfUserRequest) Descriptor() ([]byte, []int) {
	return file_billing_v1_transactions_proto_rawDescGZIP(), []int{5}
}

func (x *GetAllTransactionsOfUserRequest) GetCreatorSubscriptionId() string {
	if x != nil && x.CreatorSubscriptionId != nil {
		return *x.CreatorSubscriptionId
	}
	return ""
}

func (x *GetAllTransactionsOfUserRequest) GetCampVariantId() string {
	if x != nil && x.CampVariantId != nil {
		return *x.CampVariantId
	}
	return ""
}

func (x *GetAllTransactionsOfUserRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetTransactionByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransactionByIdRequest) Reset() {
	*x = GetTransactionByIdRequest{}
	mi := &file_billing_v1_transactions_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionByIdRequest) ProtoMessage() {}

func (x *GetTransactionByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_transactions_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionByIdRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionByIdRequest) Descriptor() ([]byte, []int) {
	return file_billing_v1_transactions_proto_rawDescGZIP(), []int{6}
}

func (x *GetTransactionByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetTransactionByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Transaction           `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTransactionByIdResponse) Reset() {
	*x = GetTransactionByIdResponse{}
	mi := &file_billing_v1_transactions_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTransactionByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionByIdResponse) ProtoMessage() {}

func (x *GetTransactionByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_transactions_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionByIdResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionByIdResponse) Descriptor() ([]byte, []int) {
	return file_billing_v1_transactions_proto_rawDescGZIP(), []int{7}
}

func (x *GetTransactionByIdResponse) GetData() *Transaction {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_billing_v1_transactions_proto protoreflect.FileDescriptor

const file_billing_v1_transactions_proto_rawDesc = "" +
	"\n" +
	"\x1dbilling/v1/transactions.proto\x12\n" +
	"billing.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\"\xbf\x02\n" +
	"\x15AddTransactionRequest\x12\x16\n" +
	"\x06amount\x18\x01 \x01(\x05R\x06amount\x12+\n" +
	"\x0fcamp_variant_id\x18\x02 \x01(\tH\x00R\rcampVariantId\x88\x01\x01\x12;\n" +
	"\x17creator_subscription_id\x18\x03 \x01(\tH\x01R\x15creatorSubscriptionId\x88\x01\x01\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12!\n" +
	"\fis_recurring\x18\x05 \x01(\bR\visRecurring\x129\n" +
	"\n" +
	"expires_on\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\texpiresOnB\x12\n" +
	"\x10_camp_variant_idB\x1a\n" +
	"\x18_creator_subscription_id\"E\n" +
	"\x16AddTransactionResponse\x12+\n" +
	"\x04data\x18\x02 \x01(\v2\x17.billing.v1.TransactionR\x04data\"\xcf\x02\n" +
	"\vTransaction\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\x05R\x06amount\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12+\n" +
	"\x0fcamp_variant_id\x18\x05 \x01(\tH\x00R\rcampVariantId\x88\x01\x01\x12;\n" +
	"\x17creator_subscription_id\x18\x06 \x01(\tH\x01R\x15creatorSubscriptionId\x88\x01\x01\x12\x16\n" +
	"\x06status\x18\a \x01(\tR\x06status\x129\n" +
	"\n" +
	"created_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAtB\x12\n" +
	"\x10_camp_variant_idB\x1a\n" +
	"\x18_creator_subscription_id\"\xa1\x02\n" +
	"\x19GetAllTransactionsRequest\x12;\n" +
	"\x17creator_subscription_id\x18\x01 \x01(\tH\x00R\x15creatorSubscriptionId\x88\x01\x01\x12\x1c\n" +
	"\auser_id\x18\x02 \x01(\tH\x01R\x06userId\x88\x01\x01\x12+\n" +
	"\x0fcamp_variant_id\x18\x03 \x01(\tH\x02R\rcampVariantId\x88\x01\x01\x12@\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2 .api.shared.v1.PaginationRequestR\n" +
	"paginationB\x1a\n" +
	"\x18_creator_subscription_idB\n" +
	"\n" +
	"\b_user_idB\x12\n" +
	"\x10_camp_variant_id\"\x9a\x01\n" +
	"\x1aGetAllTransactionsResponse\x12+\n" +
	"\x04data\x18\x01 \x03(\v2\x17.billing.v1.TransactionR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\xfd\x01\n" +
	"\x1fGetAllTransactionsOfUserRequest\x12;\n" +
	"\x17creator_subscription_id\x18\x01 \x01(\tH\x00R\x15creatorSubscriptionId\x88\x01\x01\x12+\n" +
	"\x0fcamp_variant_id\x18\x03 \x01(\tH\x01R\rcampVariantId\x88\x01\x01\x12@\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2 .api.shared.v1.PaginationRequestR\n" +
	"paginationB\x1a\n" +
	"\x18_creator_subscription_idB\x12\n" +
	"\x10_camp_variant_id\"+\n" +
	"\x19GetTransactionByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"I\n" +
	"\x1aGetTransactionByIdResponse\x12+\n" +
	"\x04data\x18\x02 \x01(\v2\x17.billing.v1.TransactionR\x04data2\xca\x03\n" +
	"\x12TransactionService\x12_\n" +
	"\x0eAddTransaction\x12!.billing.v1.AddTransactionRequest\x1a\".billing.v1.AddTransactionResponse\"\x06\x82\xb5\x18\x02\b\x01\x12m\n" +
	"\x12GetAllTransactions\x12%.billing.v1.GetAllTransactionsRequest\x1a&.billing.v1.GetAllTransactionsResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12w\n" +
	"\x18GetAllTransactionsOfUser\x12+.billing.v1.GetAllTransactionsOfUserRequest\x1a&.billing.v1.GetAllTransactionsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12k\n" +
	"\x12GetTransactionById\x12%.billing.v1.GetTransactionByIdRequest\x1a&.billing.v1.GetTransactionByIdResponse\"\x06\x82\xb5\x18\x02\b\x01B4Z2github.com/nsp-inc/vtuber/api/billing/v1;billingv1b\x06proto3"

var (
	file_billing_v1_transactions_proto_rawDescOnce sync.Once
	file_billing_v1_transactions_proto_rawDescData []byte
)

func file_billing_v1_transactions_proto_rawDescGZIP() []byte {
	file_billing_v1_transactions_proto_rawDescOnce.Do(func() {
		file_billing_v1_transactions_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_v1_transactions_proto_rawDesc), len(file_billing_v1_transactions_proto_rawDesc)))
	})
	return file_billing_v1_transactions_proto_rawDescData
}

var file_billing_v1_transactions_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_billing_v1_transactions_proto_goTypes = []any{
	(*AddTransactionRequest)(nil),           // 0: billing.v1.AddTransactionRequest
	(*AddTransactionResponse)(nil),          // 1: billing.v1.AddTransactionResponse
	(*Transaction)(nil),                     // 2: billing.v1.Transaction
	(*GetAllTransactionsRequest)(nil),       // 3: billing.v1.GetAllTransactionsRequest
	(*GetAllTransactionsResponse)(nil),      // 4: billing.v1.GetAllTransactionsResponse
	(*GetAllTransactionsOfUserRequest)(nil), // 5: billing.v1.GetAllTransactionsOfUserRequest
	(*GetTransactionByIdRequest)(nil),       // 6: billing.v1.GetTransactionByIdRequest
	(*GetTransactionByIdResponse)(nil),      // 7: billing.v1.GetTransactionByIdResponse
	(*timestamppb.Timestamp)(nil),           // 8: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),            // 9: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),            // 10: api.shared.v1.PaginationDetails
}
var file_billing_v1_transactions_proto_depIdxs = []int32{
	8,  // 0: billing.v1.AddTransactionRequest.expires_on:type_name -> google.protobuf.Timestamp
	2,  // 1: billing.v1.AddTransactionResponse.data:type_name -> billing.v1.Transaction
	8,  // 2: billing.v1.Transaction.created_at:type_name -> google.protobuf.Timestamp
	9,  // 3: billing.v1.GetAllTransactionsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 4: billing.v1.GetAllTransactionsResponse.data:type_name -> billing.v1.Transaction
	10, // 5: billing.v1.GetAllTransactionsResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	9,  // 6: billing.v1.GetAllTransactionsOfUserRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 7: billing.v1.GetTransactionByIdResponse.data:type_name -> billing.v1.Transaction
	0,  // 8: billing.v1.TransactionService.AddTransaction:input_type -> billing.v1.AddTransactionRequest
	3,  // 9: billing.v1.TransactionService.GetAllTransactions:input_type -> billing.v1.GetAllTransactionsRequest
	5,  // 10: billing.v1.TransactionService.GetAllTransactionsOfUser:input_type -> billing.v1.GetAllTransactionsOfUserRequest
	6,  // 11: billing.v1.TransactionService.GetTransactionById:input_type -> billing.v1.GetTransactionByIdRequest
	1,  // 12: billing.v1.TransactionService.AddTransaction:output_type -> billing.v1.AddTransactionResponse
	4,  // 13: billing.v1.TransactionService.GetAllTransactions:output_type -> billing.v1.GetAllTransactionsResponse
	4,  // 14: billing.v1.TransactionService.GetAllTransactionsOfUser:output_type -> billing.v1.GetAllTransactionsResponse
	7,  // 15: billing.v1.TransactionService.GetTransactionById:output_type -> billing.v1.GetTransactionByIdResponse
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_billing_v1_transactions_proto_init() }
func file_billing_v1_transactions_proto_init() {
	if File_billing_v1_transactions_proto != nil {
		return
	}
	file_billing_v1_transactions_proto_msgTypes[0].OneofWrappers = []any{}
	file_billing_v1_transactions_proto_msgTypes[2].OneofWrappers = []any{}
	file_billing_v1_transactions_proto_msgTypes[3].OneofWrappers = []any{}
	file_billing_v1_transactions_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_v1_transactions_proto_rawDesc), len(file_billing_v1_transactions_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_v1_transactions_proto_goTypes,
		DependencyIndexes: file_billing_v1_transactions_proto_depIdxs,
		MessageInfos:      file_billing_v1_transactions_proto_msgTypes,
	}.Build()
	File_billing_v1_transactions_proto = out.File
	file_billing_v1_transactions_proto_goTypes = nil
	file_billing_v1_transactions_proto_depIdxs = nil
}
