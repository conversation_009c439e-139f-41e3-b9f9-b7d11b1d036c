// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/v1/user_billing_infos.proto

package billingv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddBillingInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FullName      string                 `protobuf:"bytes,1,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty" validate:"required"`
	Address_1     string                 `protobuf:"bytes,2,opt,name=address_1,json=address1,proto3" json:"address_1,omitempty" validate:"required"`
	Address_2     *string                `protobuf:"bytes,3,opt,name=address_2,json=address2,proto3,oneof" json:"address_2,omitempty"`
	City          string                 `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty" validate:"required"`
	State         string                 `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty" validate:"required"`
	Country       string                 `protobuf:"bytes,6,opt,name=country,proto3" json:"country,omitempty" validate:"required"`
	PostalCode    string                 `protobuf:"bytes,7,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty" validate:"required"`
	CompanyName   *string                `protobuf:"bytes,8,opt,name=company_name,json=companyName,proto3,oneof" json:"company_name,omitempty"`
	VatNumber     *string                `protobuf:"bytes,9,opt,name=vat_number,json=vatNumber,proto3,oneof" json:"vat_number,omitempty"`
	CardNo        string                 `protobuf:"bytes,10,opt,name=card_no,json=cardNo,proto3" json:"card_no,omitempty" validate:"required"`
	CardExpiry    string                 `protobuf:"bytes,11,opt,name=card_expiry,json=cardExpiry,proto3" json:"card_expiry,omitempty" validate:"required"`
	CardCvc       string                 `protobuf:"bytes,12,opt,name=card_cvc,json=cardCvc,proto3" json:"card_cvc,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddBillingInfoRequest) Reset() {
	*x = AddBillingInfoRequest{}
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddBillingInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBillingInfoRequest) ProtoMessage() {}

func (x *AddBillingInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBillingInfoRequest.ProtoReflect.Descriptor instead.
func (*AddBillingInfoRequest) Descriptor() ([]byte, []int) {
	return file_billing_v1_user_billing_infos_proto_rawDescGZIP(), []int{0}
}

func (x *AddBillingInfoRequest) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *AddBillingInfoRequest) GetAddress_1() string {
	if x != nil {
		return x.Address_1
	}
	return ""
}

func (x *AddBillingInfoRequest) GetAddress_2() string {
	if x != nil && x.Address_2 != nil {
		return *x.Address_2
	}
	return ""
}

func (x *AddBillingInfoRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *AddBillingInfoRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *AddBillingInfoRequest) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *AddBillingInfoRequest) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *AddBillingInfoRequest) GetCompanyName() string {
	if x != nil && x.CompanyName != nil {
		return *x.CompanyName
	}
	return ""
}

func (x *AddBillingInfoRequest) GetVatNumber() string {
	if x != nil && x.VatNumber != nil {
		return *x.VatNumber
	}
	return ""
}

func (x *AddBillingInfoRequest) GetCardNo() string {
	if x != nil {
		return x.CardNo
	}
	return ""
}

func (x *AddBillingInfoRequest) GetCardExpiry() string {
	if x != nil {
		return x.CardExpiry
	}
	return ""
}

func (x *AddBillingInfoRequest) GetCardCvc() string {
	if x != nil {
		return x.CardCvc
	}
	return ""
}

type AddBillingInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *BillingInfo           `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddBillingInfoResponse) Reset() {
	*x = AddBillingInfoResponse{}
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddBillingInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBillingInfoResponse) ProtoMessage() {}

func (x *AddBillingInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBillingInfoResponse.ProtoReflect.Descriptor instead.
func (*AddBillingInfoResponse) Descriptor() ([]byte, []int) {
	return file_billing_v1_user_billing_infos_proto_rawDescGZIP(), []int{1}
}

func (x *AddBillingInfoResponse) GetData() *BillingInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type BillingInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FullName      string                 `protobuf:"bytes,2,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Address_1     string                 `protobuf:"bytes,3,opt,name=address_1,json=address1,proto3" json:"address_1,omitempty"`
	Address_2     *string                `protobuf:"bytes,4,opt,name=address_2,json=address2,proto3,oneof" json:"address_2,omitempty"`
	City          string                 `protobuf:"bytes,5,opt,name=city,proto3" json:"city,omitempty"`
	State         *string                `protobuf:"bytes,6,opt,name=state,proto3,oneof" json:"state,omitempty"`
	Country       string                 `protobuf:"bytes,7,opt,name=country,proto3" json:"country,omitempty"`
	PostalCode    string                 `protobuf:"bytes,8,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	CompanyName   *string                `protobuf:"bytes,9,opt,name=company_name,json=companyName,proto3,oneof" json:"company_name,omitempty"`
	VatNumber     *string                `protobuf:"bytes,10,opt,name=vat_number,json=vatNumber,proto3,oneof" json:"vat_number,omitempty"`
	CardNo        string                 `protobuf:"bytes,11,opt,name=card_no,json=cardNo,proto3" json:"card_no,omitempty"`
	CardExpiry    string                 `protobuf:"bytes,12,opt,name=card_expiry,json=cardExpiry,proto3" json:"card_expiry,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BillingInfo) Reset() {
	*x = BillingInfo{}
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BillingInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingInfo) ProtoMessage() {}

func (x *BillingInfo) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingInfo.ProtoReflect.Descriptor instead.
func (*BillingInfo) Descriptor() ([]byte, []int) {
	return file_billing_v1_user_billing_infos_proto_rawDescGZIP(), []int{2}
}

func (x *BillingInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BillingInfo) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *BillingInfo) GetAddress_1() string {
	if x != nil {
		return x.Address_1
	}
	return ""
}

func (x *BillingInfo) GetAddress_2() string {
	if x != nil && x.Address_2 != nil {
		return *x.Address_2
	}
	return ""
}

func (x *BillingInfo) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *BillingInfo) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *BillingInfo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *BillingInfo) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *BillingInfo) GetCompanyName() string {
	if x != nil && x.CompanyName != nil {
		return *x.CompanyName
	}
	return ""
}

func (x *BillingInfo) GetVatNumber() string {
	if x != nil && x.VatNumber != nil {
		return *x.VatNumber
	}
	return ""
}

func (x *BillingInfo) GetCardNo() string {
	if x != nil {
		return x.CardNo
	}
	return ""
}

func (x *BillingInfo) GetCardExpiry() string {
	if x != nil {
		return x.CardExpiry
	}
	return ""
}

type GetBillingInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBillingInfoRequest) Reset() {
	*x = GetBillingInfoRequest{}
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBillingInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBillingInfoRequest) ProtoMessage() {}

func (x *GetBillingInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBillingInfoRequest.ProtoReflect.Descriptor instead.
func (*GetBillingInfoRequest) Descriptor() ([]byte, []int) {
	return file_billing_v1_user_billing_infos_proto_rawDescGZIP(), []int{3}
}

type GetBillingInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*BillingInfo         `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBillingInfoResponse) Reset() {
	*x = GetBillingInfoResponse{}
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBillingInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBillingInfoResponse) ProtoMessage() {}

func (x *GetBillingInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBillingInfoResponse.ProtoReflect.Descriptor instead.
func (*GetBillingInfoResponse) Descriptor() ([]byte, []int) {
	return file_billing_v1_user_billing_infos_proto_rawDescGZIP(), []int{4}
}

func (x *GetBillingInfoResponse) GetData() []*BillingInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateBillingInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	FullName      string                 `protobuf:"bytes,2,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty" validate:"required"`
	Address_1     string                 `protobuf:"bytes,3,opt,name=address_1,json=address1,proto3" json:"address_1,omitempty" validate:"required"`
	Address_2     *string                `protobuf:"bytes,4,opt,name=address_2,json=address2,proto3,oneof" json:"address_2,omitempty"`
	City          string                 `protobuf:"bytes,5,opt,name=city,proto3" json:"city,omitempty" validate:"required"`
	State         string                 `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty" validate:"required"`
	Country       string                 `protobuf:"bytes,7,opt,name=country,proto3" json:"country,omitempty" validate:"required"`
	PostalCode    string                 `protobuf:"bytes,8,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty" validate:"required"`
	CompanyName   *string                `protobuf:"bytes,9,opt,name=company_name,json=companyName,proto3,oneof" json:"company_name,omitempty"`
	VatNumber     *string                `protobuf:"bytes,10,opt,name=vat_number,json=vatNumber,proto3,oneof" json:"vat_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateBillingInfoRequest) Reset() {
	*x = UpdateBillingInfoRequest{}
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBillingInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBillingInfoRequest) ProtoMessage() {}

func (x *UpdateBillingInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBillingInfoRequest.ProtoReflect.Descriptor instead.
func (*UpdateBillingInfoRequest) Descriptor() ([]byte, []int) {
	return file_billing_v1_user_billing_infos_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateBillingInfoRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateBillingInfoRequest) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *UpdateBillingInfoRequest) GetAddress_1() string {
	if x != nil {
		return x.Address_1
	}
	return ""
}

func (x *UpdateBillingInfoRequest) GetAddress_2() string {
	if x != nil && x.Address_2 != nil {
		return *x.Address_2
	}
	return ""
}

func (x *UpdateBillingInfoRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *UpdateBillingInfoRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *UpdateBillingInfoRequest) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *UpdateBillingInfoRequest) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *UpdateBillingInfoRequest) GetCompanyName() string {
	if x != nil && x.CompanyName != nil {
		return *x.CompanyName
	}
	return ""
}

func (x *UpdateBillingInfoRequest) GetVatNumber() string {
	if x != nil && x.VatNumber != nil {
		return *x.VatNumber
	}
	return ""
}

type UpdateBillingInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateBillingInfoResponse) Reset() {
	*x = UpdateBillingInfoResponse{}
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBillingInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBillingInfoResponse) ProtoMessage() {}

func (x *UpdateBillingInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBillingInfoResponse.ProtoReflect.Descriptor instead.
func (*UpdateBillingInfoResponse) Descriptor() ([]byte, []int) {
	return file_billing_v1_user_billing_infos_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateBillingInfoResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateBillingInfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteBillingInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteBillingInfoResponse) Reset() {
	*x = DeleteBillingInfoResponse{}
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteBillingInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBillingInfoResponse) ProtoMessage() {}

func (x *DeleteBillingInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBillingInfoResponse.ProtoReflect.Descriptor instead.
func (*DeleteBillingInfoResponse) Descriptor() ([]byte, []int) {
	return file_billing_v1_user_billing_infos_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteBillingInfoResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteBillingInfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteBillingInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteBillingInfoRequest) Reset() {
	*x = DeleteBillingInfoRequest{}
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteBillingInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBillingInfoRequest) ProtoMessage() {}

func (x *DeleteBillingInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_v1_user_billing_infos_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBillingInfoRequest.ProtoReflect.Descriptor instead.
func (*DeleteBillingInfoRequest) Descriptor() ([]byte, []int) {
	return file_billing_v1_user_billing_infos_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteBillingInfoRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_billing_v1_user_billing_infos_proto protoreflect.FileDescriptor

const file_billing_v1_user_billing_infos_proto_rawDesc = "" +
	"\n" +
	"#billing/v1/user_billing_infos.proto\x12\x0eapi.billing.v1\x1a\x14authz/v1/authz.proto\"\xa7\x03\n" +
	"\x15AddBillingInfoRequest\x12\x1b\n" +
	"\tfull_name\x18\x01 \x01(\tR\bfullName\x12\x1b\n" +
	"\taddress_1\x18\x02 \x01(\tR\baddress1\x12 \n" +
	"\taddress_2\x18\x03 \x01(\tH\x00R\baddress2\x88\x01\x01\x12\x12\n" +
	"\x04city\x18\x04 \x01(\tR\x04city\x12\x14\n" +
	"\x05state\x18\x05 \x01(\tR\x05state\x12\x18\n" +
	"\acountry\x18\x06 \x01(\tR\acountry\x12\x1f\n" +
	"\vpostal_code\x18\a \x01(\tR\n" +
	"postalCode\x12&\n" +
	"\fcompany_name\x18\b \x01(\tH\x01R\vcompanyName\x88\x01\x01\x12\"\n" +
	"\n" +
	"vat_number\x18\t \x01(\tH\x02R\tvatNumber\x88\x01\x01\x12\x17\n" +
	"\acard_no\x18\n" +
	" \x01(\tR\x06cardNo\x12\x1f\n" +
	"\vcard_expiry\x18\v \x01(\tR\n" +
	"cardExpiry\x12\x19\n" +
	"\bcard_cvc\x18\f \x01(\tR\acardCvcB\f\n" +
	"\n" +
	"_address_2B\x0f\n" +
	"\r_company_nameB\r\n" +
	"\v_vat_number\"I\n" +
	"\x16AddBillingInfoResponse\x12/\n" +
	"\x04data\x18\x01 \x01(\v2\x1b.api.billing.v1.BillingInfoR\x04data\"\xa1\x03\n" +
	"\vBillingInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1b\n" +
	"\tfull_name\x18\x02 \x01(\tR\bfullName\x12\x1b\n" +
	"\taddress_1\x18\x03 \x01(\tR\baddress1\x12 \n" +
	"\taddress_2\x18\x04 \x01(\tH\x00R\baddress2\x88\x01\x01\x12\x12\n" +
	"\x04city\x18\x05 \x01(\tR\x04city\x12\x19\n" +
	"\x05state\x18\x06 \x01(\tH\x01R\x05state\x88\x01\x01\x12\x18\n" +
	"\acountry\x18\a \x01(\tR\acountry\x12\x1f\n" +
	"\vpostal_code\x18\b \x01(\tR\n" +
	"postalCode\x12&\n" +
	"\fcompany_name\x18\t \x01(\tH\x02R\vcompanyName\x88\x01\x01\x12\"\n" +
	"\n" +
	"vat_number\x18\n" +
	" \x01(\tH\x03R\tvatNumber\x88\x01\x01\x12\x17\n" +
	"\acard_no\x18\v \x01(\tR\x06cardNo\x12\x1f\n" +
	"\vcard_expiry\x18\f \x01(\tR\n" +
	"cardExpiryB\f\n" +
	"\n" +
	"_address_2B\b\n" +
	"\x06_stateB\x0f\n" +
	"\r_company_nameB\r\n" +
	"\v_vat_number\"\x17\n" +
	"\x15GetBillingInfoRequest\"I\n" +
	"\x16GetBillingInfoResponse\x12/\n" +
	"\x04data\x18\x01 \x03(\v2\x1b.api.billing.v1.BillingInfoR\x04data\"\xe5\x02\n" +
	"\x18UpdateBillingInfoRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1b\n" +
	"\tfull_name\x18\x02 \x01(\tR\bfullName\x12\x1b\n" +
	"\taddress_1\x18\x03 \x01(\tR\baddress1\x12 \n" +
	"\taddress_2\x18\x04 \x01(\tH\x00R\baddress2\x88\x01\x01\x12\x12\n" +
	"\x04city\x18\x05 \x01(\tR\x04city\x12\x14\n" +
	"\x05state\x18\x06 \x01(\tR\x05state\x12\x18\n" +
	"\acountry\x18\a \x01(\tR\acountry\x12\x1f\n" +
	"\vpostal_code\x18\b \x01(\tR\n" +
	"postalCode\x12&\n" +
	"\fcompany_name\x18\t \x01(\tH\x01R\vcompanyName\x88\x01\x01\x12\"\n" +
	"\n" +
	"vat_number\x18\n" +
	" \x01(\tH\x02R\tvatNumber\x88\x01\x01B\f\n" +
	"\n" +
	"_address_2B\x0f\n" +
	"\r_company_nameB\r\n" +
	"\v_vat_number\"O\n" +
	"\x19UpdateBillingInfoResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"O\n" +
	"\x19DeleteBillingInfoResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"*\n" +
	"\x18DeleteBillingInfoRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id2\xc7\x03\n" +
	"\x0fBillInfoService\x12g\n" +
	"\x0eAddBillingInfo\x12%.api.billing.v1.AddBillingInfoRequest\x1a&.api.billing.v1.AddBillingInfoResponse\"\x06\x82\xb5\x18\x02\b\x01\x12g\n" +
	"\x0eGetBillingInfo\x12%.api.billing.v1.GetBillingInfoRequest\x1a&.api.billing.v1.GetBillingInfoResponse\"\x06\x82\xb5\x18\x02\b\x01\x12p\n" +
	"\x11UpdateBillingInfo\x12(.api.billing.v1.UpdateBillingInfoRequest\x1a).api.billing.v1.UpdateBillingInfoResponse\"\x06\x82\xb5\x18\x02\b\x01\x12p\n" +
	"\x11DeleteBillingInfo\x12(.api.billing.v1.DeleteBillingInfoRequest\x1a).api.billing.v1.DeleteBillingInfoResponse\"\x06\x82\xb5\x18\x02\b\x01B4Z2github.com/nsp-inc/vtuber/api/billing/v1;billingv1b\x06proto3"

var (
	file_billing_v1_user_billing_infos_proto_rawDescOnce sync.Once
	file_billing_v1_user_billing_infos_proto_rawDescData []byte
)

func file_billing_v1_user_billing_infos_proto_rawDescGZIP() []byte {
	file_billing_v1_user_billing_infos_proto_rawDescOnce.Do(func() {
		file_billing_v1_user_billing_infos_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_v1_user_billing_infos_proto_rawDesc), len(file_billing_v1_user_billing_infos_proto_rawDesc)))
	})
	return file_billing_v1_user_billing_infos_proto_rawDescData
}

var file_billing_v1_user_billing_infos_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_billing_v1_user_billing_infos_proto_goTypes = []any{
	(*AddBillingInfoRequest)(nil),     // 0: api.billing.v1.AddBillingInfoRequest
	(*AddBillingInfoResponse)(nil),    // 1: api.billing.v1.AddBillingInfoResponse
	(*BillingInfo)(nil),               // 2: api.billing.v1.BillingInfo
	(*GetBillingInfoRequest)(nil),     // 3: api.billing.v1.GetBillingInfoRequest
	(*GetBillingInfoResponse)(nil),    // 4: api.billing.v1.GetBillingInfoResponse
	(*UpdateBillingInfoRequest)(nil),  // 5: api.billing.v1.UpdateBillingInfoRequest
	(*UpdateBillingInfoResponse)(nil), // 6: api.billing.v1.UpdateBillingInfoResponse
	(*DeleteBillingInfoResponse)(nil), // 7: api.billing.v1.DeleteBillingInfoResponse
	(*DeleteBillingInfoRequest)(nil),  // 8: api.billing.v1.DeleteBillingInfoRequest
}
var file_billing_v1_user_billing_infos_proto_depIdxs = []int32{
	2, // 0: api.billing.v1.AddBillingInfoResponse.data:type_name -> api.billing.v1.BillingInfo
	2, // 1: api.billing.v1.GetBillingInfoResponse.data:type_name -> api.billing.v1.BillingInfo
	0, // 2: api.billing.v1.BillInfoService.AddBillingInfo:input_type -> api.billing.v1.AddBillingInfoRequest
	3, // 3: api.billing.v1.BillInfoService.GetBillingInfo:input_type -> api.billing.v1.GetBillingInfoRequest
	5, // 4: api.billing.v1.BillInfoService.UpdateBillingInfo:input_type -> api.billing.v1.UpdateBillingInfoRequest
	8, // 5: api.billing.v1.BillInfoService.DeleteBillingInfo:input_type -> api.billing.v1.DeleteBillingInfoRequest
	1, // 6: api.billing.v1.BillInfoService.AddBillingInfo:output_type -> api.billing.v1.AddBillingInfoResponse
	4, // 7: api.billing.v1.BillInfoService.GetBillingInfo:output_type -> api.billing.v1.GetBillingInfoResponse
	6, // 8: api.billing.v1.BillInfoService.UpdateBillingInfo:output_type -> api.billing.v1.UpdateBillingInfoResponse
	7, // 9: api.billing.v1.BillInfoService.DeleteBillingInfo:output_type -> api.billing.v1.DeleteBillingInfoResponse
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_billing_v1_user_billing_infos_proto_init() }
func file_billing_v1_user_billing_infos_proto_init() {
	if File_billing_v1_user_billing_infos_proto != nil {
		return
	}
	file_billing_v1_user_billing_infos_proto_msgTypes[0].OneofWrappers = []any{}
	file_billing_v1_user_billing_infos_proto_msgTypes[2].OneofWrappers = []any{}
	file_billing_v1_user_billing_infos_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_v1_user_billing_infos_proto_rawDesc), len(file_billing_v1_user_billing_infos_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_v1_user_billing_infos_proto_goTypes,
		DependencyIndexes: file_billing_v1_user_billing_infos_proto_depIdxs,
		MessageInfos:      file_billing_v1_user_billing_infos_proto_msgTypes,
	}.Build()
	File_billing_v1_user_billing_infos_proto = out.File
	file_billing_v1_user_billing_infos_proto_goTypes = nil
	file_billing_v1_user_billing_infos_proto_depIdxs = nil
}
