// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/v1/transactions.proto

package billingv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/billing/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// TransactionServiceName is the fully-qualified name of the TransactionService service.
	TransactionServiceName = "billing.v1.TransactionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// TransactionServiceAddTransactionProcedure is the fully-qualified name of the TransactionService's
	// AddTransaction RPC.
	TransactionServiceAddTransactionProcedure = "/billing.v1.TransactionService/AddTransaction"
	// TransactionServiceGetAllTransactionsProcedure is the fully-qualified name of the
	// TransactionService's GetAllTransactions RPC.
	TransactionServiceGetAllTransactionsProcedure = "/billing.v1.TransactionService/GetAllTransactions"
	// TransactionServiceGetAllTransactionsOfUserProcedure is the fully-qualified name of the
	// TransactionService's GetAllTransactionsOfUser RPC.
	TransactionServiceGetAllTransactionsOfUserProcedure = "/billing.v1.TransactionService/GetAllTransactionsOfUser"
	// TransactionServiceGetTransactionByIdProcedure is the fully-qualified name of the
	// TransactionService's GetTransactionById RPC.
	TransactionServiceGetTransactionByIdProcedure = "/billing.v1.TransactionService/GetTransactionById"
)

// TransactionServiceClient is a client for the billing.v1.TransactionService service.
type TransactionServiceClient interface {
	AddTransaction(context.Context, *connect.Request[v1.AddTransactionRequest]) (*connect.Response[v1.AddTransactionResponse], error)
	GetAllTransactions(context.Context, *connect.Request[v1.GetAllTransactionsRequest]) (*connect.Response[v1.GetAllTransactionsResponse], error)
	GetAllTransactionsOfUser(context.Context, *connect.Request[v1.GetAllTransactionsOfUserRequest]) (*connect.Response[v1.GetAllTransactionsResponse], error)
	GetTransactionById(context.Context, *connect.Request[v1.GetTransactionByIdRequest]) (*connect.Response[v1.GetTransactionByIdResponse], error)
}

// NewTransactionServiceClient constructs a client for the billing.v1.TransactionService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewTransactionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) TransactionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	transactionServiceMethods := v1.File_billing_v1_transactions_proto.Services().ByName("TransactionService").Methods()
	return &transactionServiceClient{
		addTransaction: connect.NewClient[v1.AddTransactionRequest, v1.AddTransactionResponse](
			httpClient,
			baseURL+TransactionServiceAddTransactionProcedure,
			connect.WithSchema(transactionServiceMethods.ByName("AddTransaction")),
			connect.WithClientOptions(opts...),
		),
		getAllTransactions: connect.NewClient[v1.GetAllTransactionsRequest, v1.GetAllTransactionsResponse](
			httpClient,
			baseURL+TransactionServiceGetAllTransactionsProcedure,
			connect.WithSchema(transactionServiceMethods.ByName("GetAllTransactions")),
			connect.WithClientOptions(opts...),
		),
		getAllTransactionsOfUser: connect.NewClient[v1.GetAllTransactionsOfUserRequest, v1.GetAllTransactionsResponse](
			httpClient,
			baseURL+TransactionServiceGetAllTransactionsOfUserProcedure,
			connect.WithSchema(transactionServiceMethods.ByName("GetAllTransactionsOfUser")),
			connect.WithClientOptions(opts...),
		),
		getTransactionById: connect.NewClient[v1.GetTransactionByIdRequest, v1.GetTransactionByIdResponse](
			httpClient,
			baseURL+TransactionServiceGetTransactionByIdProcedure,
			connect.WithSchema(transactionServiceMethods.ByName("GetTransactionById")),
			connect.WithClientOptions(opts...),
		),
	}
}

// transactionServiceClient implements TransactionServiceClient.
type transactionServiceClient struct {
	addTransaction           *connect.Client[v1.AddTransactionRequest, v1.AddTransactionResponse]
	getAllTransactions       *connect.Client[v1.GetAllTransactionsRequest, v1.GetAllTransactionsResponse]
	getAllTransactionsOfUser *connect.Client[v1.GetAllTransactionsOfUserRequest, v1.GetAllTransactionsResponse]
	getTransactionById       *connect.Client[v1.GetTransactionByIdRequest, v1.GetTransactionByIdResponse]
}

// AddTransaction calls billing.v1.TransactionService.AddTransaction.
func (c *transactionServiceClient) AddTransaction(ctx context.Context, req *connect.Request[v1.AddTransactionRequest]) (*connect.Response[v1.AddTransactionResponse], error) {
	return c.addTransaction.CallUnary(ctx, req)
}

// GetAllTransactions calls billing.v1.TransactionService.GetAllTransactions.
func (c *transactionServiceClient) GetAllTransactions(ctx context.Context, req *connect.Request[v1.GetAllTransactionsRequest]) (*connect.Response[v1.GetAllTransactionsResponse], error) {
	return c.getAllTransactions.CallUnary(ctx, req)
}

// GetAllTransactionsOfUser calls billing.v1.TransactionService.GetAllTransactionsOfUser.
func (c *transactionServiceClient) GetAllTransactionsOfUser(ctx context.Context, req *connect.Request[v1.GetAllTransactionsOfUserRequest]) (*connect.Response[v1.GetAllTransactionsResponse], error) {
	return c.getAllTransactionsOfUser.CallUnary(ctx, req)
}

// GetTransactionById calls billing.v1.TransactionService.GetTransactionById.
func (c *transactionServiceClient) GetTransactionById(ctx context.Context, req *connect.Request[v1.GetTransactionByIdRequest]) (*connect.Response[v1.GetTransactionByIdResponse], error) {
	return c.getTransactionById.CallUnary(ctx, req)
}

// TransactionServiceHandler is an implementation of the billing.v1.TransactionService service.
type TransactionServiceHandler interface {
	AddTransaction(context.Context, *connect.Request[v1.AddTransactionRequest]) (*connect.Response[v1.AddTransactionResponse], error)
	GetAllTransactions(context.Context, *connect.Request[v1.GetAllTransactionsRequest]) (*connect.Response[v1.GetAllTransactionsResponse], error)
	GetAllTransactionsOfUser(context.Context, *connect.Request[v1.GetAllTransactionsOfUserRequest]) (*connect.Response[v1.GetAllTransactionsResponse], error)
	GetTransactionById(context.Context, *connect.Request[v1.GetTransactionByIdRequest]) (*connect.Response[v1.GetTransactionByIdResponse], error)
}

// NewTransactionServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewTransactionServiceHandler(svc TransactionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	transactionServiceMethods := v1.File_billing_v1_transactions_proto.Services().ByName("TransactionService").Methods()
	transactionServiceAddTransactionHandler := connect.NewUnaryHandler(
		TransactionServiceAddTransactionProcedure,
		svc.AddTransaction,
		connect.WithSchema(transactionServiceMethods.ByName("AddTransaction")),
		connect.WithHandlerOptions(opts...),
	)
	transactionServiceGetAllTransactionsHandler := connect.NewUnaryHandler(
		TransactionServiceGetAllTransactionsProcedure,
		svc.GetAllTransactions,
		connect.WithSchema(transactionServiceMethods.ByName("GetAllTransactions")),
		connect.WithHandlerOptions(opts...),
	)
	transactionServiceGetAllTransactionsOfUserHandler := connect.NewUnaryHandler(
		TransactionServiceGetAllTransactionsOfUserProcedure,
		svc.GetAllTransactionsOfUser,
		connect.WithSchema(transactionServiceMethods.ByName("GetAllTransactionsOfUser")),
		connect.WithHandlerOptions(opts...),
	)
	transactionServiceGetTransactionByIdHandler := connect.NewUnaryHandler(
		TransactionServiceGetTransactionByIdProcedure,
		svc.GetTransactionById,
		connect.WithSchema(transactionServiceMethods.ByName("GetTransactionById")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.v1.TransactionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case TransactionServiceAddTransactionProcedure:
			transactionServiceAddTransactionHandler.ServeHTTP(w, r)
		case TransactionServiceGetAllTransactionsProcedure:
			transactionServiceGetAllTransactionsHandler.ServeHTTP(w, r)
		case TransactionServiceGetAllTransactionsOfUserProcedure:
			transactionServiceGetAllTransactionsOfUserHandler.ServeHTTP(w, r)
		case TransactionServiceGetTransactionByIdProcedure:
			transactionServiceGetTransactionByIdHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedTransactionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedTransactionServiceHandler struct{}

func (UnimplementedTransactionServiceHandler) AddTransaction(context.Context, *connect.Request[v1.AddTransactionRequest]) (*connect.Response[v1.AddTransactionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.v1.TransactionService.AddTransaction is not implemented"))
}

func (UnimplementedTransactionServiceHandler) GetAllTransactions(context.Context, *connect.Request[v1.GetAllTransactionsRequest]) (*connect.Response[v1.GetAllTransactionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.v1.TransactionService.GetAllTransactions is not implemented"))
}

func (UnimplementedTransactionServiceHandler) GetAllTransactionsOfUser(context.Context, *connect.Request[v1.GetAllTransactionsOfUserRequest]) (*connect.Response[v1.GetAllTransactionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.v1.TransactionService.GetAllTransactionsOfUser is not implemented"))
}

func (UnimplementedTransactionServiceHandler) GetTransactionById(context.Context, *connect.Request[v1.GetTransactionByIdRequest]) (*connect.Response[v1.GetTransactionByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.v1.TransactionService.GetTransactionById is not implemented"))
}
