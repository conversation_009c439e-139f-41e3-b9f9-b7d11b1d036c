// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/v1/user_billing_infos.proto

package billingv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/billing/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BillInfoServiceName is the fully-qualified name of the BillInfoService service.
	BillInfoServiceName = "api.billing.v1.BillInfoService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BillInfoServiceAddBillingInfoProcedure is the fully-qualified name of the BillInfoService's
	// AddBillingInfo RPC.
	BillInfoServiceAddBillingInfoProcedure = "/api.billing.v1.BillInfoService/AddBillingInfo"
	// BillInfoServiceGetBillingInfoProcedure is the fully-qualified name of the BillInfoService's
	// GetBillingInfo RPC.
	BillInfoServiceGetBillingInfoProcedure = "/api.billing.v1.BillInfoService/GetBillingInfo"
	// BillInfoServiceUpdateBillingInfoProcedure is the fully-qualified name of the BillInfoService's
	// UpdateBillingInfo RPC.
	BillInfoServiceUpdateBillingInfoProcedure = "/api.billing.v1.BillInfoService/UpdateBillingInfo"
	// BillInfoServiceDeleteBillingInfoProcedure is the fully-qualified name of the BillInfoService's
	// DeleteBillingInfo RPC.
	BillInfoServiceDeleteBillingInfoProcedure = "/api.billing.v1.BillInfoService/DeleteBillingInfo"
)

// BillInfoServiceClient is a client for the api.billing.v1.BillInfoService service.
type BillInfoServiceClient interface {
	AddBillingInfo(context.Context, *connect.Request[v1.AddBillingInfoRequest]) (*connect.Response[v1.AddBillingInfoResponse], error)
	GetBillingInfo(context.Context, *connect.Request[v1.GetBillingInfoRequest]) (*connect.Response[v1.GetBillingInfoResponse], error)
	UpdateBillingInfo(context.Context, *connect.Request[v1.UpdateBillingInfoRequest]) (*connect.Response[v1.UpdateBillingInfoResponse], error)
	DeleteBillingInfo(context.Context, *connect.Request[v1.DeleteBillingInfoRequest]) (*connect.Response[v1.DeleteBillingInfoResponse], error)
}

// NewBillInfoServiceClient constructs a client for the api.billing.v1.BillInfoService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBillInfoServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BillInfoServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	billInfoServiceMethods := v1.File_billing_v1_user_billing_infos_proto.Services().ByName("BillInfoService").Methods()
	return &billInfoServiceClient{
		addBillingInfo: connect.NewClient[v1.AddBillingInfoRequest, v1.AddBillingInfoResponse](
			httpClient,
			baseURL+BillInfoServiceAddBillingInfoProcedure,
			connect.WithSchema(billInfoServiceMethods.ByName("AddBillingInfo")),
			connect.WithClientOptions(opts...),
		),
		getBillingInfo: connect.NewClient[v1.GetBillingInfoRequest, v1.GetBillingInfoResponse](
			httpClient,
			baseURL+BillInfoServiceGetBillingInfoProcedure,
			connect.WithSchema(billInfoServiceMethods.ByName("GetBillingInfo")),
			connect.WithClientOptions(opts...),
		),
		updateBillingInfo: connect.NewClient[v1.UpdateBillingInfoRequest, v1.UpdateBillingInfoResponse](
			httpClient,
			baseURL+BillInfoServiceUpdateBillingInfoProcedure,
			connect.WithSchema(billInfoServiceMethods.ByName("UpdateBillingInfo")),
			connect.WithClientOptions(opts...),
		),
		deleteBillingInfo: connect.NewClient[v1.DeleteBillingInfoRequest, v1.DeleteBillingInfoResponse](
			httpClient,
			baseURL+BillInfoServiceDeleteBillingInfoProcedure,
			connect.WithSchema(billInfoServiceMethods.ByName("DeleteBillingInfo")),
			connect.WithClientOptions(opts...),
		),
	}
}

// billInfoServiceClient implements BillInfoServiceClient.
type billInfoServiceClient struct {
	addBillingInfo    *connect.Client[v1.AddBillingInfoRequest, v1.AddBillingInfoResponse]
	getBillingInfo    *connect.Client[v1.GetBillingInfoRequest, v1.GetBillingInfoResponse]
	updateBillingInfo *connect.Client[v1.UpdateBillingInfoRequest, v1.UpdateBillingInfoResponse]
	deleteBillingInfo *connect.Client[v1.DeleteBillingInfoRequest, v1.DeleteBillingInfoResponse]
}

// AddBillingInfo calls api.billing.v1.BillInfoService.AddBillingInfo.
func (c *billInfoServiceClient) AddBillingInfo(ctx context.Context, req *connect.Request[v1.AddBillingInfoRequest]) (*connect.Response[v1.AddBillingInfoResponse], error) {
	return c.addBillingInfo.CallUnary(ctx, req)
}

// GetBillingInfo calls api.billing.v1.BillInfoService.GetBillingInfo.
func (c *billInfoServiceClient) GetBillingInfo(ctx context.Context, req *connect.Request[v1.GetBillingInfoRequest]) (*connect.Response[v1.GetBillingInfoResponse], error) {
	return c.getBillingInfo.CallUnary(ctx, req)
}

// UpdateBillingInfo calls api.billing.v1.BillInfoService.UpdateBillingInfo.
func (c *billInfoServiceClient) UpdateBillingInfo(ctx context.Context, req *connect.Request[v1.UpdateBillingInfoRequest]) (*connect.Response[v1.UpdateBillingInfoResponse], error) {
	return c.updateBillingInfo.CallUnary(ctx, req)
}

// DeleteBillingInfo calls api.billing.v1.BillInfoService.DeleteBillingInfo.
func (c *billInfoServiceClient) DeleteBillingInfo(ctx context.Context, req *connect.Request[v1.DeleteBillingInfoRequest]) (*connect.Response[v1.DeleteBillingInfoResponse], error) {
	return c.deleteBillingInfo.CallUnary(ctx, req)
}

// BillInfoServiceHandler is an implementation of the api.billing.v1.BillInfoService service.
type BillInfoServiceHandler interface {
	AddBillingInfo(context.Context, *connect.Request[v1.AddBillingInfoRequest]) (*connect.Response[v1.AddBillingInfoResponse], error)
	GetBillingInfo(context.Context, *connect.Request[v1.GetBillingInfoRequest]) (*connect.Response[v1.GetBillingInfoResponse], error)
	UpdateBillingInfo(context.Context, *connect.Request[v1.UpdateBillingInfoRequest]) (*connect.Response[v1.UpdateBillingInfoResponse], error)
	DeleteBillingInfo(context.Context, *connect.Request[v1.DeleteBillingInfoRequest]) (*connect.Response[v1.DeleteBillingInfoResponse], error)
}

// NewBillInfoServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBillInfoServiceHandler(svc BillInfoServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	billInfoServiceMethods := v1.File_billing_v1_user_billing_infos_proto.Services().ByName("BillInfoService").Methods()
	billInfoServiceAddBillingInfoHandler := connect.NewUnaryHandler(
		BillInfoServiceAddBillingInfoProcedure,
		svc.AddBillingInfo,
		connect.WithSchema(billInfoServiceMethods.ByName("AddBillingInfo")),
		connect.WithHandlerOptions(opts...),
	)
	billInfoServiceGetBillingInfoHandler := connect.NewUnaryHandler(
		BillInfoServiceGetBillingInfoProcedure,
		svc.GetBillingInfo,
		connect.WithSchema(billInfoServiceMethods.ByName("GetBillingInfo")),
		connect.WithHandlerOptions(opts...),
	)
	billInfoServiceUpdateBillingInfoHandler := connect.NewUnaryHandler(
		BillInfoServiceUpdateBillingInfoProcedure,
		svc.UpdateBillingInfo,
		connect.WithSchema(billInfoServiceMethods.ByName("UpdateBillingInfo")),
		connect.WithHandlerOptions(opts...),
	)
	billInfoServiceDeleteBillingInfoHandler := connect.NewUnaryHandler(
		BillInfoServiceDeleteBillingInfoProcedure,
		svc.DeleteBillingInfo,
		connect.WithSchema(billInfoServiceMethods.ByName("DeleteBillingInfo")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.billing.v1.BillInfoService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BillInfoServiceAddBillingInfoProcedure:
			billInfoServiceAddBillingInfoHandler.ServeHTTP(w, r)
		case BillInfoServiceGetBillingInfoProcedure:
			billInfoServiceGetBillingInfoHandler.ServeHTTP(w, r)
		case BillInfoServiceUpdateBillingInfoProcedure:
			billInfoServiceUpdateBillingInfoHandler.ServeHTTP(w, r)
		case BillInfoServiceDeleteBillingInfoProcedure:
			billInfoServiceDeleteBillingInfoHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBillInfoServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBillInfoServiceHandler struct{}

func (UnimplementedBillInfoServiceHandler) AddBillingInfo(context.Context, *connect.Request[v1.AddBillingInfoRequest]) (*connect.Response[v1.AddBillingInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.billing.v1.BillInfoService.AddBillingInfo is not implemented"))
}

func (UnimplementedBillInfoServiceHandler) GetBillingInfo(context.Context, *connect.Request[v1.GetBillingInfoRequest]) (*connect.Response[v1.GetBillingInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.billing.v1.BillInfoService.GetBillingInfo is not implemented"))
}

func (UnimplementedBillInfoServiceHandler) UpdateBillingInfo(context.Context, *connect.Request[v1.UpdateBillingInfoRequest]) (*connect.Response[v1.UpdateBillingInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.billing.v1.BillInfoService.UpdateBillingInfo is not implemented"))
}

func (UnimplementedBillInfoServiceHandler) DeleteBillingInfo(context.Context, *connect.Request[v1.DeleteBillingInfoRequest]) (*connect.Response[v1.DeleteBillingInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.billing.v1.BillInfoService.DeleteBillingInfo is not implemented"))
}
