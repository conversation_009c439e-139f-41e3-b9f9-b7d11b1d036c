// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: users/v1/users.proto

package usersv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/users/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// UserServiceName is the fully-qualified name of the UserService service.
	UserServiceName = "api.users.v1.UserService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// UserServiceGetAllUsersProcedure is the fully-qualified name of the UserService's GetAllUsers RPC.
	UserServiceGetAllUsersProcedure = "/api.users.v1.UserService/GetAllUsers"
	// UserServiceGetAllDeletedUsersProcedure is the fully-qualified name of the UserService's
	// GetAllDeletedUsers RPC.
	UserServiceGetAllDeletedUsersProcedure = "/api.users.v1.UserService/GetAllDeletedUsers"
	// UserServiceGetUserByIdProcedure is the fully-qualified name of the UserService's GetUserById RPC.
	UserServiceGetUserByIdProcedure = "/api.users.v1.UserService/GetUserById"
	// UserServiceDeleteUserByIdProcedure is the fully-qualified name of the UserService's
	// DeleteUserById RPC.
	UserServiceDeleteUserByIdProcedure = "/api.users.v1.UserService/DeleteUserById"
	// UserServiceUpdateUserByIdProcedure is the fully-qualified name of the UserService's
	// UpdateUserById RPC.
	UserServiceUpdateUserByIdProcedure = "/api.users.v1.UserService/UpdateUserById"
	// UserServiceBanUserProcedure is the fully-qualified name of the UserService's BanUser RPC.
	UserServiceBanUserProcedure = "/api.users.v1.UserService/BanUser"
	// UserServiceGetAllBannedUsersProcedure is the fully-qualified name of the UserService's
	// GetAllBannedUsers RPC.
	UserServiceGetAllBannedUsersProcedure = "/api.users.v1.UserService/GetAllBannedUsers"
	// UserServiceGetUserPointProcedure is the fully-qualified name of the UserService's GetUserPoint
	// RPC.
	UserServiceGetUserPointProcedure = "/api.users.v1.UserService/GetUserPoint"
	// UserServiceGetUserCampaignInvestmentProcedure is the fully-qualified name of the UserService's
	// GetUserCampaignInvestment RPC.
	UserServiceGetUserCampaignInvestmentProcedure = "/api.users.v1.UserService/GetUserCampaignInvestment"
)

// UserServiceClient is a client for the api.users.v1.UserService service.
type UserServiceClient interface {
	// rpc AddUser(AddUserRequest) returns (AddUserResponse);
	GetAllUsers(context.Context, *connect.Request[v1.GetAllUsersRequest]) (*connect.Response[v1.GetAllUsersResponse], error)
	GetAllDeletedUsers(context.Context, *connect.Request[v1.GetAllDeletedUsersRequest]) (*connect.Response[v1.GetAllDeletedUsersResponse], error)
	GetUserById(context.Context, *connect.Request[v1.GetUserByIdRequest]) (*connect.Response[v1.GetUserByIdResponse], error)
	DeleteUserById(context.Context, *connect.Request[v1.DeleteUserByIdRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateUserById(context.Context, *connect.Request[v1.UpdateUserByIdRequest]) (*connect.Response[v1.UpdateUserResponse], error)
	BanUser(context.Context, *connect.Request[v1.BanUserRequest]) (*connect.Response[v11.GenericResponse], error)
	GetAllBannedUsers(context.Context, *connect.Request[v1.GetAllUsersRequest]) (*connect.Response[v1.GetAllUsersResponse], error)
	GetUserPoint(context.Context, *connect.Request[v1.GetUserPointRequest]) (*connect.Response[v1.GetUserResponse], error)
	GetUserCampaignInvestment(context.Context, *connect.Request[v1.GetUserCampaignInvestmentRequest]) (*connect.Response[v1.GetUserCampaignInvestmentResponse], error)
}

// NewUserServiceClient constructs a client for the api.users.v1.UserService service. By default, it
// uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewUserServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) UserServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	userServiceMethods := v1.File_users_v1_users_proto.Services().ByName("UserService").Methods()
	return &userServiceClient{
		getAllUsers: connect.NewClient[v1.GetAllUsersRequest, v1.GetAllUsersResponse](
			httpClient,
			baseURL+UserServiceGetAllUsersProcedure,
			connect.WithSchema(userServiceMethods.ByName("GetAllUsers")),
			connect.WithClientOptions(opts...),
		),
		getAllDeletedUsers: connect.NewClient[v1.GetAllDeletedUsersRequest, v1.GetAllDeletedUsersResponse](
			httpClient,
			baseURL+UserServiceGetAllDeletedUsersProcedure,
			connect.WithSchema(userServiceMethods.ByName("GetAllDeletedUsers")),
			connect.WithClientOptions(opts...),
		),
		getUserById: connect.NewClient[v1.GetUserByIdRequest, v1.GetUserByIdResponse](
			httpClient,
			baseURL+UserServiceGetUserByIdProcedure,
			connect.WithSchema(userServiceMethods.ByName("GetUserById")),
			connect.WithClientOptions(opts...),
		),
		deleteUserById: connect.NewClient[v1.DeleteUserByIdRequest, v11.GenericResponse](
			httpClient,
			baseURL+UserServiceDeleteUserByIdProcedure,
			connect.WithSchema(userServiceMethods.ByName("DeleteUserById")),
			connect.WithClientOptions(opts...),
		),
		updateUserById: connect.NewClient[v1.UpdateUserByIdRequest, v1.UpdateUserResponse](
			httpClient,
			baseURL+UserServiceUpdateUserByIdProcedure,
			connect.WithSchema(userServiceMethods.ByName("UpdateUserById")),
			connect.WithClientOptions(opts...),
		),
		banUser: connect.NewClient[v1.BanUserRequest, v11.GenericResponse](
			httpClient,
			baseURL+UserServiceBanUserProcedure,
			connect.WithSchema(userServiceMethods.ByName("BanUser")),
			connect.WithClientOptions(opts...),
		),
		getAllBannedUsers: connect.NewClient[v1.GetAllUsersRequest, v1.GetAllUsersResponse](
			httpClient,
			baseURL+UserServiceGetAllBannedUsersProcedure,
			connect.WithSchema(userServiceMethods.ByName("GetAllBannedUsers")),
			connect.WithClientOptions(opts...),
		),
		getUserPoint: connect.NewClient[v1.GetUserPointRequest, v1.GetUserResponse](
			httpClient,
			baseURL+UserServiceGetUserPointProcedure,
			connect.WithSchema(userServiceMethods.ByName("GetUserPoint")),
			connect.WithClientOptions(opts...),
		),
		getUserCampaignInvestment: connect.NewClient[v1.GetUserCampaignInvestmentRequest, v1.GetUserCampaignInvestmentResponse](
			httpClient,
			baseURL+UserServiceGetUserCampaignInvestmentProcedure,
			connect.WithSchema(userServiceMethods.ByName("GetUserCampaignInvestment")),
			connect.WithClientOptions(opts...),
		),
	}
}

// userServiceClient implements UserServiceClient.
type userServiceClient struct {
	getAllUsers               *connect.Client[v1.GetAllUsersRequest, v1.GetAllUsersResponse]
	getAllDeletedUsers        *connect.Client[v1.GetAllDeletedUsersRequest, v1.GetAllDeletedUsersResponse]
	getUserById               *connect.Client[v1.GetUserByIdRequest, v1.GetUserByIdResponse]
	deleteUserById            *connect.Client[v1.DeleteUserByIdRequest, v11.GenericResponse]
	updateUserById            *connect.Client[v1.UpdateUserByIdRequest, v1.UpdateUserResponse]
	banUser                   *connect.Client[v1.BanUserRequest, v11.GenericResponse]
	getAllBannedUsers         *connect.Client[v1.GetAllUsersRequest, v1.GetAllUsersResponse]
	getUserPoint              *connect.Client[v1.GetUserPointRequest, v1.GetUserResponse]
	getUserCampaignInvestment *connect.Client[v1.GetUserCampaignInvestmentRequest, v1.GetUserCampaignInvestmentResponse]
}

// GetAllUsers calls api.users.v1.UserService.GetAllUsers.
func (c *userServiceClient) GetAllUsers(ctx context.Context, req *connect.Request[v1.GetAllUsersRequest]) (*connect.Response[v1.GetAllUsersResponse], error) {
	return c.getAllUsers.CallUnary(ctx, req)
}

// GetAllDeletedUsers calls api.users.v1.UserService.GetAllDeletedUsers.
func (c *userServiceClient) GetAllDeletedUsers(ctx context.Context, req *connect.Request[v1.GetAllDeletedUsersRequest]) (*connect.Response[v1.GetAllDeletedUsersResponse], error) {
	return c.getAllDeletedUsers.CallUnary(ctx, req)
}

// GetUserById calls api.users.v1.UserService.GetUserById.
func (c *userServiceClient) GetUserById(ctx context.Context, req *connect.Request[v1.GetUserByIdRequest]) (*connect.Response[v1.GetUserByIdResponse], error) {
	return c.getUserById.CallUnary(ctx, req)
}

// DeleteUserById calls api.users.v1.UserService.DeleteUserById.
func (c *userServiceClient) DeleteUserById(ctx context.Context, req *connect.Request[v1.DeleteUserByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.deleteUserById.CallUnary(ctx, req)
}

// UpdateUserById calls api.users.v1.UserService.UpdateUserById.
func (c *userServiceClient) UpdateUserById(ctx context.Context, req *connect.Request[v1.UpdateUserByIdRequest]) (*connect.Response[v1.UpdateUserResponse], error) {
	return c.updateUserById.CallUnary(ctx, req)
}

// BanUser calls api.users.v1.UserService.BanUser.
func (c *userServiceClient) BanUser(ctx context.Context, req *connect.Request[v1.BanUserRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.banUser.CallUnary(ctx, req)
}

// GetAllBannedUsers calls api.users.v1.UserService.GetAllBannedUsers.
func (c *userServiceClient) GetAllBannedUsers(ctx context.Context, req *connect.Request[v1.GetAllUsersRequest]) (*connect.Response[v1.GetAllUsersResponse], error) {
	return c.getAllBannedUsers.CallUnary(ctx, req)
}

// GetUserPoint calls api.users.v1.UserService.GetUserPoint.
func (c *userServiceClient) GetUserPoint(ctx context.Context, req *connect.Request[v1.GetUserPointRequest]) (*connect.Response[v1.GetUserResponse], error) {
	return c.getUserPoint.CallUnary(ctx, req)
}

// GetUserCampaignInvestment calls api.users.v1.UserService.GetUserCampaignInvestment.
func (c *userServiceClient) GetUserCampaignInvestment(ctx context.Context, req *connect.Request[v1.GetUserCampaignInvestmentRequest]) (*connect.Response[v1.GetUserCampaignInvestmentResponse], error) {
	return c.getUserCampaignInvestment.CallUnary(ctx, req)
}

// UserServiceHandler is an implementation of the api.users.v1.UserService service.
type UserServiceHandler interface {
	// rpc AddUser(AddUserRequest) returns (AddUserResponse);
	GetAllUsers(context.Context, *connect.Request[v1.GetAllUsersRequest]) (*connect.Response[v1.GetAllUsersResponse], error)
	GetAllDeletedUsers(context.Context, *connect.Request[v1.GetAllDeletedUsersRequest]) (*connect.Response[v1.GetAllDeletedUsersResponse], error)
	GetUserById(context.Context, *connect.Request[v1.GetUserByIdRequest]) (*connect.Response[v1.GetUserByIdResponse], error)
	DeleteUserById(context.Context, *connect.Request[v1.DeleteUserByIdRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateUserById(context.Context, *connect.Request[v1.UpdateUserByIdRequest]) (*connect.Response[v1.UpdateUserResponse], error)
	BanUser(context.Context, *connect.Request[v1.BanUserRequest]) (*connect.Response[v11.GenericResponse], error)
	GetAllBannedUsers(context.Context, *connect.Request[v1.GetAllUsersRequest]) (*connect.Response[v1.GetAllUsersResponse], error)
	GetUserPoint(context.Context, *connect.Request[v1.GetUserPointRequest]) (*connect.Response[v1.GetUserResponse], error)
	GetUserCampaignInvestment(context.Context, *connect.Request[v1.GetUserCampaignInvestmentRequest]) (*connect.Response[v1.GetUserCampaignInvestmentResponse], error)
}

// NewUserServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewUserServiceHandler(svc UserServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	userServiceMethods := v1.File_users_v1_users_proto.Services().ByName("UserService").Methods()
	userServiceGetAllUsersHandler := connect.NewUnaryHandler(
		UserServiceGetAllUsersProcedure,
		svc.GetAllUsers,
		connect.WithSchema(userServiceMethods.ByName("GetAllUsers")),
		connect.WithHandlerOptions(opts...),
	)
	userServiceGetAllDeletedUsersHandler := connect.NewUnaryHandler(
		UserServiceGetAllDeletedUsersProcedure,
		svc.GetAllDeletedUsers,
		connect.WithSchema(userServiceMethods.ByName("GetAllDeletedUsers")),
		connect.WithHandlerOptions(opts...),
	)
	userServiceGetUserByIdHandler := connect.NewUnaryHandler(
		UserServiceGetUserByIdProcedure,
		svc.GetUserById,
		connect.WithSchema(userServiceMethods.ByName("GetUserById")),
		connect.WithHandlerOptions(opts...),
	)
	userServiceDeleteUserByIdHandler := connect.NewUnaryHandler(
		UserServiceDeleteUserByIdProcedure,
		svc.DeleteUserById,
		connect.WithSchema(userServiceMethods.ByName("DeleteUserById")),
		connect.WithHandlerOptions(opts...),
	)
	userServiceUpdateUserByIdHandler := connect.NewUnaryHandler(
		UserServiceUpdateUserByIdProcedure,
		svc.UpdateUserById,
		connect.WithSchema(userServiceMethods.ByName("UpdateUserById")),
		connect.WithHandlerOptions(opts...),
	)
	userServiceBanUserHandler := connect.NewUnaryHandler(
		UserServiceBanUserProcedure,
		svc.BanUser,
		connect.WithSchema(userServiceMethods.ByName("BanUser")),
		connect.WithHandlerOptions(opts...),
	)
	userServiceGetAllBannedUsersHandler := connect.NewUnaryHandler(
		UserServiceGetAllBannedUsersProcedure,
		svc.GetAllBannedUsers,
		connect.WithSchema(userServiceMethods.ByName("GetAllBannedUsers")),
		connect.WithHandlerOptions(opts...),
	)
	userServiceGetUserPointHandler := connect.NewUnaryHandler(
		UserServiceGetUserPointProcedure,
		svc.GetUserPoint,
		connect.WithSchema(userServiceMethods.ByName("GetUserPoint")),
		connect.WithHandlerOptions(opts...),
	)
	userServiceGetUserCampaignInvestmentHandler := connect.NewUnaryHandler(
		UserServiceGetUserCampaignInvestmentProcedure,
		svc.GetUserCampaignInvestment,
		connect.WithSchema(userServiceMethods.ByName("GetUserCampaignInvestment")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.users.v1.UserService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case UserServiceGetAllUsersProcedure:
			userServiceGetAllUsersHandler.ServeHTTP(w, r)
		case UserServiceGetAllDeletedUsersProcedure:
			userServiceGetAllDeletedUsersHandler.ServeHTTP(w, r)
		case UserServiceGetUserByIdProcedure:
			userServiceGetUserByIdHandler.ServeHTTP(w, r)
		case UserServiceDeleteUserByIdProcedure:
			userServiceDeleteUserByIdHandler.ServeHTTP(w, r)
		case UserServiceUpdateUserByIdProcedure:
			userServiceUpdateUserByIdHandler.ServeHTTP(w, r)
		case UserServiceBanUserProcedure:
			userServiceBanUserHandler.ServeHTTP(w, r)
		case UserServiceGetAllBannedUsersProcedure:
			userServiceGetAllBannedUsersHandler.ServeHTTP(w, r)
		case UserServiceGetUserPointProcedure:
			userServiceGetUserPointHandler.ServeHTTP(w, r)
		case UserServiceGetUserCampaignInvestmentProcedure:
			userServiceGetUserCampaignInvestmentHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedUserServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedUserServiceHandler struct{}

func (UnimplementedUserServiceHandler) GetAllUsers(context.Context, *connect.Request[v1.GetAllUsersRequest]) (*connect.Response[v1.GetAllUsersResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.UserService.GetAllUsers is not implemented"))
}

func (UnimplementedUserServiceHandler) GetAllDeletedUsers(context.Context, *connect.Request[v1.GetAllDeletedUsersRequest]) (*connect.Response[v1.GetAllDeletedUsersResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.UserService.GetAllDeletedUsers is not implemented"))
}

func (UnimplementedUserServiceHandler) GetUserById(context.Context, *connect.Request[v1.GetUserByIdRequest]) (*connect.Response[v1.GetUserByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.UserService.GetUserById is not implemented"))
}

func (UnimplementedUserServiceHandler) DeleteUserById(context.Context, *connect.Request[v1.DeleteUserByIdRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.UserService.DeleteUserById is not implemented"))
}

func (UnimplementedUserServiceHandler) UpdateUserById(context.Context, *connect.Request[v1.UpdateUserByIdRequest]) (*connect.Response[v1.UpdateUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.UserService.UpdateUserById is not implemented"))
}

func (UnimplementedUserServiceHandler) BanUser(context.Context, *connect.Request[v1.BanUserRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.UserService.BanUser is not implemented"))
}

func (UnimplementedUserServiceHandler) GetAllBannedUsers(context.Context, *connect.Request[v1.GetAllUsersRequest]) (*connect.Response[v1.GetAllUsersResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.UserService.GetAllBannedUsers is not implemented"))
}

func (UnimplementedUserServiceHandler) GetUserPoint(context.Context, *connect.Request[v1.GetUserPointRequest]) (*connect.Response[v1.GetUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.UserService.GetUserPoint is not implemented"))
}

func (UnimplementedUserServiceHandler) GetUserCampaignInvestment(context.Context, *connect.Request[v1.GetUserCampaignInvestmentRequest]) (*connect.Response[v1.GetUserCampaignInvestmentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.UserService.GetUserCampaignInvestment is not implemented"))
}
