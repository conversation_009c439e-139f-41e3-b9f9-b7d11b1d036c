// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: userdeliveryaddress/v1/userdeliveryaddress.proto

package userdeliveryaddressconnect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/userdeliveryaddress/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// UserDeliveryAddressServiceName is the fully-qualified name of the UserDeliveryAddressService
	// service.
	UserDeliveryAddressServiceName = "api.userdeliveryaddress.v1.UserDeliveryAddressService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// UserDeliveryAddressServiceAddUserDeliveryAddressProcedure is the fully-qualified name of the
	// UserDeliveryAddressService's AddUserDeliveryAddress RPC.
	UserDeliveryAddressServiceAddUserDeliveryAddressProcedure = "/api.userdeliveryaddress.v1.UserDeliveryAddressService/AddUserDeliveryAddress"
	// UserDeliveryAddressServiceGetCurrentUserDeliveryAddressProcedure is the fully-qualified name of
	// the UserDeliveryAddressService's GetCurrentUserDeliveryAddress RPC.
	UserDeliveryAddressServiceGetCurrentUserDeliveryAddressProcedure = "/api.userdeliveryaddress.v1.UserDeliveryAddressService/GetCurrentUserDeliveryAddress"
	// UserDeliveryAddressServiceGetUserDeliveryAddressByIdProcedure is the fully-qualified name of the
	// UserDeliveryAddressService's GetUserDeliveryAddressById RPC.
	UserDeliveryAddressServiceGetUserDeliveryAddressByIdProcedure = "/api.userdeliveryaddress.v1.UserDeliveryAddressService/GetUserDeliveryAddressById"
	// UserDeliveryAddressServiceDeleteUserDeliveryAddressProcedure is the fully-qualified name of the
	// UserDeliveryAddressService's DeleteUserDeliveryAddress RPC.
	UserDeliveryAddressServiceDeleteUserDeliveryAddressProcedure = "/api.userdeliveryaddress.v1.UserDeliveryAddressService/DeleteUserDeliveryAddress"
	// UserDeliveryAddressServiceUpdateUserDeliveryAddressProcedure is the fully-qualified name of the
	// UserDeliveryAddressService's UpdateUserDeliveryAddress RPC.
	UserDeliveryAddressServiceUpdateUserDeliveryAddressProcedure = "/api.userdeliveryaddress.v1.UserDeliveryAddressService/UpdateUserDeliveryAddress"
)

// UserDeliveryAddressServiceClient is a client for the
// api.userdeliveryaddress.v1.UserDeliveryAddressService service.
type UserDeliveryAddressServiceClient interface {
	AddUserDeliveryAddress(context.Context, *connect.Request[v1.AddUserDeliveryAddressRequest]) (*connect.Response[v1.AddUserDeliveryAddressResponse], error)
	GetCurrentUserDeliveryAddress(context.Context, *connect.Request[v1.GetCurrentUsersDeliveryAddressRequest]) (*connect.Response[v1.GetCurrentUsersDeliveryAddressResponse], error)
	GetUserDeliveryAddressById(context.Context, *connect.Request[v1.GetUserDeliveryAddressRequest]) (*connect.Response[v1.GetUserDeliveryAddressResponse], error)
	DeleteUserDeliveryAddress(context.Context, *connect.Request[v1.DeleteUserDeliveryAddressRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateUserDeliveryAddress(context.Context, *connect.Request[v1.UpdateUserDeliveryAddressRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewUserDeliveryAddressServiceClient constructs a client for the
// api.userdeliveryaddress.v1.UserDeliveryAddressService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewUserDeliveryAddressServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) UserDeliveryAddressServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	userDeliveryAddressServiceMethods := v1.File_userdeliveryaddress_v1_userdeliveryaddress_proto.Services().ByName("UserDeliveryAddressService").Methods()
	return &userDeliveryAddressServiceClient{
		addUserDeliveryAddress: connect.NewClient[v1.AddUserDeliveryAddressRequest, v1.AddUserDeliveryAddressResponse](
			httpClient,
			baseURL+UserDeliveryAddressServiceAddUserDeliveryAddressProcedure,
			connect.WithSchema(userDeliveryAddressServiceMethods.ByName("AddUserDeliveryAddress")),
			connect.WithClientOptions(opts...),
		),
		getCurrentUserDeliveryAddress: connect.NewClient[v1.GetCurrentUsersDeliveryAddressRequest, v1.GetCurrentUsersDeliveryAddressResponse](
			httpClient,
			baseURL+UserDeliveryAddressServiceGetCurrentUserDeliveryAddressProcedure,
			connect.WithSchema(userDeliveryAddressServiceMethods.ByName("GetCurrentUserDeliveryAddress")),
			connect.WithClientOptions(opts...),
		),
		getUserDeliveryAddressById: connect.NewClient[v1.GetUserDeliveryAddressRequest, v1.GetUserDeliveryAddressResponse](
			httpClient,
			baseURL+UserDeliveryAddressServiceGetUserDeliveryAddressByIdProcedure,
			connect.WithSchema(userDeliveryAddressServiceMethods.ByName("GetUserDeliveryAddressById")),
			connect.WithClientOptions(opts...),
		),
		deleteUserDeliveryAddress: connect.NewClient[v1.DeleteUserDeliveryAddressRequest, v11.GenericResponse](
			httpClient,
			baseURL+UserDeliveryAddressServiceDeleteUserDeliveryAddressProcedure,
			connect.WithSchema(userDeliveryAddressServiceMethods.ByName("DeleteUserDeliveryAddress")),
			connect.WithClientOptions(opts...),
		),
		updateUserDeliveryAddress: connect.NewClient[v1.UpdateUserDeliveryAddressRequest, v11.GenericResponse](
			httpClient,
			baseURL+UserDeliveryAddressServiceUpdateUserDeliveryAddressProcedure,
			connect.WithSchema(userDeliveryAddressServiceMethods.ByName("UpdateUserDeliveryAddress")),
			connect.WithClientOptions(opts...),
		),
	}
}

// userDeliveryAddressServiceClient implements UserDeliveryAddressServiceClient.
type userDeliveryAddressServiceClient struct {
	addUserDeliveryAddress        *connect.Client[v1.AddUserDeliveryAddressRequest, v1.AddUserDeliveryAddressResponse]
	getCurrentUserDeliveryAddress *connect.Client[v1.GetCurrentUsersDeliveryAddressRequest, v1.GetCurrentUsersDeliveryAddressResponse]
	getUserDeliveryAddressById    *connect.Client[v1.GetUserDeliveryAddressRequest, v1.GetUserDeliveryAddressResponse]
	deleteUserDeliveryAddress     *connect.Client[v1.DeleteUserDeliveryAddressRequest, v11.GenericResponse]
	updateUserDeliveryAddress     *connect.Client[v1.UpdateUserDeliveryAddressRequest, v11.GenericResponse]
}

// AddUserDeliveryAddress calls
// api.userdeliveryaddress.v1.UserDeliveryAddressService.AddUserDeliveryAddress.
func (c *userDeliveryAddressServiceClient) AddUserDeliveryAddress(ctx context.Context, req *connect.Request[v1.AddUserDeliveryAddressRequest]) (*connect.Response[v1.AddUserDeliveryAddressResponse], error) {
	return c.addUserDeliveryAddress.CallUnary(ctx, req)
}

// GetCurrentUserDeliveryAddress calls
// api.userdeliveryaddress.v1.UserDeliveryAddressService.GetCurrentUserDeliveryAddress.
func (c *userDeliveryAddressServiceClient) GetCurrentUserDeliveryAddress(ctx context.Context, req *connect.Request[v1.GetCurrentUsersDeliveryAddressRequest]) (*connect.Response[v1.GetCurrentUsersDeliveryAddressResponse], error) {
	return c.getCurrentUserDeliveryAddress.CallUnary(ctx, req)
}

// GetUserDeliveryAddressById calls
// api.userdeliveryaddress.v1.UserDeliveryAddressService.GetUserDeliveryAddressById.
func (c *userDeliveryAddressServiceClient) GetUserDeliveryAddressById(ctx context.Context, req *connect.Request[v1.GetUserDeliveryAddressRequest]) (*connect.Response[v1.GetUserDeliveryAddressResponse], error) {
	return c.getUserDeliveryAddressById.CallUnary(ctx, req)
}

// DeleteUserDeliveryAddress calls
// api.userdeliveryaddress.v1.UserDeliveryAddressService.DeleteUserDeliveryAddress.
func (c *userDeliveryAddressServiceClient) DeleteUserDeliveryAddress(ctx context.Context, req *connect.Request[v1.DeleteUserDeliveryAddressRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.deleteUserDeliveryAddress.CallUnary(ctx, req)
}

// UpdateUserDeliveryAddress calls
// api.userdeliveryaddress.v1.UserDeliveryAddressService.UpdateUserDeliveryAddress.
func (c *userDeliveryAddressServiceClient) UpdateUserDeliveryAddress(ctx context.Context, req *connect.Request[v1.UpdateUserDeliveryAddressRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.updateUserDeliveryAddress.CallUnary(ctx, req)
}

// UserDeliveryAddressServiceHandler is an implementation of the
// api.userdeliveryaddress.v1.UserDeliveryAddressService service.
type UserDeliveryAddressServiceHandler interface {
	AddUserDeliveryAddress(context.Context, *connect.Request[v1.AddUserDeliveryAddressRequest]) (*connect.Response[v1.AddUserDeliveryAddressResponse], error)
	GetCurrentUserDeliveryAddress(context.Context, *connect.Request[v1.GetCurrentUsersDeliveryAddressRequest]) (*connect.Response[v1.GetCurrentUsersDeliveryAddressResponse], error)
	GetUserDeliveryAddressById(context.Context, *connect.Request[v1.GetUserDeliveryAddressRequest]) (*connect.Response[v1.GetUserDeliveryAddressResponse], error)
	DeleteUserDeliveryAddress(context.Context, *connect.Request[v1.DeleteUserDeliveryAddressRequest]) (*connect.Response[v11.GenericResponse], error)
	UpdateUserDeliveryAddress(context.Context, *connect.Request[v1.UpdateUserDeliveryAddressRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewUserDeliveryAddressServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewUserDeliveryAddressServiceHandler(svc UserDeliveryAddressServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	userDeliveryAddressServiceMethods := v1.File_userdeliveryaddress_v1_userdeliveryaddress_proto.Services().ByName("UserDeliveryAddressService").Methods()
	userDeliveryAddressServiceAddUserDeliveryAddressHandler := connect.NewUnaryHandler(
		UserDeliveryAddressServiceAddUserDeliveryAddressProcedure,
		svc.AddUserDeliveryAddress,
		connect.WithSchema(userDeliveryAddressServiceMethods.ByName("AddUserDeliveryAddress")),
		connect.WithHandlerOptions(opts...),
	)
	userDeliveryAddressServiceGetCurrentUserDeliveryAddressHandler := connect.NewUnaryHandler(
		UserDeliveryAddressServiceGetCurrentUserDeliveryAddressProcedure,
		svc.GetCurrentUserDeliveryAddress,
		connect.WithSchema(userDeliveryAddressServiceMethods.ByName("GetCurrentUserDeliveryAddress")),
		connect.WithHandlerOptions(opts...),
	)
	userDeliveryAddressServiceGetUserDeliveryAddressByIdHandler := connect.NewUnaryHandler(
		UserDeliveryAddressServiceGetUserDeliveryAddressByIdProcedure,
		svc.GetUserDeliveryAddressById,
		connect.WithSchema(userDeliveryAddressServiceMethods.ByName("GetUserDeliveryAddressById")),
		connect.WithHandlerOptions(opts...),
	)
	userDeliveryAddressServiceDeleteUserDeliveryAddressHandler := connect.NewUnaryHandler(
		UserDeliveryAddressServiceDeleteUserDeliveryAddressProcedure,
		svc.DeleteUserDeliveryAddress,
		connect.WithSchema(userDeliveryAddressServiceMethods.ByName("DeleteUserDeliveryAddress")),
		connect.WithHandlerOptions(opts...),
	)
	userDeliveryAddressServiceUpdateUserDeliveryAddressHandler := connect.NewUnaryHandler(
		UserDeliveryAddressServiceUpdateUserDeliveryAddressProcedure,
		svc.UpdateUserDeliveryAddress,
		connect.WithSchema(userDeliveryAddressServiceMethods.ByName("UpdateUserDeliveryAddress")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.userdeliveryaddress.v1.UserDeliveryAddressService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case UserDeliveryAddressServiceAddUserDeliveryAddressProcedure:
			userDeliveryAddressServiceAddUserDeliveryAddressHandler.ServeHTTP(w, r)
		case UserDeliveryAddressServiceGetCurrentUserDeliveryAddressProcedure:
			userDeliveryAddressServiceGetCurrentUserDeliveryAddressHandler.ServeHTTP(w, r)
		case UserDeliveryAddressServiceGetUserDeliveryAddressByIdProcedure:
			userDeliveryAddressServiceGetUserDeliveryAddressByIdHandler.ServeHTTP(w, r)
		case UserDeliveryAddressServiceDeleteUserDeliveryAddressProcedure:
			userDeliveryAddressServiceDeleteUserDeliveryAddressHandler.ServeHTTP(w, r)
		case UserDeliveryAddressServiceUpdateUserDeliveryAddressProcedure:
			userDeliveryAddressServiceUpdateUserDeliveryAddressHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedUserDeliveryAddressServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedUserDeliveryAddressServiceHandler struct{}

func (UnimplementedUserDeliveryAddressServiceHandler) AddUserDeliveryAddress(context.Context, *connect.Request[v1.AddUserDeliveryAddressRequest]) (*connect.Response[v1.AddUserDeliveryAddressResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.userdeliveryaddress.v1.UserDeliveryAddressService.AddUserDeliveryAddress is not implemented"))
}

func (UnimplementedUserDeliveryAddressServiceHandler) GetCurrentUserDeliveryAddress(context.Context, *connect.Request[v1.GetCurrentUsersDeliveryAddressRequest]) (*connect.Response[v1.GetCurrentUsersDeliveryAddressResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.userdeliveryaddress.v1.UserDeliveryAddressService.GetCurrentUserDeliveryAddress is not implemented"))
}

func (UnimplementedUserDeliveryAddressServiceHandler) GetUserDeliveryAddressById(context.Context, *connect.Request[v1.GetUserDeliveryAddressRequest]) (*connect.Response[v1.GetUserDeliveryAddressResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.userdeliveryaddress.v1.UserDeliveryAddressService.GetUserDeliveryAddressById is not implemented"))
}

func (UnimplementedUserDeliveryAddressServiceHandler) DeleteUserDeliveryAddress(context.Context, *connect.Request[v1.DeleteUserDeliveryAddressRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.userdeliveryaddress.v1.UserDeliveryAddressService.DeleteUserDeliveryAddress is not implemented"))
}

func (UnimplementedUserDeliveryAddressServiceHandler) UpdateUserDeliveryAddress(context.Context, *connect.Request[v1.UpdateUserDeliveryAddressRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.userdeliveryaddress.v1.UserDeliveryAddressService.UpdateUserDeliveryAddress is not implemented"))
}
