// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: events/v1/events.proto

package eventsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddEventRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Title             string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty" validate:"required"`
	Description       string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Image             string                 `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty" validate:"required"`
	Rules             string                 `protobuf:"bytes,5,opt,name=rules,proto3" json:"rules,omitempty" validate:"required"`
	StartDate         *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate           *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Categories        []string               `protobuf:"bytes,8,rep,name=categories,proto3" json:"categories,omitempty"`
	AsVtuber          *bool                  `protobuf:"varint,9,opt,name=as_vtuber,json=asVtuber,proto3,oneof" json:"as_vtuber,omitempty"`
	ShortDescription  string                 `protobuf:"bytes,10,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" validate:"required"`
	ParticipationFlow string                 `protobuf:"bytes,11,opt,name=participation_flow,json=participationFlow,proto3" json:"participation_flow,omitempty" validate:"required"`
	Benefits          string                 `protobuf:"bytes,12,opt,name=benefits,proto3" json:"benefits,omitempty" validate:"required"`
	Requirements      string                 `protobuf:"bytes,13,opt,name=requirements,proto3" json:"requirements,omitempty" validate:"required"`
	Overview          string                 `protobuf:"bytes,14,opt,name=overview,proto3" json:"overview,omitempty" validate:"required"`
	SocialMediaLinks  *v1.SocialMediaLinks   `protobuf:"bytes,15,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AddEventRequest) Reset() {
	*x = AddEventRequest{}
	mi := &file_events_v1_events_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEventRequest) ProtoMessage() {}

func (x *AddEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEventRequest.ProtoReflect.Descriptor instead.
func (*AddEventRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{0}
}

func (x *AddEventRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AddEventRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddEventRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *AddEventRequest) GetRules() string {
	if x != nil {
		return x.Rules
	}
	return ""
}

func (x *AddEventRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *AddEventRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *AddEventRequest) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *AddEventRequest) GetAsVtuber() bool {
	if x != nil && x.AsVtuber != nil {
		return *x.AsVtuber
	}
	return false
}

func (x *AddEventRequest) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *AddEventRequest) GetParticipationFlow() string {
	if x != nil {
		return x.ParticipationFlow
	}
	return ""
}

func (x *AddEventRequest) GetBenefits() string {
	if x != nil {
		return x.Benefits
	}
	return ""
}

func (x *AddEventRequest) GetRequirements() string {
	if x != nil {
		return x.Requirements
	}
	return ""
}

func (x *AddEventRequest) GetOverview() string {
	if x != nil {
		return x.Overview
	}
	return ""
}

func (x *AddEventRequest) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

type AddEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Event                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddEventResponse) Reset() {
	*x = AddEventResponse{}
	mi := &file_events_v1_events_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEventResponse) ProtoMessage() {}

func (x *AddEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEventResponse.ProtoReflect.Descriptor instead.
func (*AddEventResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{1}
}

func (x *AddEventResponse) GetData() *Event {
	if x != nil {
		return x.Data
	}
	return nil
}

type Event struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title             string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description       string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Image             string                 `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`
	Rules             string                 `protobuf:"bytes,5,opt,name=rules,proto3" json:"rules,omitempty"`
	StartDate         *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate           *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Categories        []string               `protobuf:"bytes,8,rep,name=categories,proto3" json:"categories,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	User              *v1.Profile            `protobuf:"bytes,10,opt,name=user,proto3,oneof" json:"user,omitempty"`
	Vtuber            *v1.Profile            `protobuf:"bytes,11,opt,name=vtuber,proto3,oneof" json:"vtuber,omitempty"`
	Status            string                 `protobuf:"bytes,12,opt,name=status,proto3" json:"status,omitempty"`
	HasParticipated   bool                   `protobuf:"varint,13,opt,name=has_participated,json=hasParticipated,proto3" json:"has_participated,omitempty"`
	ShortDescription  string                 `protobuf:"bytes,14,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty"`
	ParticipationFlow string                 `protobuf:"bytes,15,opt,name=participation_flow,json=participationFlow,proto3" json:"participation_flow,omitempty"`
	Benefits          string                 `protobuf:"bytes,16,opt,name=benefits,proto3" json:"benefits,omitempty"`
	Requirements      string                 `protobuf:"bytes,17,opt,name=requirements,proto3" json:"requirements,omitempty"`
	Overview          string                 `protobuf:"bytes,18,opt,name=overview,proto3" json:"overview,omitempty"`
	SocialMediaLinks  *v1.SocialMediaLinks   `protobuf:"bytes,19,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	Slug              string                 `protobuf:"bytes,20,opt,name=slug,proto3" json:"slug,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Event) Reset() {
	*x = Event{}
	mi := &file_events_v1_events_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Event) ProtoMessage() {}

func (x *Event) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Event.ProtoReflect.Descriptor instead.
func (*Event) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{2}
}

func (x *Event) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Event) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Event) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Event) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Event) GetRules() string {
	if x != nil {
		return x.Rules
	}
	return ""
}

func (x *Event) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *Event) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *Event) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *Event) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Event) GetUser() *v1.Profile {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Event) GetVtuber() *v1.Profile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

func (x *Event) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Event) GetHasParticipated() bool {
	if x != nil {
		return x.HasParticipated
	}
	return false
}

func (x *Event) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *Event) GetParticipationFlow() string {
	if x != nil {
		return x.ParticipationFlow
	}
	return ""
}

func (x *Event) GetBenefits() string {
	if x != nil {
		return x.Benefits
	}
	return ""
}

func (x *Event) GetRequirements() string {
	if x != nil {
		return x.Requirements
	}
	return ""
}

func (x *Event) GetOverview() string {
	if x != nil {
		return x.Overview
	}
	return ""
}

func (x *Event) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

func (x *Event) GetSlug() string {
	if x != nil {
		return x.Slug
	}
	return ""
}

type GetAllEventsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	VtuberId      *string                `protobuf:"bytes,2,opt,name=vtuber_id,json=vtuberId,proto3,oneof" json:"vtuber_id,omitempty"`
	CategoryId    *string                `protobuf:"bytes,3,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllEventsRequest) Reset() {
	*x = GetAllEventsRequest{}
	mi := &file_events_v1_events_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllEventsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllEventsRequest) ProtoMessage() {}

func (x *GetAllEventsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllEventsRequest.ProtoReflect.Descriptor instead.
func (*GetAllEventsRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllEventsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAllEventsRequest) GetVtuberId() string {
	if x != nil && x.VtuberId != nil {
		return *x.VtuberId
	}
	return ""
}

func (x *GetAllEventsRequest) GetCategoryId() string {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return ""
}

type GetAllEventsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*Event               `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllEventsResponse) Reset() {
	*x = GetAllEventsResponse{}
	mi := &file_events_v1_events_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllEventsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllEventsResponse) ProtoMessage() {}

func (x *GetAllEventsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllEventsResponse.ProtoReflect.Descriptor instead.
func (*GetAllEventsResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllEventsResponse) GetData() []*Event {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllEventsResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetAllEventsByCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CategoryId    string                 `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllEventsByCategoryRequest) Reset() {
	*x = GetAllEventsByCategoryRequest{}
	mi := &file_events_v1_events_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllEventsByCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllEventsByCategoryRequest) ProtoMessage() {}

func (x *GetAllEventsByCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllEventsByCategoryRequest.ProtoReflect.Descriptor instead.
func (*GetAllEventsByCategoryRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{5}
}

func (x *GetAllEventsByCategoryRequest) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *GetAllEventsByCategoryRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetEventByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEventByIdRequest) Reset() {
	*x = GetEventByIdRequest{}
	mi := &file_events_v1_events_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEventByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEventByIdRequest) ProtoMessage() {}

func (x *GetEventByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEventByIdRequest.ProtoReflect.Descriptor instead.
func (*GetEventByIdRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{6}
}

func (x *GetEventByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetEventByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Event                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEventByIdResponse) Reset() {
	*x = GetEventByIdResponse{}
	mi := &file_events_v1_events_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEventByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEventByIdResponse) ProtoMessage() {}

func (x *GetEventByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEventByIdResponse.ProtoReflect.Descriptor instead.
func (*GetEventByIdResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{7}
}

func (x *GetEventByIdResponse) GetData() *Event {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteEventByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteEventByIdRequest) Reset() {
	*x = DeleteEventByIdRequest{}
	mi := &file_events_v1_events_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteEventByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEventByIdRequest) ProtoMessage() {}

func (x *DeleteEventByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEventByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteEventByIdRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteEventByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateEventByIdRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Title             string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty" validate:"required"`
	Description       string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Image             string                 `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	Rules             string                 `protobuf:"bytes,4,opt,name=rules,proto3" json:"rules,omitempty" validate:"required"`
	StartDate         *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate           *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Categories        []string               `protobuf:"bytes,7,rep,name=categories,proto3" json:"categories,omitempty"`
	Id                string                 `protobuf:"bytes,8,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	ShortDescription  string                 `protobuf:"bytes,9,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" validate:"required"`
	ParticipationFlow string                 `protobuf:"bytes,10,opt,name=participation_flow,json=participationFlow,proto3" json:"participation_flow,omitempty" validate:"required"`
	Benefits          string                 `protobuf:"bytes,11,opt,name=benefits,proto3" json:"benefits,omitempty" validate:"required"`
	Requirements      string                 `protobuf:"bytes,12,opt,name=requirements,proto3" json:"requirements,omitempty" validate:"required"`
	Overview          string                 `protobuf:"bytes,13,opt,name=overview,proto3" json:"overview,omitempty" validate:"required"`
	SocialMediaLinks  *v1.SocialMediaLinks   `protobuf:"bytes,14,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateEventByIdRequest) Reset() {
	*x = UpdateEventByIdRequest{}
	mi := &file_events_v1_events_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEventByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEventByIdRequest) ProtoMessage() {}

func (x *UpdateEventByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEventByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateEventByIdRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateEventByIdRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetRules() string {
	if x != nil {
		return x.Rules
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *UpdateEventByIdRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *UpdateEventByIdRequest) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *UpdateEventByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetParticipationFlow() string {
	if x != nil {
		return x.ParticipationFlow
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetBenefits() string {
	if x != nil {
		return x.Benefits
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetRequirements() string {
	if x != nil {
		return x.Requirements
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetOverview() string {
	if x != nil {
		return x.Overview
	}
	return ""
}

func (x *UpdateEventByIdRequest) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

type ApproveOrRejectEventRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty" validate:"omitempty,oneof=approved rejected"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApproveOrRejectEventRequest) Reset() {
	*x = ApproveOrRejectEventRequest{}
	mi := &file_events_v1_events_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveOrRejectEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveOrRejectEventRequest) ProtoMessage() {}

func (x *ApproveOrRejectEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveOrRejectEventRequest.ProtoReflect.Descriptor instead.
func (*ApproveOrRejectEventRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{10}
}

func (x *ApproveOrRejectEventRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ApproveOrRejectEventRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type DeleteEventByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteEventByIdResponse) Reset() {
	*x = DeleteEventByIdResponse{}
	mi := &file_events_v1_events_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteEventByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEventByIdResponse) ProtoMessage() {}

func (x *DeleteEventByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEventByIdResponse.ProtoReflect.Descriptor instead.
func (*DeleteEventByIdResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteEventByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteEventByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateEventByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateEventByIdResponse) Reset() {
	*x = UpdateEventByIdResponse{}
	mi := &file_events_v1_events_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEventByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEventByIdResponse) ProtoMessage() {}

func (x *UpdateEventByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEventByIdResponse.ProtoReflect.Descriptor instead.
func (*UpdateEventByIdResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateEventByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateEventByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ApproveOrRejectEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApproveOrRejectEventResponse) Reset() {
	*x = ApproveOrRejectEventResponse{}
	mi := &file_events_v1_events_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveOrRejectEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveOrRejectEventResponse) ProtoMessage() {}

func (x *ApproveOrRejectEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_events_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveOrRejectEventResponse.ProtoReflect.Descriptor instead.
func (*ApproveOrRejectEventResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_events_proto_rawDescGZIP(), []int{13}
}

func (x *ApproveOrRejectEventResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ApproveOrRejectEventResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_events_v1_events_proto protoreflect.FileDescriptor

const file_events_v1_events_proto_rawDesc = "" +
	"\n" +
	"\x16events/v1/events.proto\x12\rapi.events.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\xbe\x04\n" +
	"\x0fAddEventRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x14\n" +
	"\x05image\x18\x03 \x01(\tR\x05image\x12\x14\n" +
	"\x05rules\x18\x05 \x01(\tR\x05rules\x129\n" +
	"\n" +
	"start_date\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12\x1e\n" +
	"\n" +
	"categories\x18\b \x03(\tR\n" +
	"categories\x12 \n" +
	"\tas_vtuber\x18\t \x01(\bH\x00R\basVtuber\x88\x01\x01\x12+\n" +
	"\x11short_description\x18\n" +
	" \x01(\tR\x10shortDescription\x12-\n" +
	"\x12participation_flow\x18\v \x01(\tR\x11participationFlow\x12\x1a\n" +
	"\bbenefits\x18\f \x01(\tR\bbenefits\x12\"\n" +
	"\frequirements\x18\r \x01(\tR\frequirements\x12\x1a\n" +
	"\boverview\x18\x0e \x01(\tR\boverview\x12M\n" +
	"\x12social_media_links\x18\x0f \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinksB\f\n" +
	"\n" +
	"_as_vtuber\"<\n" +
	"\x10AddEventResponse\x12(\n" +
	"\x04data\x18\x01 \x01(\v2\x14.api.events.v1.EventR\x04data\"\xa0\x06\n" +
	"\x05Event\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x14\n" +
	"\x05image\x18\x04 \x01(\tR\x05image\x12\x14\n" +
	"\x05rules\x18\x05 \x01(\tR\x05rules\x129\n" +
	"\n" +
	"start_date\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12\x1e\n" +
	"\n" +
	"categories\x18\b \x03(\tR\n" +
	"categories\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12/\n" +
	"\x04user\x18\n" +
	" \x01(\v2\x16.api.shared.v1.ProfileH\x00R\x04user\x88\x01\x01\x123\n" +
	"\x06vtuber\x18\v \x01(\v2\x16.api.shared.v1.ProfileH\x01R\x06vtuber\x88\x01\x01\x12\x16\n" +
	"\x06status\x18\f \x01(\tR\x06status\x12)\n" +
	"\x10has_participated\x18\r \x01(\bR\x0fhasParticipated\x12+\n" +
	"\x11short_description\x18\x0e \x01(\tR\x10shortDescription\x12-\n" +
	"\x12participation_flow\x18\x0f \x01(\tR\x11participationFlow\x12\x1a\n" +
	"\bbenefits\x18\x10 \x01(\tR\bbenefits\x12\"\n" +
	"\frequirements\x18\x11 \x01(\tR\frequirements\x12\x1a\n" +
	"\boverview\x18\x12 \x01(\tR\boverview\x12M\n" +
	"\x12social_media_links\x18\x13 \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\x12\x12\n" +
	"\x04slug\x18\x14 \x01(\tR\x04slugB\a\n" +
	"\x05_userB\t\n" +
	"\a_vtuber\"\xd1\x01\n" +
	"\x13GetAllEventsRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01\x12 \n" +
	"\tvtuber_id\x18\x02 \x01(\tH\x01R\bvtuberId\x88\x01\x01\x12$\n" +
	"\vcategory_id\x18\x03 \x01(\tH\x02R\n" +
	"categoryId\x88\x01\x01B\r\n" +
	"\v_paginationB\f\n" +
	"\n" +
	"_vtuber_idB\x0e\n" +
	"\f_category_id\"\x91\x01\n" +
	"\x14GetAllEventsResponse\x12(\n" +
	"\x04data\x18\x01 \x03(\v2\x14.api.events.v1.EventR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\x96\x01\n" +
	"\x1dGetAllEventsByCategoryRequest\x12\x1f\n" +
	"\vcategory_id\x18\x01 \x01(\tR\n" +
	"categoryId\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"%\n" +
	"\x13GetEventByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"@\n" +
	"\x14GetEventByIdResponse\x12(\n" +
	"\x04data\x18\x01 \x01(\v2\x14.api.events.v1.EventR\x04data\"(\n" +
	"\x16DeleteEventByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\xa5\x04\n" +
	"\x16UpdateEventByIdRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x14\n" +
	"\x05image\x18\x03 \x01(\tR\x05image\x12\x14\n" +
	"\x05rules\x18\x04 \x01(\tR\x05rules\x129\n" +
	"\n" +
	"start_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12\x1e\n" +
	"\n" +
	"categories\x18\a \x03(\tR\n" +
	"categories\x12\x0e\n" +
	"\x02id\x18\b \x01(\tR\x02id\x12+\n" +
	"\x11short_description\x18\t \x01(\tR\x10shortDescription\x12-\n" +
	"\x12participation_flow\x18\n" +
	" \x01(\tR\x11participationFlow\x12\x1a\n" +
	"\bbenefits\x18\v \x01(\tR\bbenefits\x12\"\n" +
	"\frequirements\x18\f \x01(\tR\frequirements\x12\x1a\n" +
	"\boverview\x18\r \x01(\tR\boverview\x12M\n" +
	"\x12social_media_links\x18\x0e \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\"E\n" +
	"\x1bApproveOrRejectEventRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\"M\n" +
	"\x17DeleteEventByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"M\n" +
	"\x17UpdateEventByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"R\n" +
	"\x1cApproveOrRejectEventResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xad\a\n" +
	"\fEventService\x12S\n" +
	"\bAddEvent\x12\x1e.api.events.v1.AddEventRequest\x1a\x1f.api.events.v1.AddEventResponse\"\x06\x82\xb5\x18\x02\b\x01\x12W\n" +
	"\fGetAllEvents\x12\".api.events.v1.GetAllEventsRequest\x1a#.api.events.v1.GetAllEventsResponse\x12W\n" +
	"\fGetEventById\x12\".api.events.v1.GetEventByIdRequest\x1a#.api.events.v1.GetEventByIdResponse\x12j\n" +
	"\x0fDeleteEventById\x12%.api.events.v1.DeleteEventByIdRequest\x1a&.api.events.v1.DeleteEventByIdResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12h\n" +
	"\x0fUpdateEventById\x12%.api.events.v1.UpdateEventByIdRequest\x1a&.api.events.v1.UpdateEventByIdResponse\"\x06\x82\xb5\x18\x02\b\x01\x12k\n" +
	"\x16GetAllEventsByCategory\x12,.api.events.v1.GetAllEventsByCategoryRequest\x1a#.api.events.v1.GetAllEventsResponse\x12y\n" +
	"\x14ApproveOrRejectEvent\x12*.api.events.v1.ApproveOrRejectEventRequest\x1a+.api.events.v1.ApproveOrRejectEventResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12v\n" +
	"\vGetMyEvents\x12\".api.events.v1.GetAllEventsRequest\x1a#.api.events.v1.GetAllEventsResponse\"\x1e\x82\xb5\x18\x1a\b\x01\x18\x01\"\x14_user.vtuberId!=null\x12`\n" +
	"\rGetUserEvents\x12\".api.events.v1.GetAllEventsRequest\x1a#.api.events.v1.GetAllEventsResponse\"\x06\x82\xb5\x18\x02\b\x01B2Z0github.com/nsp-inc/vtuber/api/events/v1;eventsv1b\x06proto3"

var (
	file_events_v1_events_proto_rawDescOnce sync.Once
	file_events_v1_events_proto_rawDescData []byte
)

func file_events_v1_events_proto_rawDescGZIP() []byte {
	file_events_v1_events_proto_rawDescOnce.Do(func() {
		file_events_v1_events_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_events_v1_events_proto_rawDesc), len(file_events_v1_events_proto_rawDesc)))
	})
	return file_events_v1_events_proto_rawDescData
}

var file_events_v1_events_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_events_v1_events_proto_goTypes = []any{
	(*AddEventRequest)(nil),               // 0: api.events.v1.AddEventRequest
	(*AddEventResponse)(nil),              // 1: api.events.v1.AddEventResponse
	(*Event)(nil),                         // 2: api.events.v1.Event
	(*GetAllEventsRequest)(nil),           // 3: api.events.v1.GetAllEventsRequest
	(*GetAllEventsResponse)(nil),          // 4: api.events.v1.GetAllEventsResponse
	(*GetAllEventsByCategoryRequest)(nil), // 5: api.events.v1.GetAllEventsByCategoryRequest
	(*GetEventByIdRequest)(nil),           // 6: api.events.v1.GetEventByIdRequest
	(*GetEventByIdResponse)(nil),          // 7: api.events.v1.GetEventByIdResponse
	(*DeleteEventByIdRequest)(nil),        // 8: api.events.v1.DeleteEventByIdRequest
	(*UpdateEventByIdRequest)(nil),        // 9: api.events.v1.UpdateEventByIdRequest
	(*ApproveOrRejectEventRequest)(nil),   // 10: api.events.v1.ApproveOrRejectEventRequest
	(*DeleteEventByIdResponse)(nil),       // 11: api.events.v1.DeleteEventByIdResponse
	(*UpdateEventByIdResponse)(nil),       // 12: api.events.v1.UpdateEventByIdResponse
	(*ApproveOrRejectEventResponse)(nil),  // 13: api.events.v1.ApproveOrRejectEventResponse
	(*timestamppb.Timestamp)(nil),         // 14: google.protobuf.Timestamp
	(*v1.SocialMediaLinks)(nil),           // 15: api.shared.v1.SocialMediaLinks
	(*v1.Profile)(nil),                    // 16: api.shared.v1.Profile
	(*v1.PaginationRequest)(nil),          // 17: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),          // 18: api.shared.v1.PaginationDetails
}
var file_events_v1_events_proto_depIdxs = []int32{
	14, // 0: api.events.v1.AddEventRequest.start_date:type_name -> google.protobuf.Timestamp
	14, // 1: api.events.v1.AddEventRequest.end_date:type_name -> google.protobuf.Timestamp
	15, // 2: api.events.v1.AddEventRequest.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	2,  // 3: api.events.v1.AddEventResponse.data:type_name -> api.events.v1.Event
	14, // 4: api.events.v1.Event.start_date:type_name -> google.protobuf.Timestamp
	14, // 5: api.events.v1.Event.end_date:type_name -> google.protobuf.Timestamp
	14, // 6: api.events.v1.Event.created_at:type_name -> google.protobuf.Timestamp
	16, // 7: api.events.v1.Event.user:type_name -> api.shared.v1.Profile
	16, // 8: api.events.v1.Event.vtuber:type_name -> api.shared.v1.Profile
	15, // 9: api.events.v1.Event.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	17, // 10: api.events.v1.GetAllEventsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 11: api.events.v1.GetAllEventsResponse.data:type_name -> api.events.v1.Event
	18, // 12: api.events.v1.GetAllEventsResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	17, // 13: api.events.v1.GetAllEventsByCategoryRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 14: api.events.v1.GetEventByIdResponse.data:type_name -> api.events.v1.Event
	14, // 15: api.events.v1.UpdateEventByIdRequest.start_date:type_name -> google.protobuf.Timestamp
	14, // 16: api.events.v1.UpdateEventByIdRequest.end_date:type_name -> google.protobuf.Timestamp
	15, // 17: api.events.v1.UpdateEventByIdRequest.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	0,  // 18: api.events.v1.EventService.AddEvent:input_type -> api.events.v1.AddEventRequest
	3,  // 19: api.events.v1.EventService.GetAllEvents:input_type -> api.events.v1.GetAllEventsRequest
	6,  // 20: api.events.v1.EventService.GetEventById:input_type -> api.events.v1.GetEventByIdRequest
	8,  // 21: api.events.v1.EventService.DeleteEventById:input_type -> api.events.v1.DeleteEventByIdRequest
	9,  // 22: api.events.v1.EventService.UpdateEventById:input_type -> api.events.v1.UpdateEventByIdRequest
	5,  // 23: api.events.v1.EventService.GetAllEventsByCategory:input_type -> api.events.v1.GetAllEventsByCategoryRequest
	10, // 24: api.events.v1.EventService.ApproveOrRejectEvent:input_type -> api.events.v1.ApproveOrRejectEventRequest
	3,  // 25: api.events.v1.EventService.GetMyEvents:input_type -> api.events.v1.GetAllEventsRequest
	3,  // 26: api.events.v1.EventService.GetUserEvents:input_type -> api.events.v1.GetAllEventsRequest
	1,  // 27: api.events.v1.EventService.AddEvent:output_type -> api.events.v1.AddEventResponse
	4,  // 28: api.events.v1.EventService.GetAllEvents:output_type -> api.events.v1.GetAllEventsResponse
	7,  // 29: api.events.v1.EventService.GetEventById:output_type -> api.events.v1.GetEventByIdResponse
	11, // 30: api.events.v1.EventService.DeleteEventById:output_type -> api.events.v1.DeleteEventByIdResponse
	12, // 31: api.events.v1.EventService.UpdateEventById:output_type -> api.events.v1.UpdateEventByIdResponse
	4,  // 32: api.events.v1.EventService.GetAllEventsByCategory:output_type -> api.events.v1.GetAllEventsResponse
	13, // 33: api.events.v1.EventService.ApproveOrRejectEvent:output_type -> api.events.v1.ApproveOrRejectEventResponse
	4,  // 34: api.events.v1.EventService.GetMyEvents:output_type -> api.events.v1.GetAllEventsResponse
	4,  // 35: api.events.v1.EventService.GetUserEvents:output_type -> api.events.v1.GetAllEventsResponse
	27, // [27:36] is the sub-list for method output_type
	18, // [18:27] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_events_v1_events_proto_init() }
func file_events_v1_events_proto_init() {
	if File_events_v1_events_proto != nil {
		return
	}
	file_events_v1_events_proto_msgTypes[0].OneofWrappers = []any{}
	file_events_v1_events_proto_msgTypes[2].OneofWrappers = []any{}
	file_events_v1_events_proto_msgTypes[3].OneofWrappers = []any{}
	file_events_v1_events_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_events_v1_events_proto_rawDesc), len(file_events_v1_events_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_events_v1_events_proto_goTypes,
		DependencyIndexes: file_events_v1_events_proto_depIdxs,
		MessageInfos:      file_events_v1_events_proto_msgTypes,
	}.Build()
	File_events_v1_events_proto = out.File
	file_events_v1_events_proto_goTypes = nil
	file_events_v1_events_proto_depIdxs = nil
}
