// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: events/v1/eventvote.proto

package eventsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	_ "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddVoteRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	EventParticipationId string                 `protobuf:"bytes,1,opt,name=event_participation_id,json=eventParticipationId,proto3" json:"event_participation_id,omitempty" validate:"required"`
	FromDailyPoint       bool                   `protobuf:"varint,2,opt,name=from_daily_point,json=fromDailyPoint,proto3" json:"from_daily_point,omitempty"`
	Points               int32                  `protobuf:"varint,3,opt,name=points,proto3" json:"points,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *AddVoteRequest) Reset() {
	*x = AddVoteRequest{}
	mi := &file_events_v1_eventvote_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVoteRequest) ProtoMessage() {}

func (x *AddVoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventvote_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVoteRequest.ProtoReflect.Descriptor instead.
func (*AddVoteRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventvote_proto_rawDescGZIP(), []int{0}
}

func (x *AddVoteRequest) GetEventParticipationId() string {
	if x != nil {
		return x.EventParticipationId
	}
	return ""
}

func (x *AddVoteRequest) GetFromDailyPoint() bool {
	if x != nil {
		return x.FromDailyPoint
	}
	return false
}

func (x *AddVoteRequest) GetPoints() int32 {
	if x != nil {
		return x.Points
	}
	return 0
}

type GetDailyPointAvailableRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EventId       string                 `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDailyPointAvailableRequest) Reset() {
	*x = GetDailyPointAvailableRequest{}
	mi := &file_events_v1_eventvote_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDailyPointAvailableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyPointAvailableRequest) ProtoMessage() {}

func (x *GetDailyPointAvailableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventvote_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyPointAvailableRequest.ProtoReflect.Descriptor instead.
func (*GetDailyPointAvailableRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventvote_proto_rawDescGZIP(), []int{1}
}

func (x *GetDailyPointAvailableRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

type GetDailyPointAvailableResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Available     bool                   `protobuf:"varint,1,opt,name=available,proto3" json:"available,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDailyPointAvailableResponse) Reset() {
	*x = GetDailyPointAvailableResponse{}
	mi := &file_events_v1_eventvote_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDailyPointAvailableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyPointAvailableResponse) ProtoMessage() {}

func (x *GetDailyPointAvailableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventvote_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyPointAvailableResponse.ProtoReflect.Descriptor instead.
func (*GetDailyPointAvailableResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventvote_proto_rawDescGZIP(), []int{2}
}

func (x *GetDailyPointAvailableResponse) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

type GivePlatformPointRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	EventParticipationId string                 `protobuf:"bytes,1,opt,name=event_participation_id,json=eventParticipationId,proto3" json:"event_participation_id,omitempty" validate:"required"`
	Remarks              string                 `protobuf:"bytes,2,opt,name=remarks,proto3" json:"remarks,omitempty" validate:"required"`
	Points               int64                  `protobuf:"varint,3,opt,name=points,proto3" json:"points,omitempty" validate:"required"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GivePlatformPointRequest) Reset() {
	*x = GivePlatformPointRequest{}
	mi := &file_events_v1_eventvote_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GivePlatformPointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GivePlatformPointRequest) ProtoMessage() {}

func (x *GivePlatformPointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventvote_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GivePlatformPointRequest.ProtoReflect.Descriptor instead.
func (*GivePlatformPointRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventvote_proto_rawDescGZIP(), []int{3}
}

func (x *GivePlatformPointRequest) GetEventParticipationId() string {
	if x != nil {
		return x.EventParticipationId
	}
	return ""
}

func (x *GivePlatformPointRequest) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *GivePlatformPointRequest) GetPoints() int64 {
	if x != nil {
		return x.Points
	}
	return 0
}

type GetPlatfromPoint struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	EventParticipationId string                 `protobuf:"bytes,1,opt,name=event_participation_id,json=eventParticipationId,proto3" json:"event_participation_id,omitempty" validate:"required"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GetPlatfromPoint) Reset() {
	*x = GetPlatfromPoint{}
	mi := &file_events_v1_eventvote_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlatfromPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlatfromPoint) ProtoMessage() {}

func (x *GetPlatfromPoint) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventvote_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlatfromPoint.ProtoReflect.Descriptor instead.
func (*GetPlatfromPoint) Descriptor() ([]byte, []int) {
	return file_events_v1_eventvote_proto_rawDescGZIP(), []int{4}
}

func (x *GetPlatfromPoint) GetEventParticipationId() string {
	if x != nil {
		return x.EventParticipationId
	}
	return ""
}

type AddVoteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVoteResponse) Reset() {
	*x = AddVoteResponse{}
	mi := &file_events_v1_eventvote_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVoteResponse) ProtoMessage() {}

func (x *AddVoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventvote_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVoteResponse.ProtoReflect.Descriptor instead.
func (*AddVoteResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventvote_proto_rawDescGZIP(), []int{5}
}

func (x *AddVoteResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AddVoteResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GivePlatformPointResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GivePlatformPointResponse) Reset() {
	*x = GivePlatformPointResponse{}
	mi := &file_events_v1_eventvote_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GivePlatformPointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GivePlatformPointResponse) ProtoMessage() {}

func (x *GivePlatformPointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventvote_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GivePlatformPointResponse.ProtoReflect.Descriptor instead.
func (*GivePlatformPointResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventvote_proto_rawDescGZIP(), []int{6}
}

func (x *GivePlatformPointResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GivePlatformPointResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_events_v1_eventvote_proto protoreflect.FileDescriptor

const file_events_v1_eventvote_proto_rawDesc = "" +
	"\n" +
	"\x19events/v1/eventvote.proto\x12\rapi.events.v1\x1a\x14authz/v1/authz.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\x88\x01\n" +
	"\x0eAddVoteRequest\x124\n" +
	"\x16event_participation_id\x18\x01 \x01(\tR\x14eventParticipationId\x12(\n" +
	"\x10from_daily_point\x18\x02 \x01(\bR\x0efromDailyPoint\x12\x16\n" +
	"\x06points\x18\x03 \x01(\x05R\x06points\":\n" +
	"\x1dGetDailyPointAvailableRequest\x12\x19\n" +
	"\bevent_id\x18\x01 \x01(\tR\aeventId\">\n" +
	"\x1eGetDailyPointAvailableResponse\x12\x1c\n" +
	"\tavailable\x18\x01 \x01(\bR\tavailable\"\x82\x01\n" +
	"\x18GivePlatformPointRequest\x124\n" +
	"\x16event_participation_id\x18\x01 \x01(\tR\x14eventParticipationId\x12\x18\n" +
	"\aremarks\x18\x02 \x01(\tR\aremarks\x12\x16\n" +
	"\x06points\x18\x03 \x01(\x03R\x06points\"H\n" +
	"\x10GetPlatfromPoint\x124\n" +
	"\x16event_participation_id\x18\x01 \x01(\tR\x14eventParticipationId\"E\n" +
	"\x0fAddVoteResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"O\n" +
	"\x19GivePlatformPointResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xd1\x03\n" +
	"\x10EventVoteService\x12P\n" +
	"\aAddVote\x12\x1d.api.events.v1.AddVoteRequest\x1a\x1e.api.events.v1.AddVoteResponse\"\x06\x82\xb5\x18\x02\b\x01\x12}\n" +
	"\x16GetDailyPointAvailable\x12,.api.events.v1.GetDailyPointAvailableRequest\x1a-.api.events.v1.GetDailyPointAvailableResponse\"\x06\x82\xb5\x18\x02\b\x01\x12p\n" +
	"\x11GivePlatformPoint\x12'.api.events.v1.GivePlatformPointRequest\x1a(.api.events.v1.GivePlatformPointResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12z\n" +
	"$GetPlatfromPointOfEventParticipation\x12\x1f.api.events.v1.GetPlatfromPoint\x1a'.api.events.v1.GivePlatformPointRequest\"\b\x82\xb5\x18\x04\b\x01\x10\x01B2Z0github.com/nsp-inc/vtuber/api/events/v1;eventsv1b\x06proto3"

var (
	file_events_v1_eventvote_proto_rawDescOnce sync.Once
	file_events_v1_eventvote_proto_rawDescData []byte
)

func file_events_v1_eventvote_proto_rawDescGZIP() []byte {
	file_events_v1_eventvote_proto_rawDescOnce.Do(func() {
		file_events_v1_eventvote_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_events_v1_eventvote_proto_rawDesc), len(file_events_v1_eventvote_proto_rawDesc)))
	})
	return file_events_v1_eventvote_proto_rawDescData
}

var file_events_v1_eventvote_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_events_v1_eventvote_proto_goTypes = []any{
	(*AddVoteRequest)(nil),                 // 0: api.events.v1.AddVoteRequest
	(*GetDailyPointAvailableRequest)(nil),  // 1: api.events.v1.GetDailyPointAvailableRequest
	(*GetDailyPointAvailableResponse)(nil), // 2: api.events.v1.GetDailyPointAvailableResponse
	(*GivePlatformPointRequest)(nil),       // 3: api.events.v1.GivePlatformPointRequest
	(*GetPlatfromPoint)(nil),               // 4: api.events.v1.GetPlatfromPoint
	(*AddVoteResponse)(nil),                // 5: api.events.v1.AddVoteResponse
	(*GivePlatformPointResponse)(nil),      // 6: api.events.v1.GivePlatformPointResponse
}
var file_events_v1_eventvote_proto_depIdxs = []int32{
	0, // 0: api.events.v1.EventVoteService.AddVote:input_type -> api.events.v1.AddVoteRequest
	1, // 1: api.events.v1.EventVoteService.GetDailyPointAvailable:input_type -> api.events.v1.GetDailyPointAvailableRequest
	3, // 2: api.events.v1.EventVoteService.GivePlatformPoint:input_type -> api.events.v1.GivePlatformPointRequest
	4, // 3: api.events.v1.EventVoteService.GetPlatfromPointOfEventParticipation:input_type -> api.events.v1.GetPlatfromPoint
	5, // 4: api.events.v1.EventVoteService.AddVote:output_type -> api.events.v1.AddVoteResponse
	2, // 5: api.events.v1.EventVoteService.GetDailyPointAvailable:output_type -> api.events.v1.GetDailyPointAvailableResponse
	6, // 6: api.events.v1.EventVoteService.GivePlatformPoint:output_type -> api.events.v1.GivePlatformPointResponse
	3, // 7: api.events.v1.EventVoteService.GetPlatfromPointOfEventParticipation:output_type -> api.events.v1.GivePlatformPointRequest
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_events_v1_eventvote_proto_init() }
func file_events_v1_eventvote_proto_init() {
	if File_events_v1_eventvote_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_events_v1_eventvote_proto_rawDesc), len(file_events_v1_eventvote_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_events_v1_eventvote_proto_goTypes,
		DependencyIndexes: file_events_v1_eventvote_proto_depIdxs,
		MessageInfos:      file_events_v1_eventvote_proto_msgTypes,
	}.Build()
	File_events_v1_eventvote_proto = out.File
	file_events_v1_eventvote_proto_goTypes = nil
	file_events_v1_eventvote_proto_depIdxs = nil
}
