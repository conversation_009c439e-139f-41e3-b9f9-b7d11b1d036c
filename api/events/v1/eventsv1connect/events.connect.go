// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: events/v1/events.proto

package eventsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/events/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// EventServiceName is the fully-qualified name of the EventService service.
	EventServiceName = "api.events.v1.EventService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// EventServiceAddEventProcedure is the fully-qualified name of the EventService's AddEvent RPC.
	EventServiceAddEventProcedure = "/api.events.v1.EventService/AddEvent"
	// EventServiceGetAllEventsProcedure is the fully-qualified name of the EventService's GetAllEvents
	// RPC.
	EventServiceGetAllEventsProcedure = "/api.events.v1.EventService/GetAllEvents"
	// EventServiceGetEventByIdProcedure is the fully-qualified name of the EventService's GetEventById
	// RPC.
	EventServiceGetEventByIdProcedure = "/api.events.v1.EventService/GetEventById"
	// EventServiceDeleteEventByIdProcedure is the fully-qualified name of the EventService's
	// DeleteEventById RPC.
	EventServiceDeleteEventByIdProcedure = "/api.events.v1.EventService/DeleteEventById"
	// EventServiceUpdateEventByIdProcedure is the fully-qualified name of the EventService's
	// UpdateEventById RPC.
	EventServiceUpdateEventByIdProcedure = "/api.events.v1.EventService/UpdateEventById"
	// EventServiceGetAllEventsByCategoryProcedure is the fully-qualified name of the EventService's
	// GetAllEventsByCategory RPC.
	EventServiceGetAllEventsByCategoryProcedure = "/api.events.v1.EventService/GetAllEventsByCategory"
	// EventServiceApproveOrRejectEventProcedure is the fully-qualified name of the EventService's
	// ApproveOrRejectEvent RPC.
	EventServiceApproveOrRejectEventProcedure = "/api.events.v1.EventService/ApproveOrRejectEvent"
	// EventServiceGetMyEventsProcedure is the fully-qualified name of the EventService's GetMyEvents
	// RPC.
	EventServiceGetMyEventsProcedure = "/api.events.v1.EventService/GetMyEvents"
	// EventServiceGetUserEventsProcedure is the fully-qualified name of the EventService's
	// GetUserEvents RPC.
	EventServiceGetUserEventsProcedure = "/api.events.v1.EventService/GetUserEvents"
)

// EventServiceClient is a client for the api.events.v1.EventService service.
type EventServiceClient interface {
	AddEvent(context.Context, *connect.Request[v1.AddEventRequest]) (*connect.Response[v1.AddEventResponse], error)
	GetAllEvents(context.Context, *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error)
	GetEventById(context.Context, *connect.Request[v1.GetEventByIdRequest]) (*connect.Response[v1.GetEventByIdResponse], error)
	DeleteEventById(context.Context, *connect.Request[v1.DeleteEventByIdRequest]) (*connect.Response[v1.DeleteEventByIdResponse], error)
	UpdateEventById(context.Context, *connect.Request[v1.UpdateEventByIdRequest]) (*connect.Response[v1.UpdateEventByIdResponse], error)
	GetAllEventsByCategory(context.Context, *connect.Request[v1.GetAllEventsByCategoryRequest]) (*connect.Response[v1.GetAllEventsResponse], error)
	ApproveOrRejectEvent(context.Context, *connect.Request[v1.ApproveOrRejectEventRequest]) (*connect.Response[v1.ApproveOrRejectEventResponse], error)
	GetMyEvents(context.Context, *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error)
	GetUserEvents(context.Context, *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error)
}

// NewEventServiceClient constructs a client for the api.events.v1.EventService service. By default,
// it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and
// sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC()
// or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewEventServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) EventServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	eventServiceMethods := v1.File_events_v1_events_proto.Services().ByName("EventService").Methods()
	return &eventServiceClient{
		addEvent: connect.NewClient[v1.AddEventRequest, v1.AddEventResponse](
			httpClient,
			baseURL+EventServiceAddEventProcedure,
			connect.WithSchema(eventServiceMethods.ByName("AddEvent")),
			connect.WithClientOptions(opts...),
		),
		getAllEvents: connect.NewClient[v1.GetAllEventsRequest, v1.GetAllEventsResponse](
			httpClient,
			baseURL+EventServiceGetAllEventsProcedure,
			connect.WithSchema(eventServiceMethods.ByName("GetAllEvents")),
			connect.WithClientOptions(opts...),
		),
		getEventById: connect.NewClient[v1.GetEventByIdRequest, v1.GetEventByIdResponse](
			httpClient,
			baseURL+EventServiceGetEventByIdProcedure,
			connect.WithSchema(eventServiceMethods.ByName("GetEventById")),
			connect.WithClientOptions(opts...),
		),
		deleteEventById: connect.NewClient[v1.DeleteEventByIdRequest, v1.DeleteEventByIdResponse](
			httpClient,
			baseURL+EventServiceDeleteEventByIdProcedure,
			connect.WithSchema(eventServiceMethods.ByName("DeleteEventById")),
			connect.WithClientOptions(opts...),
		),
		updateEventById: connect.NewClient[v1.UpdateEventByIdRequest, v1.UpdateEventByIdResponse](
			httpClient,
			baseURL+EventServiceUpdateEventByIdProcedure,
			connect.WithSchema(eventServiceMethods.ByName("UpdateEventById")),
			connect.WithClientOptions(opts...),
		),
		getAllEventsByCategory: connect.NewClient[v1.GetAllEventsByCategoryRequest, v1.GetAllEventsResponse](
			httpClient,
			baseURL+EventServiceGetAllEventsByCategoryProcedure,
			connect.WithSchema(eventServiceMethods.ByName("GetAllEventsByCategory")),
			connect.WithClientOptions(opts...),
		),
		approveOrRejectEvent: connect.NewClient[v1.ApproveOrRejectEventRequest, v1.ApproveOrRejectEventResponse](
			httpClient,
			baseURL+EventServiceApproveOrRejectEventProcedure,
			connect.WithSchema(eventServiceMethods.ByName("ApproveOrRejectEvent")),
			connect.WithClientOptions(opts...),
		),
		getMyEvents: connect.NewClient[v1.GetAllEventsRequest, v1.GetAllEventsResponse](
			httpClient,
			baseURL+EventServiceGetMyEventsProcedure,
			connect.WithSchema(eventServiceMethods.ByName("GetMyEvents")),
			connect.WithClientOptions(opts...),
		),
		getUserEvents: connect.NewClient[v1.GetAllEventsRequest, v1.GetAllEventsResponse](
			httpClient,
			baseURL+EventServiceGetUserEventsProcedure,
			connect.WithSchema(eventServiceMethods.ByName("GetUserEvents")),
			connect.WithClientOptions(opts...),
		),
	}
}

// eventServiceClient implements EventServiceClient.
type eventServiceClient struct {
	addEvent               *connect.Client[v1.AddEventRequest, v1.AddEventResponse]
	getAllEvents           *connect.Client[v1.GetAllEventsRequest, v1.GetAllEventsResponse]
	getEventById           *connect.Client[v1.GetEventByIdRequest, v1.GetEventByIdResponse]
	deleteEventById        *connect.Client[v1.DeleteEventByIdRequest, v1.DeleteEventByIdResponse]
	updateEventById        *connect.Client[v1.UpdateEventByIdRequest, v1.UpdateEventByIdResponse]
	getAllEventsByCategory *connect.Client[v1.GetAllEventsByCategoryRequest, v1.GetAllEventsResponse]
	approveOrRejectEvent   *connect.Client[v1.ApproveOrRejectEventRequest, v1.ApproveOrRejectEventResponse]
	getMyEvents            *connect.Client[v1.GetAllEventsRequest, v1.GetAllEventsResponse]
	getUserEvents          *connect.Client[v1.GetAllEventsRequest, v1.GetAllEventsResponse]
}

// AddEvent calls api.events.v1.EventService.AddEvent.
func (c *eventServiceClient) AddEvent(ctx context.Context, req *connect.Request[v1.AddEventRequest]) (*connect.Response[v1.AddEventResponse], error) {
	return c.addEvent.CallUnary(ctx, req)
}

// GetAllEvents calls api.events.v1.EventService.GetAllEvents.
func (c *eventServiceClient) GetAllEvents(ctx context.Context, req *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error) {
	return c.getAllEvents.CallUnary(ctx, req)
}

// GetEventById calls api.events.v1.EventService.GetEventById.
func (c *eventServiceClient) GetEventById(ctx context.Context, req *connect.Request[v1.GetEventByIdRequest]) (*connect.Response[v1.GetEventByIdResponse], error) {
	return c.getEventById.CallUnary(ctx, req)
}

// DeleteEventById calls api.events.v1.EventService.DeleteEventById.
func (c *eventServiceClient) DeleteEventById(ctx context.Context, req *connect.Request[v1.DeleteEventByIdRequest]) (*connect.Response[v1.DeleteEventByIdResponse], error) {
	return c.deleteEventById.CallUnary(ctx, req)
}

// UpdateEventById calls api.events.v1.EventService.UpdateEventById.
func (c *eventServiceClient) UpdateEventById(ctx context.Context, req *connect.Request[v1.UpdateEventByIdRequest]) (*connect.Response[v1.UpdateEventByIdResponse], error) {
	return c.updateEventById.CallUnary(ctx, req)
}

// GetAllEventsByCategory calls api.events.v1.EventService.GetAllEventsByCategory.
func (c *eventServiceClient) GetAllEventsByCategory(ctx context.Context, req *connect.Request[v1.GetAllEventsByCategoryRequest]) (*connect.Response[v1.GetAllEventsResponse], error) {
	return c.getAllEventsByCategory.CallUnary(ctx, req)
}

// ApproveOrRejectEvent calls api.events.v1.EventService.ApproveOrRejectEvent.
func (c *eventServiceClient) ApproveOrRejectEvent(ctx context.Context, req *connect.Request[v1.ApproveOrRejectEventRequest]) (*connect.Response[v1.ApproveOrRejectEventResponse], error) {
	return c.approveOrRejectEvent.CallUnary(ctx, req)
}

// GetMyEvents calls api.events.v1.EventService.GetMyEvents.
func (c *eventServiceClient) GetMyEvents(ctx context.Context, req *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error) {
	return c.getMyEvents.CallUnary(ctx, req)
}

// GetUserEvents calls api.events.v1.EventService.GetUserEvents.
func (c *eventServiceClient) GetUserEvents(ctx context.Context, req *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error) {
	return c.getUserEvents.CallUnary(ctx, req)
}

// EventServiceHandler is an implementation of the api.events.v1.EventService service.
type EventServiceHandler interface {
	AddEvent(context.Context, *connect.Request[v1.AddEventRequest]) (*connect.Response[v1.AddEventResponse], error)
	GetAllEvents(context.Context, *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error)
	GetEventById(context.Context, *connect.Request[v1.GetEventByIdRequest]) (*connect.Response[v1.GetEventByIdResponse], error)
	DeleteEventById(context.Context, *connect.Request[v1.DeleteEventByIdRequest]) (*connect.Response[v1.DeleteEventByIdResponse], error)
	UpdateEventById(context.Context, *connect.Request[v1.UpdateEventByIdRequest]) (*connect.Response[v1.UpdateEventByIdResponse], error)
	GetAllEventsByCategory(context.Context, *connect.Request[v1.GetAllEventsByCategoryRequest]) (*connect.Response[v1.GetAllEventsResponse], error)
	ApproveOrRejectEvent(context.Context, *connect.Request[v1.ApproveOrRejectEventRequest]) (*connect.Response[v1.ApproveOrRejectEventResponse], error)
	GetMyEvents(context.Context, *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error)
	GetUserEvents(context.Context, *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error)
}

// NewEventServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewEventServiceHandler(svc EventServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	eventServiceMethods := v1.File_events_v1_events_proto.Services().ByName("EventService").Methods()
	eventServiceAddEventHandler := connect.NewUnaryHandler(
		EventServiceAddEventProcedure,
		svc.AddEvent,
		connect.WithSchema(eventServiceMethods.ByName("AddEvent")),
		connect.WithHandlerOptions(opts...),
	)
	eventServiceGetAllEventsHandler := connect.NewUnaryHandler(
		EventServiceGetAllEventsProcedure,
		svc.GetAllEvents,
		connect.WithSchema(eventServiceMethods.ByName("GetAllEvents")),
		connect.WithHandlerOptions(opts...),
	)
	eventServiceGetEventByIdHandler := connect.NewUnaryHandler(
		EventServiceGetEventByIdProcedure,
		svc.GetEventById,
		connect.WithSchema(eventServiceMethods.ByName("GetEventById")),
		connect.WithHandlerOptions(opts...),
	)
	eventServiceDeleteEventByIdHandler := connect.NewUnaryHandler(
		EventServiceDeleteEventByIdProcedure,
		svc.DeleteEventById,
		connect.WithSchema(eventServiceMethods.ByName("DeleteEventById")),
		connect.WithHandlerOptions(opts...),
	)
	eventServiceUpdateEventByIdHandler := connect.NewUnaryHandler(
		EventServiceUpdateEventByIdProcedure,
		svc.UpdateEventById,
		connect.WithSchema(eventServiceMethods.ByName("UpdateEventById")),
		connect.WithHandlerOptions(opts...),
	)
	eventServiceGetAllEventsByCategoryHandler := connect.NewUnaryHandler(
		EventServiceGetAllEventsByCategoryProcedure,
		svc.GetAllEventsByCategory,
		connect.WithSchema(eventServiceMethods.ByName("GetAllEventsByCategory")),
		connect.WithHandlerOptions(opts...),
	)
	eventServiceApproveOrRejectEventHandler := connect.NewUnaryHandler(
		EventServiceApproveOrRejectEventProcedure,
		svc.ApproveOrRejectEvent,
		connect.WithSchema(eventServiceMethods.ByName("ApproveOrRejectEvent")),
		connect.WithHandlerOptions(opts...),
	)
	eventServiceGetMyEventsHandler := connect.NewUnaryHandler(
		EventServiceGetMyEventsProcedure,
		svc.GetMyEvents,
		connect.WithSchema(eventServiceMethods.ByName("GetMyEvents")),
		connect.WithHandlerOptions(opts...),
	)
	eventServiceGetUserEventsHandler := connect.NewUnaryHandler(
		EventServiceGetUserEventsProcedure,
		svc.GetUserEvents,
		connect.WithSchema(eventServiceMethods.ByName("GetUserEvents")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.events.v1.EventService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case EventServiceAddEventProcedure:
			eventServiceAddEventHandler.ServeHTTP(w, r)
		case EventServiceGetAllEventsProcedure:
			eventServiceGetAllEventsHandler.ServeHTTP(w, r)
		case EventServiceGetEventByIdProcedure:
			eventServiceGetEventByIdHandler.ServeHTTP(w, r)
		case EventServiceDeleteEventByIdProcedure:
			eventServiceDeleteEventByIdHandler.ServeHTTP(w, r)
		case EventServiceUpdateEventByIdProcedure:
			eventServiceUpdateEventByIdHandler.ServeHTTP(w, r)
		case EventServiceGetAllEventsByCategoryProcedure:
			eventServiceGetAllEventsByCategoryHandler.ServeHTTP(w, r)
		case EventServiceApproveOrRejectEventProcedure:
			eventServiceApproveOrRejectEventHandler.ServeHTTP(w, r)
		case EventServiceGetMyEventsProcedure:
			eventServiceGetMyEventsHandler.ServeHTTP(w, r)
		case EventServiceGetUserEventsProcedure:
			eventServiceGetUserEventsHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedEventServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedEventServiceHandler struct{}

func (UnimplementedEventServiceHandler) AddEvent(context.Context, *connect.Request[v1.AddEventRequest]) (*connect.Response[v1.AddEventResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventService.AddEvent is not implemented"))
}

func (UnimplementedEventServiceHandler) GetAllEvents(context.Context, *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventService.GetAllEvents is not implemented"))
}

func (UnimplementedEventServiceHandler) GetEventById(context.Context, *connect.Request[v1.GetEventByIdRequest]) (*connect.Response[v1.GetEventByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventService.GetEventById is not implemented"))
}

func (UnimplementedEventServiceHandler) DeleteEventById(context.Context, *connect.Request[v1.DeleteEventByIdRequest]) (*connect.Response[v1.DeleteEventByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventService.DeleteEventById is not implemented"))
}

func (UnimplementedEventServiceHandler) UpdateEventById(context.Context, *connect.Request[v1.UpdateEventByIdRequest]) (*connect.Response[v1.UpdateEventByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventService.UpdateEventById is not implemented"))
}

func (UnimplementedEventServiceHandler) GetAllEventsByCategory(context.Context, *connect.Request[v1.GetAllEventsByCategoryRequest]) (*connect.Response[v1.GetAllEventsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventService.GetAllEventsByCategory is not implemented"))
}

func (UnimplementedEventServiceHandler) ApproveOrRejectEvent(context.Context, *connect.Request[v1.ApproveOrRejectEventRequest]) (*connect.Response[v1.ApproveOrRejectEventResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventService.ApproveOrRejectEvent is not implemented"))
}

func (UnimplementedEventServiceHandler) GetMyEvents(context.Context, *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventService.GetMyEvents is not implemented"))
}

func (UnimplementedEventServiceHandler) GetUserEvents(context.Context, *connect.Request[v1.GetAllEventsRequest]) (*connect.Response[v1.GetAllEventsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventService.GetUserEvents is not implemented"))
}
