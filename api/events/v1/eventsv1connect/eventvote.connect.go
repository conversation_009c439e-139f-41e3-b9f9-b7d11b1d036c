// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: events/v1/eventvote.proto

package eventsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/events/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// EventVoteServiceName is the fully-qualified name of the EventVoteService service.
	EventVoteServiceName = "api.events.v1.EventVoteService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// EventVoteServiceAddVoteProcedure is the fully-qualified name of the EventVoteService's AddVote
	// RPC.
	EventVoteServiceAddVoteProcedure = "/api.events.v1.EventVoteService/AddVote"
	// EventVoteServiceGetDailyPointAvailableProcedure is the fully-qualified name of the
	// EventVoteService's GetDailyPointAvailable RPC.
	EventVoteServiceGetDailyPointAvailableProcedure = "/api.events.v1.EventVoteService/GetDailyPointAvailable"
	// EventVoteServiceGivePlatformPointProcedure is the fully-qualified name of the EventVoteService's
	// GivePlatformPoint RPC.
	EventVoteServiceGivePlatformPointProcedure = "/api.events.v1.EventVoteService/GivePlatformPoint"
	// EventVoteServiceGetPlatfromPointOfEventParticipationProcedure is the fully-qualified name of the
	// EventVoteService's GetPlatfromPointOfEventParticipation RPC.
	EventVoteServiceGetPlatfromPointOfEventParticipationProcedure = "/api.events.v1.EventVoteService/GetPlatfromPointOfEventParticipation"
)

// EventVoteServiceClient is a client for the api.events.v1.EventVoteService service.
type EventVoteServiceClient interface {
	AddVote(context.Context, *connect.Request[v1.AddVoteRequest]) (*connect.Response[v1.AddVoteResponse], error)
	GetDailyPointAvailable(context.Context, *connect.Request[v1.GetDailyPointAvailableRequest]) (*connect.Response[v1.GetDailyPointAvailableResponse], error)
	GivePlatformPoint(context.Context, *connect.Request[v1.GivePlatformPointRequest]) (*connect.Response[v1.GivePlatformPointResponse], error)
	GetPlatfromPointOfEventParticipation(context.Context, *connect.Request[v1.GetPlatfromPoint]) (*connect.Response[v1.GivePlatformPointRequest], error)
}

// NewEventVoteServiceClient constructs a client for the api.events.v1.EventVoteService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewEventVoteServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) EventVoteServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	eventVoteServiceMethods := v1.File_events_v1_eventvote_proto.Services().ByName("EventVoteService").Methods()
	return &eventVoteServiceClient{
		addVote: connect.NewClient[v1.AddVoteRequest, v1.AddVoteResponse](
			httpClient,
			baseURL+EventVoteServiceAddVoteProcedure,
			connect.WithSchema(eventVoteServiceMethods.ByName("AddVote")),
			connect.WithClientOptions(opts...),
		),
		getDailyPointAvailable: connect.NewClient[v1.GetDailyPointAvailableRequest, v1.GetDailyPointAvailableResponse](
			httpClient,
			baseURL+EventVoteServiceGetDailyPointAvailableProcedure,
			connect.WithSchema(eventVoteServiceMethods.ByName("GetDailyPointAvailable")),
			connect.WithClientOptions(opts...),
		),
		givePlatformPoint: connect.NewClient[v1.GivePlatformPointRequest, v1.GivePlatformPointResponse](
			httpClient,
			baseURL+EventVoteServiceGivePlatformPointProcedure,
			connect.WithSchema(eventVoteServiceMethods.ByName("GivePlatformPoint")),
			connect.WithClientOptions(opts...),
		),
		getPlatfromPointOfEventParticipation: connect.NewClient[v1.GetPlatfromPoint, v1.GivePlatformPointRequest](
			httpClient,
			baseURL+EventVoteServiceGetPlatfromPointOfEventParticipationProcedure,
			connect.WithSchema(eventVoteServiceMethods.ByName("GetPlatfromPointOfEventParticipation")),
			connect.WithClientOptions(opts...),
		),
	}
}

// eventVoteServiceClient implements EventVoteServiceClient.
type eventVoteServiceClient struct {
	addVote                              *connect.Client[v1.AddVoteRequest, v1.AddVoteResponse]
	getDailyPointAvailable               *connect.Client[v1.GetDailyPointAvailableRequest, v1.GetDailyPointAvailableResponse]
	givePlatformPoint                    *connect.Client[v1.GivePlatformPointRequest, v1.GivePlatformPointResponse]
	getPlatfromPointOfEventParticipation *connect.Client[v1.GetPlatfromPoint, v1.GivePlatformPointRequest]
}

// AddVote calls api.events.v1.EventVoteService.AddVote.
func (c *eventVoteServiceClient) AddVote(ctx context.Context, req *connect.Request[v1.AddVoteRequest]) (*connect.Response[v1.AddVoteResponse], error) {
	return c.addVote.CallUnary(ctx, req)
}

// GetDailyPointAvailable calls api.events.v1.EventVoteService.GetDailyPointAvailable.
func (c *eventVoteServiceClient) GetDailyPointAvailable(ctx context.Context, req *connect.Request[v1.GetDailyPointAvailableRequest]) (*connect.Response[v1.GetDailyPointAvailableResponse], error) {
	return c.getDailyPointAvailable.CallUnary(ctx, req)
}

// GivePlatformPoint calls api.events.v1.EventVoteService.GivePlatformPoint.
func (c *eventVoteServiceClient) GivePlatformPoint(ctx context.Context, req *connect.Request[v1.GivePlatformPointRequest]) (*connect.Response[v1.GivePlatformPointResponse], error) {
	return c.givePlatformPoint.CallUnary(ctx, req)
}

// GetPlatfromPointOfEventParticipation calls
// api.events.v1.EventVoteService.GetPlatfromPointOfEventParticipation.
func (c *eventVoteServiceClient) GetPlatfromPointOfEventParticipation(ctx context.Context, req *connect.Request[v1.GetPlatfromPoint]) (*connect.Response[v1.GivePlatformPointRequest], error) {
	return c.getPlatfromPointOfEventParticipation.CallUnary(ctx, req)
}

// EventVoteServiceHandler is an implementation of the api.events.v1.EventVoteService service.
type EventVoteServiceHandler interface {
	AddVote(context.Context, *connect.Request[v1.AddVoteRequest]) (*connect.Response[v1.AddVoteResponse], error)
	GetDailyPointAvailable(context.Context, *connect.Request[v1.GetDailyPointAvailableRequest]) (*connect.Response[v1.GetDailyPointAvailableResponse], error)
	GivePlatformPoint(context.Context, *connect.Request[v1.GivePlatformPointRequest]) (*connect.Response[v1.GivePlatformPointResponse], error)
	GetPlatfromPointOfEventParticipation(context.Context, *connect.Request[v1.GetPlatfromPoint]) (*connect.Response[v1.GivePlatformPointRequest], error)
}

// NewEventVoteServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewEventVoteServiceHandler(svc EventVoteServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	eventVoteServiceMethods := v1.File_events_v1_eventvote_proto.Services().ByName("EventVoteService").Methods()
	eventVoteServiceAddVoteHandler := connect.NewUnaryHandler(
		EventVoteServiceAddVoteProcedure,
		svc.AddVote,
		connect.WithSchema(eventVoteServiceMethods.ByName("AddVote")),
		connect.WithHandlerOptions(opts...),
	)
	eventVoteServiceGetDailyPointAvailableHandler := connect.NewUnaryHandler(
		EventVoteServiceGetDailyPointAvailableProcedure,
		svc.GetDailyPointAvailable,
		connect.WithSchema(eventVoteServiceMethods.ByName("GetDailyPointAvailable")),
		connect.WithHandlerOptions(opts...),
	)
	eventVoteServiceGivePlatformPointHandler := connect.NewUnaryHandler(
		EventVoteServiceGivePlatformPointProcedure,
		svc.GivePlatformPoint,
		connect.WithSchema(eventVoteServiceMethods.ByName("GivePlatformPoint")),
		connect.WithHandlerOptions(opts...),
	)
	eventVoteServiceGetPlatfromPointOfEventParticipationHandler := connect.NewUnaryHandler(
		EventVoteServiceGetPlatfromPointOfEventParticipationProcedure,
		svc.GetPlatfromPointOfEventParticipation,
		connect.WithSchema(eventVoteServiceMethods.ByName("GetPlatfromPointOfEventParticipation")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.events.v1.EventVoteService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case EventVoteServiceAddVoteProcedure:
			eventVoteServiceAddVoteHandler.ServeHTTP(w, r)
		case EventVoteServiceGetDailyPointAvailableProcedure:
			eventVoteServiceGetDailyPointAvailableHandler.ServeHTTP(w, r)
		case EventVoteServiceGivePlatformPointProcedure:
			eventVoteServiceGivePlatformPointHandler.ServeHTTP(w, r)
		case EventVoteServiceGetPlatfromPointOfEventParticipationProcedure:
			eventVoteServiceGetPlatfromPointOfEventParticipationHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedEventVoteServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedEventVoteServiceHandler struct{}

func (UnimplementedEventVoteServiceHandler) AddVote(context.Context, *connect.Request[v1.AddVoteRequest]) (*connect.Response[v1.AddVoteResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventVoteService.AddVote is not implemented"))
}

func (UnimplementedEventVoteServiceHandler) GetDailyPointAvailable(context.Context, *connect.Request[v1.GetDailyPointAvailableRequest]) (*connect.Response[v1.GetDailyPointAvailableResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventVoteService.GetDailyPointAvailable is not implemented"))
}

func (UnimplementedEventVoteServiceHandler) GivePlatformPoint(context.Context, *connect.Request[v1.GivePlatformPointRequest]) (*connect.Response[v1.GivePlatformPointResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventVoteService.GivePlatformPoint is not implemented"))
}

func (UnimplementedEventVoteServiceHandler) GetPlatfromPointOfEventParticipation(context.Context, *connect.Request[v1.GetPlatfromPoint]) (*connect.Response[v1.GivePlatformPointRequest], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventVoteService.GetPlatfromPointOfEventParticipation is not implemented"))
}
