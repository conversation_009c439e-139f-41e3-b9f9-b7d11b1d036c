// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: events/v1/eventcomment.proto

package eventsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/events/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// EventCommentServiceName is the fully-qualified name of the EventCommentService service.
	EventCommentServiceName = "api.events.v1.EventCommentService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// EventCommentServiceAddEventCommentProcedure is the fully-qualified name of the
	// EventCommentService's AddEventComment RPC.
	EventCommentServiceAddEventCommentProcedure = "/api.events.v1.EventCommentService/AddEventComment"
	// EventCommentServiceGetAllEventCommentsProcedure is the fully-qualified name of the
	// EventCommentService's GetAllEventComments RPC.
	EventCommentServiceGetAllEventCommentsProcedure = "/api.events.v1.EventCommentService/GetAllEventComments"
	// EventCommentServiceGetAllRepliesOfEventCommentProcedure is the fully-qualified name of the
	// EventCommentService's GetAllRepliesOfEventComment RPC.
	EventCommentServiceGetAllRepliesOfEventCommentProcedure = "/api.events.v1.EventCommentService/GetAllRepliesOfEventComment"
	// EventCommentServiceGetEventCommentByIdProcedure is the fully-qualified name of the
	// EventCommentService's GetEventCommentById RPC.
	EventCommentServiceGetEventCommentByIdProcedure = "/api.events.v1.EventCommentService/GetEventCommentById"
	// EventCommentServiceDeleteEventCommentByIdProcedure is the fully-qualified name of the
	// EventCommentService's DeleteEventCommentById RPC.
	EventCommentServiceDeleteEventCommentByIdProcedure = "/api.events.v1.EventCommentService/DeleteEventCommentById"
	// EventCommentServiceUpdateEventCommentByIdProcedure is the fully-qualified name of the
	// EventCommentService's UpdateEventCommentById RPC.
	EventCommentServiceUpdateEventCommentByIdProcedure = "/api.events.v1.EventCommentService/UpdateEventCommentById"
)

// EventCommentServiceClient is a client for the api.events.v1.EventCommentService service.
type EventCommentServiceClient interface {
	AddEventComment(context.Context, *connect.Request[v1.AddEventCommentRequest]) (*connect.Response[v1.AddEventCommentResponse], error)
	GetAllEventComments(context.Context, *connect.Request[v1.GetAllEventCommentsRequest]) (*connect.Response[v1.GetAllEventCommentsResponse], error)
	GetAllRepliesOfEventComment(context.Context, *connect.Request[v1.GetEventCommentByIdRequest]) (*connect.Response[v1.GetAllEventCommentsResponse], error)
	GetEventCommentById(context.Context, *connect.Request[v1.GetEventCommentByIdRequest]) (*connect.Response[v1.GetEventCommentByIdResponse], error)
	DeleteEventCommentById(context.Context, *connect.Request[v1.DeleteEventCommentByIdRequest]) (*connect.Response[v1.DeleteEventCommentByIdResponse], error)
	UpdateEventCommentById(context.Context, *connect.Request[v1.UpdateEventCommentByIdRequest]) (*connect.Response[v1.UpdateEventCommentByIdResponse], error)
}

// NewEventCommentServiceClient constructs a client for the api.events.v1.EventCommentService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewEventCommentServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) EventCommentServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	eventCommentServiceMethods := v1.File_events_v1_eventcomment_proto.Services().ByName("EventCommentService").Methods()
	return &eventCommentServiceClient{
		addEventComment: connect.NewClient[v1.AddEventCommentRequest, v1.AddEventCommentResponse](
			httpClient,
			baseURL+EventCommentServiceAddEventCommentProcedure,
			connect.WithSchema(eventCommentServiceMethods.ByName("AddEventComment")),
			connect.WithClientOptions(opts...),
		),
		getAllEventComments: connect.NewClient[v1.GetAllEventCommentsRequest, v1.GetAllEventCommentsResponse](
			httpClient,
			baseURL+EventCommentServiceGetAllEventCommentsProcedure,
			connect.WithSchema(eventCommentServiceMethods.ByName("GetAllEventComments")),
			connect.WithClientOptions(opts...),
		),
		getAllRepliesOfEventComment: connect.NewClient[v1.GetEventCommentByIdRequest, v1.GetAllEventCommentsResponse](
			httpClient,
			baseURL+EventCommentServiceGetAllRepliesOfEventCommentProcedure,
			connect.WithSchema(eventCommentServiceMethods.ByName("GetAllRepliesOfEventComment")),
			connect.WithClientOptions(opts...),
		),
		getEventCommentById: connect.NewClient[v1.GetEventCommentByIdRequest, v1.GetEventCommentByIdResponse](
			httpClient,
			baseURL+EventCommentServiceGetEventCommentByIdProcedure,
			connect.WithSchema(eventCommentServiceMethods.ByName("GetEventCommentById")),
			connect.WithClientOptions(opts...),
		),
		deleteEventCommentById: connect.NewClient[v1.DeleteEventCommentByIdRequest, v1.DeleteEventCommentByIdResponse](
			httpClient,
			baseURL+EventCommentServiceDeleteEventCommentByIdProcedure,
			connect.WithSchema(eventCommentServiceMethods.ByName("DeleteEventCommentById")),
			connect.WithClientOptions(opts...),
		),
		updateEventCommentById: connect.NewClient[v1.UpdateEventCommentByIdRequest, v1.UpdateEventCommentByIdResponse](
			httpClient,
			baseURL+EventCommentServiceUpdateEventCommentByIdProcedure,
			connect.WithSchema(eventCommentServiceMethods.ByName("UpdateEventCommentById")),
			connect.WithClientOptions(opts...),
		),
	}
}

// eventCommentServiceClient implements EventCommentServiceClient.
type eventCommentServiceClient struct {
	addEventComment             *connect.Client[v1.AddEventCommentRequest, v1.AddEventCommentResponse]
	getAllEventComments         *connect.Client[v1.GetAllEventCommentsRequest, v1.GetAllEventCommentsResponse]
	getAllRepliesOfEventComment *connect.Client[v1.GetEventCommentByIdRequest, v1.GetAllEventCommentsResponse]
	getEventCommentById         *connect.Client[v1.GetEventCommentByIdRequest, v1.GetEventCommentByIdResponse]
	deleteEventCommentById      *connect.Client[v1.DeleteEventCommentByIdRequest, v1.DeleteEventCommentByIdResponse]
	updateEventCommentById      *connect.Client[v1.UpdateEventCommentByIdRequest, v1.UpdateEventCommentByIdResponse]
}

// AddEventComment calls api.events.v1.EventCommentService.AddEventComment.
func (c *eventCommentServiceClient) AddEventComment(ctx context.Context, req *connect.Request[v1.AddEventCommentRequest]) (*connect.Response[v1.AddEventCommentResponse], error) {
	return c.addEventComment.CallUnary(ctx, req)
}

// GetAllEventComments calls api.events.v1.EventCommentService.GetAllEventComments.
func (c *eventCommentServiceClient) GetAllEventComments(ctx context.Context, req *connect.Request[v1.GetAllEventCommentsRequest]) (*connect.Response[v1.GetAllEventCommentsResponse], error) {
	return c.getAllEventComments.CallUnary(ctx, req)
}

// GetAllRepliesOfEventComment calls api.events.v1.EventCommentService.GetAllRepliesOfEventComment.
func (c *eventCommentServiceClient) GetAllRepliesOfEventComment(ctx context.Context, req *connect.Request[v1.GetEventCommentByIdRequest]) (*connect.Response[v1.GetAllEventCommentsResponse], error) {
	return c.getAllRepliesOfEventComment.CallUnary(ctx, req)
}

// GetEventCommentById calls api.events.v1.EventCommentService.GetEventCommentById.
func (c *eventCommentServiceClient) GetEventCommentById(ctx context.Context, req *connect.Request[v1.GetEventCommentByIdRequest]) (*connect.Response[v1.GetEventCommentByIdResponse], error) {
	return c.getEventCommentById.CallUnary(ctx, req)
}

// DeleteEventCommentById calls api.events.v1.EventCommentService.DeleteEventCommentById.
func (c *eventCommentServiceClient) DeleteEventCommentById(ctx context.Context, req *connect.Request[v1.DeleteEventCommentByIdRequest]) (*connect.Response[v1.DeleteEventCommentByIdResponse], error) {
	return c.deleteEventCommentById.CallUnary(ctx, req)
}

// UpdateEventCommentById calls api.events.v1.EventCommentService.UpdateEventCommentById.
func (c *eventCommentServiceClient) UpdateEventCommentById(ctx context.Context, req *connect.Request[v1.UpdateEventCommentByIdRequest]) (*connect.Response[v1.UpdateEventCommentByIdResponse], error) {
	return c.updateEventCommentById.CallUnary(ctx, req)
}

// EventCommentServiceHandler is an implementation of the api.events.v1.EventCommentService service.
type EventCommentServiceHandler interface {
	AddEventComment(context.Context, *connect.Request[v1.AddEventCommentRequest]) (*connect.Response[v1.AddEventCommentResponse], error)
	GetAllEventComments(context.Context, *connect.Request[v1.GetAllEventCommentsRequest]) (*connect.Response[v1.GetAllEventCommentsResponse], error)
	GetAllRepliesOfEventComment(context.Context, *connect.Request[v1.GetEventCommentByIdRequest]) (*connect.Response[v1.GetAllEventCommentsResponse], error)
	GetEventCommentById(context.Context, *connect.Request[v1.GetEventCommentByIdRequest]) (*connect.Response[v1.GetEventCommentByIdResponse], error)
	DeleteEventCommentById(context.Context, *connect.Request[v1.DeleteEventCommentByIdRequest]) (*connect.Response[v1.DeleteEventCommentByIdResponse], error)
	UpdateEventCommentById(context.Context, *connect.Request[v1.UpdateEventCommentByIdRequest]) (*connect.Response[v1.UpdateEventCommentByIdResponse], error)
}

// NewEventCommentServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewEventCommentServiceHandler(svc EventCommentServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	eventCommentServiceMethods := v1.File_events_v1_eventcomment_proto.Services().ByName("EventCommentService").Methods()
	eventCommentServiceAddEventCommentHandler := connect.NewUnaryHandler(
		EventCommentServiceAddEventCommentProcedure,
		svc.AddEventComment,
		connect.WithSchema(eventCommentServiceMethods.ByName("AddEventComment")),
		connect.WithHandlerOptions(opts...),
	)
	eventCommentServiceGetAllEventCommentsHandler := connect.NewUnaryHandler(
		EventCommentServiceGetAllEventCommentsProcedure,
		svc.GetAllEventComments,
		connect.WithSchema(eventCommentServiceMethods.ByName("GetAllEventComments")),
		connect.WithHandlerOptions(opts...),
	)
	eventCommentServiceGetAllRepliesOfEventCommentHandler := connect.NewUnaryHandler(
		EventCommentServiceGetAllRepliesOfEventCommentProcedure,
		svc.GetAllRepliesOfEventComment,
		connect.WithSchema(eventCommentServiceMethods.ByName("GetAllRepliesOfEventComment")),
		connect.WithHandlerOptions(opts...),
	)
	eventCommentServiceGetEventCommentByIdHandler := connect.NewUnaryHandler(
		EventCommentServiceGetEventCommentByIdProcedure,
		svc.GetEventCommentById,
		connect.WithSchema(eventCommentServiceMethods.ByName("GetEventCommentById")),
		connect.WithHandlerOptions(opts...),
	)
	eventCommentServiceDeleteEventCommentByIdHandler := connect.NewUnaryHandler(
		EventCommentServiceDeleteEventCommentByIdProcedure,
		svc.DeleteEventCommentById,
		connect.WithSchema(eventCommentServiceMethods.ByName("DeleteEventCommentById")),
		connect.WithHandlerOptions(opts...),
	)
	eventCommentServiceUpdateEventCommentByIdHandler := connect.NewUnaryHandler(
		EventCommentServiceUpdateEventCommentByIdProcedure,
		svc.UpdateEventCommentById,
		connect.WithSchema(eventCommentServiceMethods.ByName("UpdateEventCommentById")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.events.v1.EventCommentService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case EventCommentServiceAddEventCommentProcedure:
			eventCommentServiceAddEventCommentHandler.ServeHTTP(w, r)
		case EventCommentServiceGetAllEventCommentsProcedure:
			eventCommentServiceGetAllEventCommentsHandler.ServeHTTP(w, r)
		case EventCommentServiceGetAllRepliesOfEventCommentProcedure:
			eventCommentServiceGetAllRepliesOfEventCommentHandler.ServeHTTP(w, r)
		case EventCommentServiceGetEventCommentByIdProcedure:
			eventCommentServiceGetEventCommentByIdHandler.ServeHTTP(w, r)
		case EventCommentServiceDeleteEventCommentByIdProcedure:
			eventCommentServiceDeleteEventCommentByIdHandler.ServeHTTP(w, r)
		case EventCommentServiceUpdateEventCommentByIdProcedure:
			eventCommentServiceUpdateEventCommentByIdHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedEventCommentServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedEventCommentServiceHandler struct{}

func (UnimplementedEventCommentServiceHandler) AddEventComment(context.Context, *connect.Request[v1.AddEventCommentRequest]) (*connect.Response[v1.AddEventCommentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventCommentService.AddEventComment is not implemented"))
}

func (UnimplementedEventCommentServiceHandler) GetAllEventComments(context.Context, *connect.Request[v1.GetAllEventCommentsRequest]) (*connect.Response[v1.GetAllEventCommentsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventCommentService.GetAllEventComments is not implemented"))
}

func (UnimplementedEventCommentServiceHandler) GetAllRepliesOfEventComment(context.Context, *connect.Request[v1.GetEventCommentByIdRequest]) (*connect.Response[v1.GetAllEventCommentsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventCommentService.GetAllRepliesOfEventComment is not implemented"))
}

func (UnimplementedEventCommentServiceHandler) GetEventCommentById(context.Context, *connect.Request[v1.GetEventCommentByIdRequest]) (*connect.Response[v1.GetEventCommentByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventCommentService.GetEventCommentById is not implemented"))
}

func (UnimplementedEventCommentServiceHandler) DeleteEventCommentById(context.Context, *connect.Request[v1.DeleteEventCommentByIdRequest]) (*connect.Response[v1.DeleteEventCommentByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventCommentService.DeleteEventCommentById is not implemented"))
}

func (UnimplementedEventCommentServiceHandler) UpdateEventCommentById(context.Context, *connect.Request[v1.UpdateEventCommentByIdRequest]) (*connect.Response[v1.UpdateEventCommentByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventCommentService.UpdateEventCommentById is not implemented"))
}
