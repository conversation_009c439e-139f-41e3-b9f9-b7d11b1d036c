// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: events/v1/eventparticipant.proto

package eventsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/events/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// EventParticipantServiceName is the fully-qualified name of the EventParticipantService service.
	EventParticipantServiceName = "api.events.v1.EventParticipantService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// EventParticipantServiceAddEventParticipationProcedure is the fully-qualified name of the
	// EventParticipantService's AddEventParticipation RPC.
	EventParticipantServiceAddEventParticipationProcedure = "/api.events.v1.EventParticipantService/AddEventParticipation"
	// EventParticipantServiceGetEventParticipantsByEventIdProcedure is the fully-qualified name of the
	// EventParticipantService's GetEventParticipantsByEventId RPC.
	EventParticipantServiceGetEventParticipantsByEventIdProcedure = "/api.events.v1.EventParticipantService/GetEventParticipantsByEventId"
	// EventParticipantServiceGetMyEventParticipationProcedure is the fully-qualified name of the
	// EventParticipantService's GetMyEventParticipation RPC.
	EventParticipantServiceGetMyEventParticipationProcedure = "/api.events.v1.EventParticipantService/GetMyEventParticipation"
	// EventParticipantServiceChangeStatusProcedure is the fully-qualified name of the
	// EventParticipantService's ChangeStatus RPC.
	EventParticipantServiceChangeStatusProcedure = "/api.events.v1.EventParticipantService/ChangeStatus"
	// EventParticipantServiceGetAllEventParticipationProcedure is the fully-qualified name of the
	// EventParticipantService's GetAllEventParticipation RPC.
	EventParticipantServiceGetAllEventParticipationProcedure = "/api.events.v1.EventParticipantService/GetAllEventParticipation"
	// EventParticipantServiceGetCreatorEventParticipationProcedure is the fully-qualified name of the
	// EventParticipantService's GetCreatorEventParticipation RPC.
	EventParticipantServiceGetCreatorEventParticipationProcedure = "/api.events.v1.EventParticipantService/GetCreatorEventParticipation"
	// EventParticipantServiceGetAllEventParticipantsByEventIdProcedure is the fully-qualified name of
	// the EventParticipantService's GetAllEventParticipantsByEventId RPC.
	EventParticipantServiceGetAllEventParticipantsByEventIdProcedure = "/api.events.v1.EventParticipantService/GetAllEventParticipantsByEventId"
	// EventParticipantServiceGetAllEventParticipationOfVtuberProcedure is the fully-qualified name of
	// the EventParticipantService's GetAllEventParticipationOfVtuber RPC.
	EventParticipantServiceGetAllEventParticipationOfVtuberProcedure = "/api.events.v1.EventParticipantService/GetAllEventParticipationOfVtuber"
	// EventParticipantServiceGetTopTenEventParticipantsVtubersProcedure is the fully-qualified name of
	// the EventParticipantService's GetTopTenEventParticipantsVtubers RPC.
	EventParticipantServiceGetTopTenEventParticipantsVtubersProcedure = "/api.events.v1.EventParticipantService/GetTopTenEventParticipantsVtubers"
)

// EventParticipantServiceClient is a client for the api.events.v1.EventParticipantService service.
type EventParticipantServiceClient interface {
	AddEventParticipation(context.Context, *connect.Request[v1.AddEventParticipationRequest]) (*connect.Response[v1.AddEventParticipationResponse], error)
	GetEventParticipantsByEventId(context.Context, *connect.Request[v1.GetEventParticipantsByEventIdRequest]) (*connect.Response[v1.GetEventParticipantsByEventIdResponse], error)
	GetMyEventParticipation(context.Context, *connect.Request[v1.GetMyEventParticipationRequest]) (*connect.Response[v1.GetMyEventParticipationResponse], error)
	ChangeStatus(context.Context, *connect.Request[v1.ChangeStatusRequest]) (*connect.Response[v1.ChangeStatusResponse], error)
	GetAllEventParticipation(context.Context, *connect.Request[v1.GetAllEventParticipationRequest]) (*connect.Response[v1.GetAllEventParticipationResponse], error)
	GetCreatorEventParticipation(context.Context, *connect.Request[v1.GetCreatorEventParticipationRequest]) (*connect.Response[v1.GetCreatorEventParticipationResponse], error)
	GetAllEventParticipantsByEventId(context.Context, *connect.Request[v1.GetEventParticipantsByEventIdRequest]) (*connect.Response[v1.GetEventParticipantsByEventIdResponse], error)
	GetAllEventParticipationOfVtuber(context.Context, *connect.Request[v1.GetEventParticipationOfVtuberRequest]) (*connect.Response[v1.GetAllEventParticipationResponse], error)
	GetTopTenEventParticipantsVtubers(context.Context, *connect.Request[v1.GetTopTenEventParticipantsVtuberRequest]) (*connect.Response[v1.GetTopTenEventParticipantsVtuberResponse], error)
}

// NewEventParticipantServiceClient constructs a client for the
// api.events.v1.EventParticipantService service. By default, it uses the Connect protocol with the
// binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To use the
// gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewEventParticipantServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) EventParticipantServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	eventParticipantServiceMethods := v1.File_events_v1_eventparticipant_proto.Services().ByName("EventParticipantService").Methods()
	return &eventParticipantServiceClient{
		addEventParticipation: connect.NewClient[v1.AddEventParticipationRequest, v1.AddEventParticipationResponse](
			httpClient,
			baseURL+EventParticipantServiceAddEventParticipationProcedure,
			connect.WithSchema(eventParticipantServiceMethods.ByName("AddEventParticipation")),
			connect.WithClientOptions(opts...),
		),
		getEventParticipantsByEventId: connect.NewClient[v1.GetEventParticipantsByEventIdRequest, v1.GetEventParticipantsByEventIdResponse](
			httpClient,
			baseURL+EventParticipantServiceGetEventParticipantsByEventIdProcedure,
			connect.WithSchema(eventParticipantServiceMethods.ByName("GetEventParticipantsByEventId")),
			connect.WithClientOptions(opts...),
		),
		getMyEventParticipation: connect.NewClient[v1.GetMyEventParticipationRequest, v1.GetMyEventParticipationResponse](
			httpClient,
			baseURL+EventParticipantServiceGetMyEventParticipationProcedure,
			connect.WithSchema(eventParticipantServiceMethods.ByName("GetMyEventParticipation")),
			connect.WithClientOptions(opts...),
		),
		changeStatus: connect.NewClient[v1.ChangeStatusRequest, v1.ChangeStatusResponse](
			httpClient,
			baseURL+EventParticipantServiceChangeStatusProcedure,
			connect.WithSchema(eventParticipantServiceMethods.ByName("ChangeStatus")),
			connect.WithClientOptions(opts...),
		),
		getAllEventParticipation: connect.NewClient[v1.GetAllEventParticipationRequest, v1.GetAllEventParticipationResponse](
			httpClient,
			baseURL+EventParticipantServiceGetAllEventParticipationProcedure,
			connect.WithSchema(eventParticipantServiceMethods.ByName("GetAllEventParticipation")),
			connect.WithClientOptions(opts...),
		),
		getCreatorEventParticipation: connect.NewClient[v1.GetCreatorEventParticipationRequest, v1.GetCreatorEventParticipationResponse](
			httpClient,
			baseURL+EventParticipantServiceGetCreatorEventParticipationProcedure,
			connect.WithSchema(eventParticipantServiceMethods.ByName("GetCreatorEventParticipation")),
			connect.WithClientOptions(opts...),
		),
		getAllEventParticipantsByEventId: connect.NewClient[v1.GetEventParticipantsByEventIdRequest, v1.GetEventParticipantsByEventIdResponse](
			httpClient,
			baseURL+EventParticipantServiceGetAllEventParticipantsByEventIdProcedure,
			connect.WithSchema(eventParticipantServiceMethods.ByName("GetAllEventParticipantsByEventId")),
			connect.WithClientOptions(opts...),
		),
		getAllEventParticipationOfVtuber: connect.NewClient[v1.GetEventParticipationOfVtuberRequest, v1.GetAllEventParticipationResponse](
			httpClient,
			baseURL+EventParticipantServiceGetAllEventParticipationOfVtuberProcedure,
			connect.WithSchema(eventParticipantServiceMethods.ByName("GetAllEventParticipationOfVtuber")),
			connect.WithClientOptions(opts...),
		),
		getTopTenEventParticipantsVtubers: connect.NewClient[v1.GetTopTenEventParticipantsVtuberRequest, v1.GetTopTenEventParticipantsVtuberResponse](
			httpClient,
			baseURL+EventParticipantServiceGetTopTenEventParticipantsVtubersProcedure,
			connect.WithSchema(eventParticipantServiceMethods.ByName("GetTopTenEventParticipantsVtubers")),
			connect.WithClientOptions(opts...),
		),
	}
}

// eventParticipantServiceClient implements EventParticipantServiceClient.
type eventParticipantServiceClient struct {
	addEventParticipation             *connect.Client[v1.AddEventParticipationRequest, v1.AddEventParticipationResponse]
	getEventParticipantsByEventId     *connect.Client[v1.GetEventParticipantsByEventIdRequest, v1.GetEventParticipantsByEventIdResponse]
	getMyEventParticipation           *connect.Client[v1.GetMyEventParticipationRequest, v1.GetMyEventParticipationResponse]
	changeStatus                      *connect.Client[v1.ChangeStatusRequest, v1.ChangeStatusResponse]
	getAllEventParticipation          *connect.Client[v1.GetAllEventParticipationRequest, v1.GetAllEventParticipationResponse]
	getCreatorEventParticipation      *connect.Client[v1.GetCreatorEventParticipationRequest, v1.GetCreatorEventParticipationResponse]
	getAllEventParticipantsByEventId  *connect.Client[v1.GetEventParticipantsByEventIdRequest, v1.GetEventParticipantsByEventIdResponse]
	getAllEventParticipationOfVtuber  *connect.Client[v1.GetEventParticipationOfVtuberRequest, v1.GetAllEventParticipationResponse]
	getTopTenEventParticipantsVtubers *connect.Client[v1.GetTopTenEventParticipantsVtuberRequest, v1.GetTopTenEventParticipantsVtuberResponse]
}

// AddEventParticipation calls api.events.v1.EventParticipantService.AddEventParticipation.
func (c *eventParticipantServiceClient) AddEventParticipation(ctx context.Context, req *connect.Request[v1.AddEventParticipationRequest]) (*connect.Response[v1.AddEventParticipationResponse], error) {
	return c.addEventParticipation.CallUnary(ctx, req)
}

// GetEventParticipantsByEventId calls
// api.events.v1.EventParticipantService.GetEventParticipantsByEventId.
func (c *eventParticipantServiceClient) GetEventParticipantsByEventId(ctx context.Context, req *connect.Request[v1.GetEventParticipantsByEventIdRequest]) (*connect.Response[v1.GetEventParticipantsByEventIdResponse], error) {
	return c.getEventParticipantsByEventId.CallUnary(ctx, req)
}

// GetMyEventParticipation calls api.events.v1.EventParticipantService.GetMyEventParticipation.
func (c *eventParticipantServiceClient) GetMyEventParticipation(ctx context.Context, req *connect.Request[v1.GetMyEventParticipationRequest]) (*connect.Response[v1.GetMyEventParticipationResponse], error) {
	return c.getMyEventParticipation.CallUnary(ctx, req)
}

// ChangeStatus calls api.events.v1.EventParticipantService.ChangeStatus.
func (c *eventParticipantServiceClient) ChangeStatus(ctx context.Context, req *connect.Request[v1.ChangeStatusRequest]) (*connect.Response[v1.ChangeStatusResponse], error) {
	return c.changeStatus.CallUnary(ctx, req)
}

// GetAllEventParticipation calls api.events.v1.EventParticipantService.GetAllEventParticipation.
func (c *eventParticipantServiceClient) GetAllEventParticipation(ctx context.Context, req *connect.Request[v1.GetAllEventParticipationRequest]) (*connect.Response[v1.GetAllEventParticipationResponse], error) {
	return c.getAllEventParticipation.CallUnary(ctx, req)
}

// GetCreatorEventParticipation calls
// api.events.v1.EventParticipantService.GetCreatorEventParticipation.
func (c *eventParticipantServiceClient) GetCreatorEventParticipation(ctx context.Context, req *connect.Request[v1.GetCreatorEventParticipationRequest]) (*connect.Response[v1.GetCreatorEventParticipationResponse], error) {
	return c.getCreatorEventParticipation.CallUnary(ctx, req)
}

// GetAllEventParticipantsByEventId calls
// api.events.v1.EventParticipantService.GetAllEventParticipantsByEventId.
func (c *eventParticipantServiceClient) GetAllEventParticipantsByEventId(ctx context.Context, req *connect.Request[v1.GetEventParticipantsByEventIdRequest]) (*connect.Response[v1.GetEventParticipantsByEventIdResponse], error) {
	return c.getAllEventParticipantsByEventId.CallUnary(ctx, req)
}

// GetAllEventParticipationOfVtuber calls
// api.events.v1.EventParticipantService.GetAllEventParticipationOfVtuber.
func (c *eventParticipantServiceClient) GetAllEventParticipationOfVtuber(ctx context.Context, req *connect.Request[v1.GetEventParticipationOfVtuberRequest]) (*connect.Response[v1.GetAllEventParticipationResponse], error) {
	return c.getAllEventParticipationOfVtuber.CallUnary(ctx, req)
}

// GetTopTenEventParticipantsVtubers calls
// api.events.v1.EventParticipantService.GetTopTenEventParticipantsVtubers.
func (c *eventParticipantServiceClient) GetTopTenEventParticipantsVtubers(ctx context.Context, req *connect.Request[v1.GetTopTenEventParticipantsVtuberRequest]) (*connect.Response[v1.GetTopTenEventParticipantsVtuberResponse], error) {
	return c.getTopTenEventParticipantsVtubers.CallUnary(ctx, req)
}

// EventParticipantServiceHandler is an implementation of the api.events.v1.EventParticipantService
// service.
type EventParticipantServiceHandler interface {
	AddEventParticipation(context.Context, *connect.Request[v1.AddEventParticipationRequest]) (*connect.Response[v1.AddEventParticipationResponse], error)
	GetEventParticipantsByEventId(context.Context, *connect.Request[v1.GetEventParticipantsByEventIdRequest]) (*connect.Response[v1.GetEventParticipantsByEventIdResponse], error)
	GetMyEventParticipation(context.Context, *connect.Request[v1.GetMyEventParticipationRequest]) (*connect.Response[v1.GetMyEventParticipationResponse], error)
	ChangeStatus(context.Context, *connect.Request[v1.ChangeStatusRequest]) (*connect.Response[v1.ChangeStatusResponse], error)
	GetAllEventParticipation(context.Context, *connect.Request[v1.GetAllEventParticipationRequest]) (*connect.Response[v1.GetAllEventParticipationResponse], error)
	GetCreatorEventParticipation(context.Context, *connect.Request[v1.GetCreatorEventParticipationRequest]) (*connect.Response[v1.GetCreatorEventParticipationResponse], error)
	GetAllEventParticipantsByEventId(context.Context, *connect.Request[v1.GetEventParticipantsByEventIdRequest]) (*connect.Response[v1.GetEventParticipantsByEventIdResponse], error)
	GetAllEventParticipationOfVtuber(context.Context, *connect.Request[v1.GetEventParticipationOfVtuberRequest]) (*connect.Response[v1.GetAllEventParticipationResponse], error)
	GetTopTenEventParticipantsVtubers(context.Context, *connect.Request[v1.GetTopTenEventParticipantsVtuberRequest]) (*connect.Response[v1.GetTopTenEventParticipantsVtuberResponse], error)
}

// NewEventParticipantServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewEventParticipantServiceHandler(svc EventParticipantServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	eventParticipantServiceMethods := v1.File_events_v1_eventparticipant_proto.Services().ByName("EventParticipantService").Methods()
	eventParticipantServiceAddEventParticipationHandler := connect.NewUnaryHandler(
		EventParticipantServiceAddEventParticipationProcedure,
		svc.AddEventParticipation,
		connect.WithSchema(eventParticipantServiceMethods.ByName("AddEventParticipation")),
		connect.WithHandlerOptions(opts...),
	)
	eventParticipantServiceGetEventParticipantsByEventIdHandler := connect.NewUnaryHandler(
		EventParticipantServiceGetEventParticipantsByEventIdProcedure,
		svc.GetEventParticipantsByEventId,
		connect.WithSchema(eventParticipantServiceMethods.ByName("GetEventParticipantsByEventId")),
		connect.WithHandlerOptions(opts...),
	)
	eventParticipantServiceGetMyEventParticipationHandler := connect.NewUnaryHandler(
		EventParticipantServiceGetMyEventParticipationProcedure,
		svc.GetMyEventParticipation,
		connect.WithSchema(eventParticipantServiceMethods.ByName("GetMyEventParticipation")),
		connect.WithHandlerOptions(opts...),
	)
	eventParticipantServiceChangeStatusHandler := connect.NewUnaryHandler(
		EventParticipantServiceChangeStatusProcedure,
		svc.ChangeStatus,
		connect.WithSchema(eventParticipantServiceMethods.ByName("ChangeStatus")),
		connect.WithHandlerOptions(opts...),
	)
	eventParticipantServiceGetAllEventParticipationHandler := connect.NewUnaryHandler(
		EventParticipantServiceGetAllEventParticipationProcedure,
		svc.GetAllEventParticipation,
		connect.WithSchema(eventParticipantServiceMethods.ByName("GetAllEventParticipation")),
		connect.WithHandlerOptions(opts...),
	)
	eventParticipantServiceGetCreatorEventParticipationHandler := connect.NewUnaryHandler(
		EventParticipantServiceGetCreatorEventParticipationProcedure,
		svc.GetCreatorEventParticipation,
		connect.WithSchema(eventParticipantServiceMethods.ByName("GetCreatorEventParticipation")),
		connect.WithHandlerOptions(opts...),
	)
	eventParticipantServiceGetAllEventParticipantsByEventIdHandler := connect.NewUnaryHandler(
		EventParticipantServiceGetAllEventParticipantsByEventIdProcedure,
		svc.GetAllEventParticipantsByEventId,
		connect.WithSchema(eventParticipantServiceMethods.ByName("GetAllEventParticipantsByEventId")),
		connect.WithHandlerOptions(opts...),
	)
	eventParticipantServiceGetAllEventParticipationOfVtuberHandler := connect.NewUnaryHandler(
		EventParticipantServiceGetAllEventParticipationOfVtuberProcedure,
		svc.GetAllEventParticipationOfVtuber,
		connect.WithSchema(eventParticipantServiceMethods.ByName("GetAllEventParticipationOfVtuber")),
		connect.WithHandlerOptions(opts...),
	)
	eventParticipantServiceGetTopTenEventParticipantsVtubersHandler := connect.NewUnaryHandler(
		EventParticipantServiceGetTopTenEventParticipantsVtubersProcedure,
		svc.GetTopTenEventParticipantsVtubers,
		connect.WithSchema(eventParticipantServiceMethods.ByName("GetTopTenEventParticipantsVtubers")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.events.v1.EventParticipantService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case EventParticipantServiceAddEventParticipationProcedure:
			eventParticipantServiceAddEventParticipationHandler.ServeHTTP(w, r)
		case EventParticipantServiceGetEventParticipantsByEventIdProcedure:
			eventParticipantServiceGetEventParticipantsByEventIdHandler.ServeHTTP(w, r)
		case EventParticipantServiceGetMyEventParticipationProcedure:
			eventParticipantServiceGetMyEventParticipationHandler.ServeHTTP(w, r)
		case EventParticipantServiceChangeStatusProcedure:
			eventParticipantServiceChangeStatusHandler.ServeHTTP(w, r)
		case EventParticipantServiceGetAllEventParticipationProcedure:
			eventParticipantServiceGetAllEventParticipationHandler.ServeHTTP(w, r)
		case EventParticipantServiceGetCreatorEventParticipationProcedure:
			eventParticipantServiceGetCreatorEventParticipationHandler.ServeHTTP(w, r)
		case EventParticipantServiceGetAllEventParticipantsByEventIdProcedure:
			eventParticipantServiceGetAllEventParticipantsByEventIdHandler.ServeHTTP(w, r)
		case EventParticipantServiceGetAllEventParticipationOfVtuberProcedure:
			eventParticipantServiceGetAllEventParticipationOfVtuberHandler.ServeHTTP(w, r)
		case EventParticipantServiceGetTopTenEventParticipantsVtubersProcedure:
			eventParticipantServiceGetTopTenEventParticipantsVtubersHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedEventParticipantServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedEventParticipantServiceHandler struct{}

func (UnimplementedEventParticipantServiceHandler) AddEventParticipation(context.Context, *connect.Request[v1.AddEventParticipationRequest]) (*connect.Response[v1.AddEventParticipationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventParticipantService.AddEventParticipation is not implemented"))
}

func (UnimplementedEventParticipantServiceHandler) GetEventParticipantsByEventId(context.Context, *connect.Request[v1.GetEventParticipantsByEventIdRequest]) (*connect.Response[v1.GetEventParticipantsByEventIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventParticipantService.GetEventParticipantsByEventId is not implemented"))
}

func (UnimplementedEventParticipantServiceHandler) GetMyEventParticipation(context.Context, *connect.Request[v1.GetMyEventParticipationRequest]) (*connect.Response[v1.GetMyEventParticipationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventParticipantService.GetMyEventParticipation is not implemented"))
}

func (UnimplementedEventParticipantServiceHandler) ChangeStatus(context.Context, *connect.Request[v1.ChangeStatusRequest]) (*connect.Response[v1.ChangeStatusResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventParticipantService.ChangeStatus is not implemented"))
}

func (UnimplementedEventParticipantServiceHandler) GetAllEventParticipation(context.Context, *connect.Request[v1.GetAllEventParticipationRequest]) (*connect.Response[v1.GetAllEventParticipationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventParticipantService.GetAllEventParticipation is not implemented"))
}

func (UnimplementedEventParticipantServiceHandler) GetCreatorEventParticipation(context.Context, *connect.Request[v1.GetCreatorEventParticipationRequest]) (*connect.Response[v1.GetCreatorEventParticipationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventParticipantService.GetCreatorEventParticipation is not implemented"))
}

func (UnimplementedEventParticipantServiceHandler) GetAllEventParticipantsByEventId(context.Context, *connect.Request[v1.GetEventParticipantsByEventIdRequest]) (*connect.Response[v1.GetEventParticipantsByEventIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventParticipantService.GetAllEventParticipantsByEventId is not implemented"))
}

func (UnimplementedEventParticipantServiceHandler) GetAllEventParticipationOfVtuber(context.Context, *connect.Request[v1.GetEventParticipationOfVtuberRequest]) (*connect.Response[v1.GetAllEventParticipationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventParticipantService.GetAllEventParticipationOfVtuber is not implemented"))
}

func (UnimplementedEventParticipantServiceHandler) GetTopTenEventParticipantsVtubers(context.Context, *connect.Request[v1.GetTopTenEventParticipantsVtuberRequest]) (*connect.Response[v1.GetTopTenEventParticipantsVtuberResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.events.v1.EventParticipantService.GetTopTenEventParticipantsVtubers is not implemented"))
}
