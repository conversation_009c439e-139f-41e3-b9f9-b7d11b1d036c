// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: events/v1/eventcomment.proto

package eventsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddEventCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty" validate:"required"`
	EventId       string                 `protobuf:"bytes,2,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty" validate:"required"`
	AsVtuber      *bool                  `protobuf:"varint,3,opt,name=as_vtuber,json=asVtuber,proto3,oneof" json:"as_vtuber,omitempty"`
	ParentId      *string                `protobuf:"bytes,4,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddEventCommentRequest) Reset() {
	*x = AddEventCommentRequest{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddEventCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEventCommentRequest) ProtoMessage() {}

func (x *AddEventCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEventCommentRequest.ProtoReflect.Descriptor instead.
func (*AddEventCommentRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{0}
}

func (x *AddEventCommentRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AddEventCommentRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *AddEventCommentRequest) GetAsVtuber() bool {
	if x != nil && x.AsVtuber != nil {
		return *x.AsVtuber
	}
	return false
}

func (x *AddEventCommentRequest) GetParentId() string {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return ""
}

type AddEventCommentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *EventComment          `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddEventCommentResponse) Reset() {
	*x = AddEventCommentResponse{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddEventCommentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEventCommentResponse) ProtoMessage() {}

func (x *AddEventCommentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEventCommentResponse.ProtoReflect.Descriptor instead.
func (*AddEventCommentResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{1}
}

func (x *AddEventCommentResponse) GetData() *EventComment {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetAllEventCommentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EventId       string                 `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllEventCommentsRequest) Reset() {
	*x = GetAllEventCommentsRequest{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllEventCommentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllEventCommentsRequest) ProtoMessage() {}

func (x *GetAllEventCommentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllEventCommentsRequest.ProtoReflect.Descriptor instead.
func (*GetAllEventCommentsRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{2}
}

func (x *GetAllEventCommentsRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *GetAllEventCommentsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetAllEventCommentsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*EventComment        `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllEventCommentsResponse) Reset() {
	*x = GetAllEventCommentsResponse{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllEventCommentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllEventCommentsResponse) ProtoMessage() {}

func (x *GetAllEventCommentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllEventCommentsResponse.ProtoReflect.Descriptor instead.
func (*GetAllEventCommentsResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllEventCommentsResponse) GetData() []*EventComment {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllEventCommentsResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type EventComment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	EventId       string                 `protobuf:"bytes,3,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	ParentId      *string                `protobuf:"bytes,5,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	User          *v1.Profile            `protobuf:"bytes,7,opt,name=user,proto3" json:"user,omitempty"`
	Vtuber        *v1.Profile            `protobuf:"bytes,8,opt,name=vtuber,proto3,oneof" json:"vtuber,omitempty"`
	HasReply      bool                   `protobuf:"varint,9,opt,name=has_reply,json=hasReply,proto3" json:"has_reply,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventComment) Reset() {
	*x = EventComment{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventComment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventComment) ProtoMessage() {}

func (x *EventComment) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventComment.ProtoReflect.Descriptor instead.
func (*EventComment) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{4}
}

func (x *EventComment) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EventComment) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *EventComment) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *EventComment) GetParentId() string {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return ""
}

func (x *EventComment) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *EventComment) GetUser() *v1.Profile {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *EventComment) GetVtuber() *v1.Profile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

func (x *EventComment) GetHasReply() bool {
	if x != nil {
		return x.HasReply
	}
	return false
}

type GetEventCommentByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEventCommentByIdRequest) Reset() {
	*x = GetEventCommentByIdRequest{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEventCommentByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEventCommentByIdRequest) ProtoMessage() {}

func (x *GetEventCommentByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEventCommentByIdRequest.ProtoReflect.Descriptor instead.
func (*GetEventCommentByIdRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{5}
}

func (x *GetEventCommentByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetEventCommentByIdRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetEventCommentByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *EventComment          `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEventCommentByIdResponse) Reset() {
	*x = GetEventCommentByIdResponse{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEventCommentByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEventCommentByIdResponse) ProtoMessage() {}

func (x *GetEventCommentByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEventCommentByIdResponse.ProtoReflect.Descriptor instead.
func (*GetEventCommentByIdResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{6}
}

func (x *GetEventCommentByIdResponse) GetData() *EventComment {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteEventCommentByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteEventCommentByIdRequest) Reset() {
	*x = DeleteEventCommentByIdRequest{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteEventCommentByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEventCommentByIdRequest) ProtoMessage() {}

func (x *DeleteEventCommentByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEventCommentByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteEventCommentByIdRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteEventCommentByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateEventCommentByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty" validate:"required"`
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateEventCommentByIdRequest) Reset() {
	*x = UpdateEventCommentByIdRequest{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEventCommentByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEventCommentByIdRequest) ProtoMessage() {}

func (x *UpdateEventCommentByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEventCommentByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateEventCommentByIdRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateEventCommentByIdRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UpdateEventCommentByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateEventCommentByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateEventCommentByIdResponse) Reset() {
	*x = UpdateEventCommentByIdResponse{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEventCommentByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEventCommentByIdResponse) ProtoMessage() {}

func (x *UpdateEventCommentByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEventCommentByIdResponse.ProtoReflect.Descriptor instead.
func (*UpdateEventCommentByIdResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateEventCommentByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateEventCommentByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteEventCommentByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteEventCommentByIdResponse) Reset() {
	*x = DeleteEventCommentByIdResponse{}
	mi := &file_events_v1_eventcomment_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteEventCommentByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEventCommentByIdResponse) ProtoMessage() {}

func (x *DeleteEventCommentByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventcomment_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEventCommentByIdResponse.ProtoReflect.Descriptor instead.
func (*DeleteEventCommentByIdResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventcomment_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteEventCommentByIdResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteEventCommentByIdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_events_v1_eventcomment_proto protoreflect.FileDescriptor

const file_events_v1_eventcomment_proto_rawDesc = "" +
	"\n" +
	"\x1cevents/v1/eventcomment.proto\x12\rapi.events.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\xad\x01\n" +
	"\x16AddEventCommentRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x19\n" +
	"\bevent_id\x18\x02 \x01(\tR\aeventId\x12 \n" +
	"\tas_vtuber\x18\x03 \x01(\bH\x00R\basVtuber\x88\x01\x01\x12 \n" +
	"\tparent_id\x18\x04 \x01(\tH\x01R\bparentId\x88\x01\x01B\f\n" +
	"\n" +
	"_as_vtuberB\f\n" +
	"\n" +
	"_parent_id\"J\n" +
	"\x17AddEventCommentResponse\x12/\n" +
	"\x04data\x18\x01 \x01(\v2\x1b.api.events.v1.EventCommentR\x04data\"\x8d\x01\n" +
	"\x1aGetAllEventCommentsRequest\x12\x19\n" +
	"\bevent_id\x18\x01 \x01(\tR\aeventId\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\x9f\x01\n" +
	"\x1bGetAllEventCommentsResponse\x12/\n" +
	"\x04data\x18\x01 \x03(\v2\x1b.api.events.v1.EventCommentR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\xc7\x02\n" +
	"\fEventComment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x19\n" +
	"\bevent_id\x18\x03 \x01(\tR\aeventId\x12 \n" +
	"\tparent_id\x18\x05 \x01(\tH\x00R\bparentId\x88\x01\x01\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12*\n" +
	"\x04user\x18\a \x01(\v2\x16.api.shared.v1.ProfileR\x04user\x123\n" +
	"\x06vtuber\x18\b \x01(\v2\x16.api.shared.v1.ProfileH\x01R\x06vtuber\x88\x01\x01\x12\x1b\n" +
	"\thas_reply\x18\t \x01(\bR\bhasReplyB\f\n" +
	"\n" +
	"_parent_idB\t\n" +
	"\a_vtuber\"\x82\x01\n" +
	"\x1aGetEventCommentByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"N\n" +
	"\x1bGetEventCommentByIdResponse\x12/\n" +
	"\x04data\x18\x01 \x01(\v2\x1b.api.events.v1.EventCommentR\x04data\"/\n" +
	"\x1dDeleteEventCommentByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"I\n" +
	"\x1dUpdateEventCommentByIdRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\"T\n" +
	"\x1eUpdateEventCommentByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"T\n" +
	"\x1eDeleteEventCommentByIdResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\x81\x06\n" +
	"\x13EventCommentService\x12\x93\x01\n" +
	"\x0fAddEventComment\x12%.api.events.v1.AddEventCommentRequest\x1a&.api.events.v1.AddEventCommentResponse\"1\x82\xb5\x18-\b\x01\")as_vtuber==true?_user.vtuberId!=null:true\x12n\n" +
	"\x13GetAllEventComments\x12).api.events.v1.GetAllEventCommentsRequest\x1a*.api.events.v1.GetAllEventCommentsResponse\"\x00\x12v\n" +
	"\x1bGetAllRepliesOfEventComment\x12).api.events.v1.GetEventCommentByIdRequest\x1a*.api.events.v1.GetAllEventCommentsResponse\"\x00\x12n\n" +
	"\x13GetEventCommentById\x12).api.events.v1.GetEventCommentByIdRequest\x1a*.api.events.v1.GetEventCommentByIdResponse\"\x00\x12}\n" +
	"\x16DeleteEventCommentById\x12,.api.events.v1.DeleteEventCommentByIdRequest\x1a-.api.events.v1.DeleteEventCommentByIdResponse\"\x06\x82\xb5\x18\x02\b\x01\x12}\n" +
	"\x16UpdateEventCommentById\x12,.api.events.v1.UpdateEventCommentByIdRequest\x1a-.api.events.v1.UpdateEventCommentByIdResponse\"\x06\x82\xb5\x18\x02\b\x01B2Z0github.com/nsp-inc/vtuber/api/events/v1;eventsv1b\x06proto3"

var (
	file_events_v1_eventcomment_proto_rawDescOnce sync.Once
	file_events_v1_eventcomment_proto_rawDescData []byte
)

func file_events_v1_eventcomment_proto_rawDescGZIP() []byte {
	file_events_v1_eventcomment_proto_rawDescOnce.Do(func() {
		file_events_v1_eventcomment_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_events_v1_eventcomment_proto_rawDesc), len(file_events_v1_eventcomment_proto_rawDesc)))
	})
	return file_events_v1_eventcomment_proto_rawDescData
}

var file_events_v1_eventcomment_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_events_v1_eventcomment_proto_goTypes = []any{
	(*AddEventCommentRequest)(nil),         // 0: api.events.v1.AddEventCommentRequest
	(*AddEventCommentResponse)(nil),        // 1: api.events.v1.AddEventCommentResponse
	(*GetAllEventCommentsRequest)(nil),     // 2: api.events.v1.GetAllEventCommentsRequest
	(*GetAllEventCommentsResponse)(nil),    // 3: api.events.v1.GetAllEventCommentsResponse
	(*EventComment)(nil),                   // 4: api.events.v1.EventComment
	(*GetEventCommentByIdRequest)(nil),     // 5: api.events.v1.GetEventCommentByIdRequest
	(*GetEventCommentByIdResponse)(nil),    // 6: api.events.v1.GetEventCommentByIdResponse
	(*DeleteEventCommentByIdRequest)(nil),  // 7: api.events.v1.DeleteEventCommentByIdRequest
	(*UpdateEventCommentByIdRequest)(nil),  // 8: api.events.v1.UpdateEventCommentByIdRequest
	(*UpdateEventCommentByIdResponse)(nil), // 9: api.events.v1.UpdateEventCommentByIdResponse
	(*DeleteEventCommentByIdResponse)(nil), // 10: api.events.v1.DeleteEventCommentByIdResponse
	(*v1.PaginationRequest)(nil),           // 11: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),           // 12: api.shared.v1.PaginationDetails
	(*timestamppb.Timestamp)(nil),          // 13: google.protobuf.Timestamp
	(*v1.Profile)(nil),                     // 14: api.shared.v1.Profile
}
var file_events_v1_eventcomment_proto_depIdxs = []int32{
	4,  // 0: api.events.v1.AddEventCommentResponse.data:type_name -> api.events.v1.EventComment
	11, // 1: api.events.v1.GetAllEventCommentsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	4,  // 2: api.events.v1.GetAllEventCommentsResponse.data:type_name -> api.events.v1.EventComment
	12, // 3: api.events.v1.GetAllEventCommentsResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	13, // 4: api.events.v1.EventComment.created_at:type_name -> google.protobuf.Timestamp
	14, // 5: api.events.v1.EventComment.user:type_name -> api.shared.v1.Profile
	14, // 6: api.events.v1.EventComment.vtuber:type_name -> api.shared.v1.Profile
	11, // 7: api.events.v1.GetEventCommentByIdRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	4,  // 8: api.events.v1.GetEventCommentByIdResponse.data:type_name -> api.events.v1.EventComment
	0,  // 9: api.events.v1.EventCommentService.AddEventComment:input_type -> api.events.v1.AddEventCommentRequest
	2,  // 10: api.events.v1.EventCommentService.GetAllEventComments:input_type -> api.events.v1.GetAllEventCommentsRequest
	5,  // 11: api.events.v1.EventCommentService.GetAllRepliesOfEventComment:input_type -> api.events.v1.GetEventCommentByIdRequest
	5,  // 12: api.events.v1.EventCommentService.GetEventCommentById:input_type -> api.events.v1.GetEventCommentByIdRequest
	7,  // 13: api.events.v1.EventCommentService.DeleteEventCommentById:input_type -> api.events.v1.DeleteEventCommentByIdRequest
	8,  // 14: api.events.v1.EventCommentService.UpdateEventCommentById:input_type -> api.events.v1.UpdateEventCommentByIdRequest
	1,  // 15: api.events.v1.EventCommentService.AddEventComment:output_type -> api.events.v1.AddEventCommentResponse
	3,  // 16: api.events.v1.EventCommentService.GetAllEventComments:output_type -> api.events.v1.GetAllEventCommentsResponse
	3,  // 17: api.events.v1.EventCommentService.GetAllRepliesOfEventComment:output_type -> api.events.v1.GetAllEventCommentsResponse
	6,  // 18: api.events.v1.EventCommentService.GetEventCommentById:output_type -> api.events.v1.GetEventCommentByIdResponse
	10, // 19: api.events.v1.EventCommentService.DeleteEventCommentById:output_type -> api.events.v1.DeleteEventCommentByIdResponse
	9,  // 20: api.events.v1.EventCommentService.UpdateEventCommentById:output_type -> api.events.v1.UpdateEventCommentByIdResponse
	15, // [15:21] is the sub-list for method output_type
	9,  // [9:15] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_events_v1_eventcomment_proto_init() }
func file_events_v1_eventcomment_proto_init() {
	if File_events_v1_eventcomment_proto != nil {
		return
	}
	file_events_v1_eventcomment_proto_msgTypes[0].OneofWrappers = []any{}
	file_events_v1_eventcomment_proto_msgTypes[2].OneofWrappers = []any{}
	file_events_v1_eventcomment_proto_msgTypes[4].OneofWrappers = []any{}
	file_events_v1_eventcomment_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_events_v1_eventcomment_proto_rawDesc), len(file_events_v1_eventcomment_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_events_v1_eventcomment_proto_goTypes,
		DependencyIndexes: file_events_v1_eventcomment_proto_depIdxs,
		MessageInfos:      file_events_v1_eventcomment_proto_msgTypes,
	}.Build()
	File_events_v1_eventcomment_proto = out.File
	file_events_v1_eventcomment_proto_goTypes = nil
	file_events_v1_eventcomment_proto_depIdxs = nil
}
