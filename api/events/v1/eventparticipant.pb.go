// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: events/v1/eventparticipant.proto

package eventsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EventParticipation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	EventId       string                 `protobuf:"bytes,2,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	VtuberId      string                 `protobuf:"bytes,3,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	Status        string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty" validate:"required,oneof=approved rejected"`
	Vtuber        *v1.Profile            `protobuf:"bytes,5,opt,name=vtuber,proto3,oneof" json:"vtuber,omitempty"`
	Event         *Event                 `protobuf:"bytes,6,opt,name=event,proto3,oneof" json:"event,omitempty"`
	VoteCount     int64                  `protobuf:"varint,7,opt,name=vote_count,json=voteCount,proto3" json:"vote_count,omitempty"`
	Remarks       *string                `protobuf:"bytes,8,opt,name=remarks,proto3,oneof" json:"remarks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventParticipation) Reset() {
	*x = EventParticipation{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventParticipation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventParticipation) ProtoMessage() {}

func (x *EventParticipation) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventParticipation.ProtoReflect.Descriptor instead.
func (*EventParticipation) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{0}
}

func (x *EventParticipation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EventParticipation) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *EventParticipation) GetVtuberId() string {
	if x != nil {
		return x.VtuberId
	}
	return ""
}

func (x *EventParticipation) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *EventParticipation) GetVtuber() *v1.Profile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

func (x *EventParticipation) GetEvent() *Event {
	if x != nil {
		return x.Event
	}
	return nil
}

func (x *EventParticipation) GetVoteCount() int64 {
	if x != nil {
		return x.VoteCount
	}
	return 0
}

func (x *EventParticipation) GetRemarks() string {
	if x != nil && x.Remarks != nil {
		return *x.Remarks
	}
	return ""
}

type AddEventParticipationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EventId       string                 `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddEventParticipationRequest) Reset() {
	*x = AddEventParticipationRequest{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddEventParticipationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEventParticipationRequest) ProtoMessage() {}

func (x *AddEventParticipationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEventParticipationRequest.ProtoReflect.Descriptor instead.
func (*AddEventParticipationRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{1}
}

func (x *AddEventParticipationRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

type GetCreatorEventParticipationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCreatorEventParticipationRequest) Reset() {
	*x = GetCreatorEventParticipationRequest{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCreatorEventParticipationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreatorEventParticipationRequest) ProtoMessage() {}

func (x *GetCreatorEventParticipationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreatorEventParticipationRequest.ProtoReflect.Descriptor instead.
func (*GetCreatorEventParticipationRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{2}
}

type GetCreatorEventParticipationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Events        []string               `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCreatorEventParticipationResponse) Reset() {
	*x = GetCreatorEventParticipationResponse{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCreatorEventParticipationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreatorEventParticipationResponse) ProtoMessage() {}

func (x *GetCreatorEventParticipationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreatorEventParticipationResponse.ProtoReflect.Descriptor instead.
func (*GetCreatorEventParticipationResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{3}
}

func (x *GetCreatorEventParticipationResponse) GetEvents() []string {
	if x != nil {
		return x.Events
	}
	return nil
}

type GetEventParticipationOfVtuberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VtuberId      string                 `protobuf:"bytes,1,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEventParticipationOfVtuberRequest) Reset() {
	*x = GetEventParticipationOfVtuberRequest{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEventParticipationOfVtuberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEventParticipationOfVtuberRequest) ProtoMessage() {}

func (x *GetEventParticipationOfVtuberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEventParticipationOfVtuberRequest.ProtoReflect.Descriptor instead.
func (*GetEventParticipationOfVtuberRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{4}
}

func (x *GetEventParticipationOfVtuberRequest) GetVtuberId() string {
	if x != nil {
		return x.VtuberId
	}
	return ""
}

func (x *GetEventParticipationOfVtuberRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetEventParticipantsByEventIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EventId       string                 `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty" validate:"required"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEventParticipantsByEventIdRequest) Reset() {
	*x = GetEventParticipantsByEventIdRequest{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEventParticipantsByEventIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEventParticipantsByEventIdRequest) ProtoMessage() {}

func (x *GetEventParticipantsByEventIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEventParticipantsByEventIdRequest.ProtoReflect.Descriptor instead.
func (*GetEventParticipantsByEventIdRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{5}
}

func (x *GetEventParticipantsByEventIdRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *GetEventParticipantsByEventIdRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetEventParticipantsByEventIdResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*EventParticipation  `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetEventParticipantsByEventIdResponse) Reset() {
	*x = GetEventParticipantsByEventIdResponse{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEventParticipantsByEventIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEventParticipantsByEventIdResponse) ProtoMessage() {}

func (x *GetEventParticipantsByEventIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEventParticipantsByEventIdResponse.ProtoReflect.Descriptor instead.
func (*GetEventParticipantsByEventIdResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{6}
}

func (x *GetEventParticipantsByEventIdResponse) GetData() []*EventParticipation {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetEventParticipantsByEventIdResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetMyEventParticipationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyEventParticipationRequest) Reset() {
	*x = GetMyEventParticipationRequest{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyEventParticipationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyEventParticipationRequest) ProtoMessage() {}

func (x *GetMyEventParticipationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyEventParticipationRequest.ProtoReflect.Descriptor instead.
func (*GetMyEventParticipationRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{7}
}

func (x *GetMyEventParticipationRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetMyEventParticipationResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*EventParticipation  `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetMyEventParticipationResponse) Reset() {
	*x = GetMyEventParticipationResponse{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyEventParticipationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyEventParticipationResponse) ProtoMessage() {}

func (x *GetMyEventParticipationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyEventParticipationResponse.ProtoReflect.Descriptor instead.
func (*GetMyEventParticipationResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{8}
}

func (x *GetMyEventParticipationResponse) GetData() []*EventParticipation {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetMyEventParticipationResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type ChangeStatusRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	EventParticipationId string                 `protobuf:"bytes,1,opt,name=event_participation_id,json=eventParticipationId,proto3" json:"event_participation_id,omitempty" validate:"required"`
	Status               string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty" validate:"required,oneof=approved rejected"`
	Reason               *string                `protobuf:"bytes,3,opt,name=reason,proto3,oneof" json:"reason,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ChangeStatusRequest) Reset() {
	*x = ChangeStatusRequest{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeStatusRequest) ProtoMessage() {}

func (x *ChangeStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeStatusRequest.ProtoReflect.Descriptor instead.
func (*ChangeStatusRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{9}
}

func (x *ChangeStatusRequest) GetEventParticipationId() string {
	if x != nil {
		return x.EventParticipationId
	}
	return ""
}

func (x *ChangeStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ChangeStatusRequest) GetReason() string {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return ""
}

type GetAllEventParticipationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllEventParticipationRequest) Reset() {
	*x = GetAllEventParticipationRequest{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllEventParticipationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllEventParticipationRequest) ProtoMessage() {}

func (x *GetAllEventParticipationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllEventParticipationRequest.ProtoReflect.Descriptor instead.
func (*GetAllEventParticipationRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{10}
}

func (x *GetAllEventParticipationRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetAllEventParticipationResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*EventParticipation  `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v1.PaginationDetails  `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllEventParticipationResponse) Reset() {
	*x = GetAllEventParticipationResponse{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllEventParticipationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllEventParticipationResponse) ProtoMessage() {}

func (x *GetAllEventParticipationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllEventParticipationResponse.ProtoReflect.Descriptor instead.
func (*GetAllEventParticipationResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{11}
}

func (x *GetAllEventParticipationResponse) GetData() []*EventParticipation {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllEventParticipationResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetTopTenEventParticipantsVtuberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EventId       string                 `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTopTenEventParticipantsVtuberRequest) Reset() {
	*x = GetTopTenEventParticipantsVtuberRequest{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTopTenEventParticipantsVtuberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopTenEventParticipantsVtuberRequest) ProtoMessage() {}

func (x *GetTopTenEventParticipantsVtuberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopTenEventParticipantsVtuberRequest.ProtoReflect.Descriptor instead.
func (*GetTopTenEventParticipantsVtuberRequest) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{12}
}

func (x *GetTopTenEventParticipantsVtuberRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

type GetTopTenEventParticipantsVtuberResponse struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Data          []*EventParticipantVtuber `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTopTenEventParticipantsVtuberResponse) Reset() {
	*x = GetTopTenEventParticipantsVtuberResponse{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTopTenEventParticipantsVtuberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopTenEventParticipantsVtuberResponse) ProtoMessage() {}

func (x *GetTopTenEventParticipantsVtuberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopTenEventParticipantsVtuberResponse.ProtoReflect.Descriptor instead.
func (*GetTopTenEventParticipantsVtuberResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{13}
}

func (x *GetTopTenEventParticipantsVtuberResponse) GetData() []*EventParticipantVtuber {
	if x != nil {
		return x.Data
	}
	return nil
}

type EventParticipantVtuber struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Image         *string                `protobuf:"bytes,2,opt,name=image,proto3,oneof" json:"image,omitempty"`
	VoteCount     int32                  `protobuf:"varint,3,opt,name=vote_count,json=voteCount,proto3" json:"vote_count,omitempty"`
	Id            string                 `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventParticipantVtuber) Reset() {
	*x = EventParticipantVtuber{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventParticipantVtuber) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventParticipantVtuber) ProtoMessage() {}

func (x *EventParticipantVtuber) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventParticipantVtuber.ProtoReflect.Descriptor instead.
func (*EventParticipantVtuber) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{14}
}

func (x *EventParticipantVtuber) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EventParticipantVtuber) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

func (x *EventParticipantVtuber) GetVoteCount() int32 {
	if x != nil {
		return x.VoteCount
	}
	return 0
}

func (x *EventParticipantVtuber) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type AddEventParticipationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddEventParticipationResponse) Reset() {
	*x = AddEventParticipationResponse{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddEventParticipationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEventParticipationResponse) ProtoMessage() {}

func (x *AddEventParticipationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEventParticipationResponse.ProtoReflect.Descriptor instead.
func (*AddEventParticipationResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{15}
}

func (x *AddEventParticipationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AddEventParticipationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ChangeStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeStatusResponse) Reset() {
	*x = ChangeStatusResponse{}
	mi := &file_events_v1_eventparticipant_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeStatusResponse) ProtoMessage() {}

func (x *ChangeStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_eventparticipant_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeStatusResponse.ProtoReflect.Descriptor instead.
func (*ChangeStatusResponse) Descriptor() ([]byte, []int) {
	return file_events_v1_eventparticipant_proto_rawDescGZIP(), []int{16}
}

func (x *ChangeStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ChangeStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_events_v1_eventparticipant_proto protoreflect.FileDescriptor

const file_events_v1_eventparticipant_proto_rawDesc = "" +
	"\n" +
	" events/v1/eventparticipant.proto\x12\rapi.events.v1\x1a\x14authz/v1/authz.proto\x1a\x16events/v1/events.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\xb9\x02\n" +
	"\x12EventParticipation\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x19\n" +
	"\bevent_id\x18\x02 \x01(\tR\aeventId\x12\x1b\n" +
	"\tvtuber_id\x18\x03 \x01(\tR\bvtuberId\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x123\n" +
	"\x06vtuber\x18\x05 \x01(\v2\x16.api.shared.v1.ProfileH\x00R\x06vtuber\x88\x01\x01\x12/\n" +
	"\x05event\x18\x06 \x01(\v2\x14.api.events.v1.EventH\x01R\x05event\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"vote_count\x18\a \x01(\x03R\tvoteCount\x12\x1d\n" +
	"\aremarks\x18\b \x01(\tH\x02R\aremarks\x88\x01\x01B\t\n" +
	"\a_vtuberB\b\n" +
	"\x06_eventB\n" +
	"\n" +
	"\b_remarks\"9\n" +
	"\x1cAddEventParticipationRequest\x12\x19\n" +
	"\bevent_id\x18\x01 \x01(\tR\aeventId\"%\n" +
	"#GetCreatorEventParticipationRequest\">\n" +
	"$GetCreatorEventParticipationResponse\x12\x16\n" +
	"\x06events\x18\x01 \x03(\tR\x06events\"\x99\x01\n" +
	"$GetEventParticipationOfVtuberRequest\x12\x1b\n" +
	"\tvtuber_id\x18\x01 \x01(\tR\bvtuberId\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\x97\x01\n" +
	"$GetEventParticipantsByEventIdRequest\x12\x19\n" +
	"\bevent_id\x18\x01 \x01(\tR\aeventId\x12E\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\xaf\x01\n" +
	"%GetEventParticipantsByEventIdResponse\x125\n" +
	"\x04data\x18\x01 \x03(\v2!.api.events.v1.EventParticipationR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"v\n" +
	"\x1eGetMyEventParticipationRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\xa9\x01\n" +
	"\x1fGetMyEventParticipationResponse\x125\n" +
	"\x04data\x18\x01 \x03(\v2!.api.events.v1.EventParticipationR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\x8b\x01\n" +
	"\x13ChangeStatusRequest\x124\n" +
	"\x16event_participation_id\x18\x01 \x01(\tR\x14eventParticipationId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1b\n" +
	"\x06reason\x18\x03 \x01(\tH\x00R\x06reason\x88\x01\x01B\t\n" +
	"\a_reason\"w\n" +
	"\x1fGetAllEventParticipationRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01B\r\n" +
	"\v_pagination\"\xaa\x01\n" +
	" GetAllEventParticipationResponse\x125\n" +
	"\x04data\x18\x01 \x03(\v2!.api.events.v1.EventParticipationR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"D\n" +
	"'GetTopTenEventParticipantsVtuberRequest\x12\x19\n" +
	"\bevent_id\x18\x01 \x01(\tR\aeventId\"e\n" +
	"(GetTopTenEventParticipantsVtuberResponse\x129\n" +
	"\x04data\x18\x01 \x03(\v2%.api.events.v1.EventParticipantVtuberR\x04data\"\x80\x01\n" +
	"\x16EventParticipantVtuber\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x19\n" +
	"\x05image\x18\x02 \x01(\tH\x00R\x05image\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"vote_count\x18\x03 \x01(\x05R\tvoteCount\x12\x0e\n" +
	"\x02id\x18\x04 \x01(\tR\x02idB\b\n" +
	"\x06_image\"S\n" +
	"\x1dAddEventParticipationResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"J\n" +
	"\x14ChangeStatusResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xa9\n" +
	"\n" +
	"\x17EventParticipantService\x12\x92\x01\n" +
	"\x15AddEventParticipation\x12+.api.events.v1.AddEventParticipationRequest\x1a,.api.events.v1.AddEventParticipationResponse\"\x1e\x82\xb5\x18\x1a\b\x01\x18\x01\"\x14_user.vtuberId!=null\x12\x8a\x01\n" +
	"\x1dGetEventParticipantsByEventId\x123.api.events.v1.GetEventParticipantsByEventIdRequest\x1a4.api.events.v1.GetEventParticipantsByEventIdResponse\x12\x98\x01\n" +
	"\x17GetMyEventParticipation\x12-.api.events.v1.GetMyEventParticipationRequest\x1a..api.events.v1.GetMyEventParticipationResponse\"\x1e\x82\xb5\x18\x1a\b\x01\x18\x01\"\x14_user.vtuberId!=null\x12a\n" +
	"\fChangeStatus\x12\".api.events.v1.ChangeStatusRequest\x1a#.api.events.v1.ChangeStatusResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12\x85\x01\n" +
	"\x18GetAllEventParticipation\x12..api.events.v1.GetAllEventParticipationRequest\x1a/.api.events.v1.GetAllEventParticipationResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12\xa7\x01\n" +
	"\x1cGetCreatorEventParticipation\x122.api.events.v1.GetCreatorEventParticipationRequest\x1a3.api.events.v1.GetCreatorEventParticipationResponse\"\x1e\x82\xb5\x18\x1a\b\x01\x18\x01\"\x14_user.vtuberId!=null\x12\x95\x01\n" +
	" GetAllEventParticipantsByEventId\x123.api.events.v1.GetEventParticipantsByEventIdRequest\x1a4.api.events.v1.GetEventParticipantsByEventIdResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x8a\x01\n" +
	" GetAllEventParticipationOfVtuber\x123.api.events.v1.GetEventParticipationOfVtuberRequest\x1a/.api.events.v1.GetAllEventParticipationResponse\"\x00\x12\x96\x01\n" +
	"!GetTopTenEventParticipantsVtubers\x126.api.events.v1.GetTopTenEventParticipantsVtuberRequest\x1a7.api.events.v1.GetTopTenEventParticipantsVtuberResponse\"\x00B2Z0github.com/nsp-inc/vtuber/api/events/v1;eventsv1b\x06proto3"

var (
	file_events_v1_eventparticipant_proto_rawDescOnce sync.Once
	file_events_v1_eventparticipant_proto_rawDescData []byte
)

func file_events_v1_eventparticipant_proto_rawDescGZIP() []byte {
	file_events_v1_eventparticipant_proto_rawDescOnce.Do(func() {
		file_events_v1_eventparticipant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_events_v1_eventparticipant_proto_rawDesc), len(file_events_v1_eventparticipant_proto_rawDesc)))
	})
	return file_events_v1_eventparticipant_proto_rawDescData
}

var file_events_v1_eventparticipant_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_events_v1_eventparticipant_proto_goTypes = []any{
	(*EventParticipation)(nil),                       // 0: api.events.v1.EventParticipation
	(*AddEventParticipationRequest)(nil),             // 1: api.events.v1.AddEventParticipationRequest
	(*GetCreatorEventParticipationRequest)(nil),      // 2: api.events.v1.GetCreatorEventParticipationRequest
	(*GetCreatorEventParticipationResponse)(nil),     // 3: api.events.v1.GetCreatorEventParticipationResponse
	(*GetEventParticipationOfVtuberRequest)(nil),     // 4: api.events.v1.GetEventParticipationOfVtuberRequest
	(*GetEventParticipantsByEventIdRequest)(nil),     // 5: api.events.v1.GetEventParticipantsByEventIdRequest
	(*GetEventParticipantsByEventIdResponse)(nil),    // 6: api.events.v1.GetEventParticipantsByEventIdResponse
	(*GetMyEventParticipationRequest)(nil),           // 7: api.events.v1.GetMyEventParticipationRequest
	(*GetMyEventParticipationResponse)(nil),          // 8: api.events.v1.GetMyEventParticipationResponse
	(*ChangeStatusRequest)(nil),                      // 9: api.events.v1.ChangeStatusRequest
	(*GetAllEventParticipationRequest)(nil),          // 10: api.events.v1.GetAllEventParticipationRequest
	(*GetAllEventParticipationResponse)(nil),         // 11: api.events.v1.GetAllEventParticipationResponse
	(*GetTopTenEventParticipantsVtuberRequest)(nil),  // 12: api.events.v1.GetTopTenEventParticipantsVtuberRequest
	(*GetTopTenEventParticipantsVtuberResponse)(nil), // 13: api.events.v1.GetTopTenEventParticipantsVtuberResponse
	(*EventParticipantVtuber)(nil),                   // 14: api.events.v1.EventParticipantVtuber
	(*AddEventParticipationResponse)(nil),            // 15: api.events.v1.AddEventParticipationResponse
	(*ChangeStatusResponse)(nil),                     // 16: api.events.v1.ChangeStatusResponse
	(*v1.Profile)(nil),                               // 17: api.shared.v1.Profile
	(*Event)(nil),                                    // 18: api.events.v1.Event
	(*v1.PaginationRequest)(nil),                     // 19: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),                     // 20: api.shared.v1.PaginationDetails
}
var file_events_v1_eventparticipant_proto_depIdxs = []int32{
	17, // 0: api.events.v1.EventParticipation.vtuber:type_name -> api.shared.v1.Profile
	18, // 1: api.events.v1.EventParticipation.event:type_name -> api.events.v1.Event
	19, // 2: api.events.v1.GetEventParticipationOfVtuberRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	19, // 3: api.events.v1.GetEventParticipantsByEventIdRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	0,  // 4: api.events.v1.GetEventParticipantsByEventIdResponse.data:type_name -> api.events.v1.EventParticipation
	20, // 5: api.events.v1.GetEventParticipantsByEventIdResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	19, // 6: api.events.v1.GetMyEventParticipationRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	0,  // 7: api.events.v1.GetMyEventParticipationResponse.data:type_name -> api.events.v1.EventParticipation
	20, // 8: api.events.v1.GetMyEventParticipationResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	19, // 9: api.events.v1.GetAllEventParticipationRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	0,  // 10: api.events.v1.GetAllEventParticipationResponse.data:type_name -> api.events.v1.EventParticipation
	20, // 11: api.events.v1.GetAllEventParticipationResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	14, // 12: api.events.v1.GetTopTenEventParticipantsVtuberResponse.data:type_name -> api.events.v1.EventParticipantVtuber
	1,  // 13: api.events.v1.EventParticipantService.AddEventParticipation:input_type -> api.events.v1.AddEventParticipationRequest
	5,  // 14: api.events.v1.EventParticipantService.GetEventParticipantsByEventId:input_type -> api.events.v1.GetEventParticipantsByEventIdRequest
	7,  // 15: api.events.v1.EventParticipantService.GetMyEventParticipation:input_type -> api.events.v1.GetMyEventParticipationRequest
	9,  // 16: api.events.v1.EventParticipantService.ChangeStatus:input_type -> api.events.v1.ChangeStatusRequest
	10, // 17: api.events.v1.EventParticipantService.GetAllEventParticipation:input_type -> api.events.v1.GetAllEventParticipationRequest
	2,  // 18: api.events.v1.EventParticipantService.GetCreatorEventParticipation:input_type -> api.events.v1.GetCreatorEventParticipationRequest
	5,  // 19: api.events.v1.EventParticipantService.GetAllEventParticipantsByEventId:input_type -> api.events.v1.GetEventParticipantsByEventIdRequest
	4,  // 20: api.events.v1.EventParticipantService.GetAllEventParticipationOfVtuber:input_type -> api.events.v1.GetEventParticipationOfVtuberRequest
	12, // 21: api.events.v1.EventParticipantService.GetTopTenEventParticipantsVtubers:input_type -> api.events.v1.GetTopTenEventParticipantsVtuberRequest
	15, // 22: api.events.v1.EventParticipantService.AddEventParticipation:output_type -> api.events.v1.AddEventParticipationResponse
	6,  // 23: api.events.v1.EventParticipantService.GetEventParticipantsByEventId:output_type -> api.events.v1.GetEventParticipantsByEventIdResponse
	8,  // 24: api.events.v1.EventParticipantService.GetMyEventParticipation:output_type -> api.events.v1.GetMyEventParticipationResponse
	16, // 25: api.events.v1.EventParticipantService.ChangeStatus:output_type -> api.events.v1.ChangeStatusResponse
	11, // 26: api.events.v1.EventParticipantService.GetAllEventParticipation:output_type -> api.events.v1.GetAllEventParticipationResponse
	3,  // 27: api.events.v1.EventParticipantService.GetCreatorEventParticipation:output_type -> api.events.v1.GetCreatorEventParticipationResponse
	6,  // 28: api.events.v1.EventParticipantService.GetAllEventParticipantsByEventId:output_type -> api.events.v1.GetEventParticipantsByEventIdResponse
	11, // 29: api.events.v1.EventParticipantService.GetAllEventParticipationOfVtuber:output_type -> api.events.v1.GetAllEventParticipationResponse
	13, // 30: api.events.v1.EventParticipantService.GetTopTenEventParticipantsVtubers:output_type -> api.events.v1.GetTopTenEventParticipantsVtuberResponse
	22, // [22:31] is the sub-list for method output_type
	13, // [13:22] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_events_v1_eventparticipant_proto_init() }
func file_events_v1_eventparticipant_proto_init() {
	if File_events_v1_eventparticipant_proto != nil {
		return
	}
	file_events_v1_events_proto_init()
	file_events_v1_eventparticipant_proto_msgTypes[0].OneofWrappers = []any{}
	file_events_v1_eventparticipant_proto_msgTypes[4].OneofWrappers = []any{}
	file_events_v1_eventparticipant_proto_msgTypes[5].OneofWrappers = []any{}
	file_events_v1_eventparticipant_proto_msgTypes[7].OneofWrappers = []any{}
	file_events_v1_eventparticipant_proto_msgTypes[9].OneofWrappers = []any{}
	file_events_v1_eventparticipant_proto_msgTypes[10].OneofWrappers = []any{}
	file_events_v1_eventparticipant_proto_msgTypes[14].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_events_v1_eventparticipant_proto_rawDesc), len(file_events_v1_eventparticipant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_events_v1_eventparticipant_proto_goTypes,
		DependencyIndexes: file_events_v1_eventparticipant_proto_depIdxs,
		MessageInfos:      file_events_v1_eventparticipant_proto_msgTypes,
	}.Build()
	File_events_v1_eventparticipant_proto = out.File
	file_events_v1_eventparticipant_proto_goTypes = nil
	file_events_v1_eventparticipant_proto_depIdxs = nil
}
