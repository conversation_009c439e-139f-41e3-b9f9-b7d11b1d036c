// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: authz/v1/authz.proto

package authzv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MethodOptions struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Require       *bool                  `protobuf:"varint,1,opt,name=require,proto3,oneof" json:"require,omitempty"`
	IsAdmin       *bool                  `protobuf:"varint,2,opt,name=is_admin,json=isAdmin,proto3,oneof" json:"is_admin,omitempty"`
	IsVtuber      *bool                  `protobuf:"varint,3,opt,name=is_vtuber,json=isVtuber,proto3,oneof" json:"is_vtuber,omitempty"`
	Expression    *string                `protobuf:"bytes,4,opt,name=expression,proto3,oneof" json:"expression,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MethodOptions) Reset() {
	*x = MethodOptions{}
	mi := &file_authz_v1_authz_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MethodOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MethodOptions) ProtoMessage() {}

func (x *MethodOptions) ProtoReflect() protoreflect.Message {
	mi := &file_authz_v1_authz_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MethodOptions.ProtoReflect.Descriptor instead.
func (*MethodOptions) Descriptor() ([]byte, []int) {
	return file_authz_v1_authz_proto_rawDescGZIP(), []int{0}
}

func (x *MethodOptions) GetRequire() bool {
	if x != nil && x.Require != nil {
		return *x.Require
	}
	return false
}

func (x *MethodOptions) GetIsAdmin() bool {
	if x != nil && x.IsAdmin != nil {
		return *x.IsAdmin
	}
	return false
}

func (x *MethodOptions) GetIsVtuber() bool {
	if x != nil && x.IsVtuber != nil {
		return *x.IsVtuber
	}
	return false
}

func (x *MethodOptions) GetExpression() string {
	if x != nil && x.Expression != nil {
		return *x.Expression
	}
	return ""
}

var file_authz_v1_authz_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.MethodOptions)(nil),
		ExtensionType: (*MethodOptions)(nil),
		Field:         50000,
		Name:          "api.authz.v1.options",
		Tag:           "bytes,50000,opt,name=options",
		Filename:      "authz/v1/authz.proto",
	},
}

// Extension fields to descriptorpb.MethodOptions.
var (
	// optional api.authz.v1.MethodOptions options = 50000;
	E_Options = &file_authz_v1_authz_proto_extTypes[0]
)

var File_authz_v1_authz_proto protoreflect.FileDescriptor

const file_authz_v1_authz_proto_rawDesc = "" +
	"\n" +
	"\x14authz/v1/authz.proto\x12\fapi.authz.v1\x1a google/protobuf/descriptor.proto\"\xcb\x01\n" +
	"\rMethodOptions\x12\x1d\n" +
	"\arequire\x18\x01 \x01(\bH\x00R\arequire\x88\x01\x01\x12\x1e\n" +
	"\bis_admin\x18\x02 \x01(\bH\x01R\aisAdmin\x88\x01\x01\x12 \n" +
	"\tis_vtuber\x18\x03 \x01(\bH\x02R\bisVtuber\x88\x01\x01\x12#\n" +
	"\n" +
	"expression\x18\x04 \x01(\tH\x03R\n" +
	"expression\x88\x01\x01B\n" +
	"\n" +
	"\b_requireB\v\n" +
	"\t_is_adminB\f\n" +
	"\n" +
	"_is_vtuberB\r\n" +
	"\v_expression:W\n" +
	"\aoptions\x12\x1e.google.protobuf.MethodOptions\x18І\x03 \x01(\v2\x1b.api.authz.v1.MethodOptionsR\aoptionsB0Z.github.com/nsp-inc/vtuber/api/authz/v1;authzv1b\x06proto3"

var (
	file_authz_v1_authz_proto_rawDescOnce sync.Once
	file_authz_v1_authz_proto_rawDescData []byte
)

func file_authz_v1_authz_proto_rawDescGZIP() []byte {
	file_authz_v1_authz_proto_rawDescOnce.Do(func() {
		file_authz_v1_authz_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_authz_v1_authz_proto_rawDesc), len(file_authz_v1_authz_proto_rawDesc)))
	})
	return file_authz_v1_authz_proto_rawDescData
}

var file_authz_v1_authz_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_authz_v1_authz_proto_goTypes = []any{
	(*MethodOptions)(nil),              // 0: api.authz.v1.MethodOptions
	(*descriptorpb.MethodOptions)(nil), // 1: google.protobuf.MethodOptions
}
var file_authz_v1_authz_proto_depIdxs = []int32{
	1, // 0: api.authz.v1.options:extendee -> google.protobuf.MethodOptions
	0, // 1: api.authz.v1.options:type_name -> api.authz.v1.MethodOptions
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	1, // [1:2] is the sub-list for extension type_name
	0, // [0:1] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_authz_v1_authz_proto_init() }
func file_authz_v1_authz_proto_init() {
	if File_authz_v1_authz_proto != nil {
		return
	}
	file_authz_v1_authz_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_authz_v1_authz_proto_rawDesc), len(file_authz_v1_authz_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 1,
			NumServices:   0,
		},
		GoTypes:           file_authz_v1_authz_proto_goTypes,
		DependencyIndexes: file_authz_v1_authz_proto_depIdxs,
		MessageInfos:      file_authz_v1_authz_proto_msgTypes,
		ExtensionInfos:    file_authz_v1_authz_proto_extTypes,
	}.Build()
	File_authz_v1_authz_proto = out.File
	file_authz_v1_authz_proto_goTypes = nil
	file_authz_v1_authz_proto_depIdxs = nil
}
