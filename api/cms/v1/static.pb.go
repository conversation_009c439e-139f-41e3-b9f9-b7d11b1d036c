// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: cms/v1/static.proto

package cmsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateStaticRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateStaticRequest) Reset() {
	*x = UpdateStaticRequest{}
	mi := &file_cms_v1_static_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateStaticRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaticRequest) ProtoMessage() {}

func (x *UpdateStaticRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaticRequest.ProtoReflect.Descriptor instead.
func (*UpdateStaticRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateStaticRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateStaticRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type AddStaticRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty" validate:"required"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty" validate:"required"`
	Language      string                 `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty" validate:"required,oneof=en-us ja-jp"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddStaticRequest) Reset() {
	*x = AddStaticRequest{}
	mi := &file_cms_v1_static_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddStaticRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddStaticRequest) ProtoMessage() {}

func (x *AddStaticRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddStaticRequest.ProtoReflect.Descriptor instead.
func (*AddStaticRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{1}
}

func (x *AddStaticRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *AddStaticRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *AddStaticRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type DeleteStaticRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteStaticRequest) Reset() {
	*x = DeleteStaticRequest{}
	mi := &file_cms_v1_static_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteStaticRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaticRequest) ProtoMessage() {}

func (x *DeleteStaticRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaticRequest.ProtoReflect.Descriptor instead.
func (*DeleteStaticRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteStaticRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateSocialMediaRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	SocialMediaLinks *v1.SocialMediaLinks   `protobuf:"bytes,1,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdateSocialMediaRequest) Reset() {
	*x = UpdateSocialMediaRequest{}
	mi := &file_cms_v1_static_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSocialMediaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSocialMediaRequest) ProtoMessage() {}

func (x *UpdateSocialMediaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSocialMediaRequest.ProtoReflect.Descriptor instead.
func (*UpdateSocialMediaRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateSocialMediaRequest) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

type UpdateSocialMediaResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSocialMediaResponse) Reset() {
	*x = UpdateSocialMediaResponse{}
	mi := &file_cms_v1_static_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSocialMediaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSocialMediaResponse) ProtoMessage() {}

func (x *UpdateSocialMediaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSocialMediaResponse.ProtoReflect.Descriptor instead.
func (*UpdateSocialMediaResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateSocialMediaResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateSocialMediaResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetSocialMediaLinksResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	SocialMediaLinks *v1.SocialMediaLinks   `protobuf:"bytes,1,opt,name=social_media_links,json=socialMediaLinks,proto3" json:"social_media_links,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetSocialMediaLinksResponse) Reset() {
	*x = GetSocialMediaLinksResponse{}
	mi := &file_cms_v1_static_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSocialMediaLinksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSocialMediaLinksResponse) ProtoMessage() {}

func (x *GetSocialMediaLinksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSocialMediaLinksResponse.ProtoReflect.Descriptor instead.
func (*GetSocialMediaLinksResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{5}
}

func (x *GetSocialMediaLinksResponse) GetSocialMediaLinks() *v1.SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

type GetSocialMediaLinksRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSocialMediaLinksRequest) Reset() {
	*x = GetSocialMediaLinksRequest{}
	mi := &file_cms_v1_static_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSocialMediaLinksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSocialMediaLinksRequest) ProtoMessage() {}

func (x *GetSocialMediaLinksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSocialMediaLinksRequest.ProtoReflect.Descriptor instead.
func (*GetSocialMediaLinksRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{6}
}

type StaticResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Key           string                 `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	Language      string                 `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StaticResponse) Reset() {
	*x = StaticResponse{}
	mi := &file_cms_v1_static_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaticResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaticResponse) ProtoMessage() {}

func (x *StaticResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaticResponse.ProtoReflect.Descriptor instead.
func (*StaticResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{7}
}

func (x *StaticResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StaticResponse) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *StaticResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *StaticResponse) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *StaticResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *StaticResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type GetAllStaticRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllStaticRequest) Reset() {
	*x = GetAllStaticRequest{}
	mi := &file_cms_v1_static_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllStaticRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllStaticRequest) ProtoMessage() {}

func (x *GetAllStaticRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllStaticRequest.ProtoReflect.Descriptor instead.
func (*GetAllStaticRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{8}
}

func (x *GetAllStaticRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type GetAllStaticResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*StaticResponse      `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllStaticResponse) Reset() {
	*x = GetAllStaticResponse{}
	mi := &file_cms_v1_static_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllStaticResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllStaticResponse) ProtoMessage() {}

func (x *GetAllStaticResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllStaticResponse.ProtoReflect.Descriptor instead.
func (*GetAllStaticResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{9}
}

func (x *GetAllStaticResponse) GetData() []*StaticResponse {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteStaticResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteStaticResponse) Reset() {
	*x = DeleteStaticResponse{}
	mi := &file_cms_v1_static_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteStaticResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaticResponse) ProtoMessage() {}

func (x *DeleteStaticResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_static_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaticResponse.ProtoReflect.Descriptor instead.
func (*DeleteStaticResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_static_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteStaticResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteStaticResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_cms_v1_static_proto protoreflect.FileDescriptor

const file_cms_v1_static_proto_rawDesc = "" +
	"\n" +
	"\x13cms/v1/static.proto\x12\n" +
	"api.cms.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\";\n" +
	"\x13UpdateStaticRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\"V\n" +
	"\x10AddStaticRequest\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\x12\x1a\n" +
	"\blanguage\x18\x03 \x01(\tR\blanguage\"%\n" +
	"\x13DeleteStaticRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"i\n" +
	"\x18UpdateSocialMediaRequest\x12M\n" +
	"\x12social_media_links\x18\x01 \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\"O\n" +
	"\x19UpdateSocialMediaResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"l\n" +
	"\x1bGetSocialMediaLinksResponse\x12M\n" +
	"\x12social_media_links\x18\x01 \x01(\v2\x1f.api.shared.v1.SocialMediaLinksR\x10socialMediaLinks\"\x1c\n" +
	"\x1aGetSocialMediaLinksRequest\"\xda\x01\n" +
	"\x0eStaticResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x10\n" +
	"\x03key\x18\x02 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\x12\x1a\n" +
	"\blanguage\x18\x04 \x01(\tR\blanguage\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"'\n" +
	"\x13GetAllStaticRequest\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\"F\n" +
	"\x14GetAllStaticResponse\x12.\n" +
	"\x04data\x18\x01 \x03(\v2\x1a.api.cms.v1.StaticResponseR\x04data\"J\n" +
	"\x14DeleteStaticResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xb3\x05\n" +
	"\rStaticService\x12r\n" +
	"\x14UpdateStaticResource\x12\x1f.api.cms.v1.UpdateStaticRequest\x1a\x1a.api.cms.v1.StaticResponse\"\x1d\x82\xb5\x18\x19\b\x01\"\x15_user.role == 'admin'\x12[\n" +
	"\x14GetAllStaticResource\x12\x1f.api.cms.v1.GetAllStaticRequest\x1a .api.cms.v1.GetAllStaticResponse\"\x00\x12l\n" +
	"\x11AddStaticResource\x12\x1c.api.cms.v1.AddStaticRequest\x1a\x1a.api.cms.v1.StaticResponse\"\x1d\x82\xb5\x18\x19\b\x01\"\x15_user.role == 'admin'\x12x\n" +
	"\x14DeleteStaticResource\x12\x1f.api.cms.v1.DeleteStaticRequest\x1a .api.cms.v1.DeleteStaticResponse\"\x1d\x82\xb5\x18\x19\b\x01\"\x15_user.role == 'admin'\x12\x7f\n" +
	"\x11UpdateSocialMedia\x12$.api.cms.v1.UpdateSocialMediaRequest\x1a%.api.cms.v1.UpdateSocialMediaResponse\"\x1d\x82\xb5\x18\x19\b\x01\"\x15_user.role == 'admin'\x12h\n" +
	"\x13GetSocialMediaLinks\x12&.api.cms.v1.GetSocialMediaLinksRequest\x1a'.api.cms.v1.GetSocialMediaLinksResponse\"\x00B,Z*github.com/nsp-inc/vtuber/api/cms/v1;cmsv1b\x06proto3"

var (
	file_cms_v1_static_proto_rawDescOnce sync.Once
	file_cms_v1_static_proto_rawDescData []byte
)

func file_cms_v1_static_proto_rawDescGZIP() []byte {
	file_cms_v1_static_proto_rawDescOnce.Do(func() {
		file_cms_v1_static_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cms_v1_static_proto_rawDesc), len(file_cms_v1_static_proto_rawDesc)))
	})
	return file_cms_v1_static_proto_rawDescData
}

var file_cms_v1_static_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_cms_v1_static_proto_goTypes = []any{
	(*UpdateStaticRequest)(nil),         // 0: api.cms.v1.UpdateStaticRequest
	(*AddStaticRequest)(nil),            // 1: api.cms.v1.AddStaticRequest
	(*DeleteStaticRequest)(nil),         // 2: api.cms.v1.DeleteStaticRequest
	(*UpdateSocialMediaRequest)(nil),    // 3: api.cms.v1.UpdateSocialMediaRequest
	(*UpdateSocialMediaResponse)(nil),   // 4: api.cms.v1.UpdateSocialMediaResponse
	(*GetSocialMediaLinksResponse)(nil), // 5: api.cms.v1.GetSocialMediaLinksResponse
	(*GetSocialMediaLinksRequest)(nil),  // 6: api.cms.v1.GetSocialMediaLinksRequest
	(*StaticResponse)(nil),              // 7: api.cms.v1.StaticResponse
	(*GetAllStaticRequest)(nil),         // 8: api.cms.v1.GetAllStaticRequest
	(*GetAllStaticResponse)(nil),        // 9: api.cms.v1.GetAllStaticResponse
	(*DeleteStaticResponse)(nil),        // 10: api.cms.v1.DeleteStaticResponse
	(*v1.SocialMediaLinks)(nil),         // 11: api.shared.v1.SocialMediaLinks
	(*timestamppb.Timestamp)(nil),       // 12: google.protobuf.Timestamp
}
var file_cms_v1_static_proto_depIdxs = []int32{
	11, // 0: api.cms.v1.UpdateSocialMediaRequest.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	11, // 1: api.cms.v1.GetSocialMediaLinksResponse.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	12, // 2: api.cms.v1.StaticResponse.created_at:type_name -> google.protobuf.Timestamp
	12, // 3: api.cms.v1.StaticResponse.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 4: api.cms.v1.GetAllStaticResponse.data:type_name -> api.cms.v1.StaticResponse
	0,  // 5: api.cms.v1.StaticService.UpdateStaticResource:input_type -> api.cms.v1.UpdateStaticRequest
	8,  // 6: api.cms.v1.StaticService.GetAllStaticResource:input_type -> api.cms.v1.GetAllStaticRequest
	1,  // 7: api.cms.v1.StaticService.AddStaticResource:input_type -> api.cms.v1.AddStaticRequest
	2,  // 8: api.cms.v1.StaticService.DeleteStaticResource:input_type -> api.cms.v1.DeleteStaticRequest
	3,  // 9: api.cms.v1.StaticService.UpdateSocialMedia:input_type -> api.cms.v1.UpdateSocialMediaRequest
	6,  // 10: api.cms.v1.StaticService.GetSocialMediaLinks:input_type -> api.cms.v1.GetSocialMediaLinksRequest
	7,  // 11: api.cms.v1.StaticService.UpdateStaticResource:output_type -> api.cms.v1.StaticResponse
	9,  // 12: api.cms.v1.StaticService.GetAllStaticResource:output_type -> api.cms.v1.GetAllStaticResponse
	7,  // 13: api.cms.v1.StaticService.AddStaticResource:output_type -> api.cms.v1.StaticResponse
	10, // 14: api.cms.v1.StaticService.DeleteStaticResource:output_type -> api.cms.v1.DeleteStaticResponse
	4,  // 15: api.cms.v1.StaticService.UpdateSocialMedia:output_type -> api.cms.v1.UpdateSocialMediaResponse
	5,  // 16: api.cms.v1.StaticService.GetSocialMediaLinks:output_type -> api.cms.v1.GetSocialMediaLinksResponse
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_cms_v1_static_proto_init() }
func file_cms_v1_static_proto_init() {
	if File_cms_v1_static_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cms_v1_static_proto_rawDesc), len(file_cms_v1_static_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cms_v1_static_proto_goTypes,
		DependencyIndexes: file_cms_v1_static_proto_depIdxs,
		MessageInfos:      file_cms_v1_static_proto_msgTypes,
	}.Build()
	File_cms_v1_static_proto = out.File
	file_cms_v1_static_proto_goTypes = nil
	file_cms_v1_static_proto_depIdxs = nil
}
