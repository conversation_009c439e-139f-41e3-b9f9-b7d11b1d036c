// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: cms/v1/faqs.proto

package cmsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	_ "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddFaqRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Question      string                 `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty" validate:"required"`
	Response      string                 `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty" validate:"required"`
	Index         int32                  `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty" validate:"gte=0"`
	IsActive      *bool                  `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	Language      string                 `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty" validate:"omitempty,oneof=en-us ja-jp"`
	Tag           string                 `protobuf:"bytes,6,opt,name=tag,proto3" json:"tag,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFaqRequest) Reset() {
	*x = AddFaqRequest{}
	mi := &file_cms_v1_faqs_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFaqRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFaqRequest) ProtoMessage() {}

func (x *AddFaqRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFaqRequest.ProtoReflect.Descriptor instead.
func (*AddFaqRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{0}
}

func (x *AddFaqRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *AddFaqRequest) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *AddFaqRequest) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *AddFaqRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *AddFaqRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *AddFaqRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type AddFaqResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Faq                   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFaqResponse) Reset() {
	*x = AddFaqResponse{}
	mi := &file_cms_v1_faqs_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFaqResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFaqResponse) ProtoMessage() {}

func (x *AddFaqResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFaqResponse.ProtoReflect.Descriptor instead.
func (*AddFaqResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{1}
}

func (x *AddFaqResponse) GetData() *Faq {
	if x != nil {
		return x.Data
	}
	return nil
}

type Faq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Question      string                 `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	Response      string                 `protobuf:"bytes,3,opt,name=response,proto3" json:"response,omitempty"`
	Index         int32                  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	IsActive      bool                   `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	Language      string                 `protobuf:"bytes,7,opt,name=language,proto3" json:"language,omitempty"`
	Tag           string                 `protobuf:"bytes,8,opt,name=tag,proto3" json:"tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Faq) Reset() {
	*x = Faq{}
	mi := &file_cms_v1_faqs_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Faq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Faq) ProtoMessage() {}

func (x *Faq) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Faq.ProtoReflect.Descriptor instead.
func (*Faq) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{2}
}

func (x *Faq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Faq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *Faq) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *Faq) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *Faq) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Faq) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Faq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *Faq) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type GetAllFaqsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Language      *string                `protobuf:"bytes,1,opt,name=language,proto3,oneof" json:"language,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllFaqsRequest) Reset() {
	*x = GetAllFaqsRequest{}
	mi := &file_cms_v1_faqs_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllFaqsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllFaqsRequest) ProtoMessage() {}

func (x *GetAllFaqsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllFaqsRequest.ProtoReflect.Descriptor instead.
func (*GetAllFaqsRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllFaqsRequest) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

type GetAllFaqsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*Faq                 `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllFaqsResponse) Reset() {
	*x = GetAllFaqsResponse{}
	mi := &file_cms_v1_faqs_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllFaqsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllFaqsResponse) ProtoMessage() {}

func (x *GetAllFaqsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllFaqsResponse.ProtoReflect.Descriptor instead.
func (*GetAllFaqsResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllFaqsResponse) GetData() []*Faq {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetFaqRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFaqRequest) Reset() {
	*x = GetFaqRequest{}
	mi := &file_cms_v1_faqs_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFaqRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFaqRequest) ProtoMessage() {}

func (x *GetFaqRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFaqRequest.ProtoReflect.Descriptor instead.
func (*GetFaqRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{5}
}

func (x *GetFaqRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetFaqResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Faq                   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFaqResponse) Reset() {
	*x = GetFaqResponse{}
	mi := &file_cms_v1_faqs_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFaqResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFaqResponse) ProtoMessage() {}

func (x *GetFaqResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFaqResponse.ProtoReflect.Descriptor instead.
func (*GetFaqResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{6}
}

func (x *GetFaqResponse) GetData() *Faq {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateFaqRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Question      string                 `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty" validate:"required"`
	Response      string                 `protobuf:"bytes,3,opt,name=response,proto3" json:"response,omitempty" validate:"required"`
	Index         int32                  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty" validate:"gte=0"`
	Language      string                 `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty" validate:"omitempty,oneof=en-us ja-jp"`
	Tag           string                 `protobuf:"bytes,6,opt,name=tag,proto3" json:"tag,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateFaqRequest) Reset() {
	*x = UpdateFaqRequest{}
	mi := &file_cms_v1_faqs_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFaqRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFaqRequest) ProtoMessage() {}

func (x *UpdateFaqRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFaqRequest.ProtoReflect.Descriptor instead.
func (*UpdateFaqRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateFaqRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateFaqRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *UpdateFaqRequest) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *UpdateFaqRequest) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *UpdateFaqRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UpdateFaqRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type DeleteFaqRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFaqRequest) Reset() {
	*x = DeleteFaqRequest{}
	mi := &file_cms_v1_faqs_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFaqRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFaqRequest) ProtoMessage() {}

func (x *DeleteFaqRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFaqRequest.ProtoReflect.Descriptor instead.
func (*DeleteFaqRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteFaqRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ToogleFaqStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ToogleFaqStatusRequest) Reset() {
	*x = ToogleFaqStatusRequest{}
	mi := &file_cms_v1_faqs_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ToogleFaqStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToogleFaqStatusRequest) ProtoMessage() {}

func (x *ToogleFaqStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToogleFaqStatusRequest.ProtoReflect.Descriptor instead.
func (*ToogleFaqStatusRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{9}
}

func (x *ToogleFaqStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetAllActiveFaqsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Language      *string                `protobuf:"bytes,1,opt,name=language,proto3,oneof" json:"language,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllActiveFaqsRequest) Reset() {
	*x = GetAllActiveFaqsRequest{}
	mi := &file_cms_v1_faqs_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllActiveFaqsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllActiveFaqsRequest) ProtoMessage() {}

func (x *GetAllActiveFaqsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllActiveFaqsRequest.ProtoReflect.Descriptor instead.
func (*GetAllActiveFaqsRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{10}
}

func (x *GetAllActiveFaqsRequest) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

type UpdateFaqResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateFaqResponse) Reset() {
	*x = UpdateFaqResponse{}
	mi := &file_cms_v1_faqs_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFaqResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFaqResponse) ProtoMessage() {}

func (x *UpdateFaqResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFaqResponse.ProtoReflect.Descriptor instead.
func (*UpdateFaqResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateFaqResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateFaqResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteFaqResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFaqResponse) Reset() {
	*x = DeleteFaqResponse{}
	mi := &file_cms_v1_faqs_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFaqResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFaqResponse) ProtoMessage() {}

func (x *DeleteFaqResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFaqResponse.ProtoReflect.Descriptor instead.
func (*DeleteFaqResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteFaqResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteFaqResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ToogleFaqStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ToogleFaqStatusResponse) Reset() {
	*x = ToogleFaqStatusResponse{}
	mi := &file_cms_v1_faqs_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ToogleFaqStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToogleFaqStatusResponse) ProtoMessage() {}

func (x *ToogleFaqStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_faqs_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToogleFaqStatusResponse.ProtoReflect.Descriptor instead.
func (*ToogleFaqStatusResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_faqs_proto_rawDescGZIP(), []int{13}
}

func (x *ToogleFaqStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ToogleFaqStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_cms_v1_faqs_proto protoreflect.FileDescriptor

const file_cms_v1_faqs_proto_rawDesc = "" +
	"\n" +
	"\x11cms/v1/faqs.proto\x12\n" +
	"api.cms.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\xbb\x01\n" +
	"\rAddFaqRequest\x12\x1a\n" +
	"\bquestion\x18\x01 \x01(\tR\bquestion\x12\x1a\n" +
	"\bresponse\x18\x02 \x01(\tR\bresponse\x12\x14\n" +
	"\x05index\x18\x03 \x01(\x05R\x05index\x12 \n" +
	"\tis_active\x18\x04 \x01(\bH\x00R\bisActive\x88\x01\x01\x12\x1a\n" +
	"\blanguage\x18\x05 \x01(\tR\blanguage\x12\x10\n" +
	"\x03tag\x18\x06 \x01(\tR\x03tagB\f\n" +
	"\n" +
	"_is_active\"5\n" +
	"\x0eAddFaqResponse\x12#\n" +
	"\x04data\x18\x01 \x01(\v2\x0f.api.cms.v1.FaqR\x04data\"\xe9\x01\n" +
	"\x03Faq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\bquestion\x18\x02 \x01(\tR\bquestion\x12\x1a\n" +
	"\bresponse\x18\x03 \x01(\tR\bresponse\x12\x14\n" +
	"\x05index\x18\x04 \x01(\x05R\x05index\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive\x12\x1a\n" +
	"\blanguage\x18\a \x01(\tR\blanguage\x12\x10\n" +
	"\x03tag\x18\b \x01(\tR\x03tag\"A\n" +
	"\x11GetAllFaqsRequest\x12\x1f\n" +
	"\blanguage\x18\x01 \x01(\tH\x00R\blanguage\x88\x01\x01B\v\n" +
	"\t_language\"9\n" +
	"\x12GetAllFaqsResponse\x12#\n" +
	"\x04data\x18\x01 \x03(\v2\x0f.api.cms.v1.FaqR\x04data\"\x1f\n" +
	"\rGetFaqRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"5\n" +
	"\x0eGetFaqResponse\x12#\n" +
	"\x04data\x18\x01 \x01(\v2\x0f.api.cms.v1.FaqR\x04data\"\x9e\x01\n" +
	"\x10UpdateFaqRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\bquestion\x18\x02 \x01(\tR\bquestion\x12\x1a\n" +
	"\bresponse\x18\x03 \x01(\tR\bresponse\x12\x14\n" +
	"\x05index\x18\x04 \x01(\x05R\x05index\x12\x1a\n" +
	"\blanguage\x18\x05 \x01(\tR\blanguage\x12\x10\n" +
	"\x03tag\x18\x06 \x01(\tR\x03tag\"\"\n" +
	"\x10DeleteFaqRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"(\n" +
	"\x16ToogleFaqStatusRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"G\n" +
	"\x17GetAllActiveFaqsRequest\x12\x1f\n" +
	"\blanguage\x18\x01 \x01(\tH\x00R\blanguage\x88\x01\x01B\v\n" +
	"\t_language\"G\n" +
	"\x11UpdateFaqResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"G\n" +
	"\x11DeleteFaqResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"M\n" +
	"\x17ToogleFaqStatusResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xd6\x04\n" +
	"\n" +
	"FaqService\x12I\n" +
	"\x06AddFaq\x12\x19.api.cms.v1.AddFaqRequest\x1a\x1a.api.cms.v1.AddFaqResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12U\n" +
	"\n" +
	"GetAllFaqs\x12\x1d.api.cms.v1.GetAllFaqsRequest\x1a\x1e.api.cms.v1.GetAllFaqsResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12?\n" +
	"\x06GetFaq\x12\x19.api.cms.v1.GetFaqRequest\x1a\x1a.api.cms.v1.GetFaqResponse\x12R\n" +
	"\tUpdateFaq\x12\x1c.api.cms.v1.UpdateFaqRequest\x1a\x1d.api.cms.v1.UpdateFaqResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12R\n" +
	"\tDeleteFaq\x12\x1c.api.cms.v1.DeleteFaqRequest\x1a\x1d.api.cms.v1.DeleteFaqResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12d\n" +
	"\x0fToogleFaqStatus\x12\".api.cms.v1.ToogleFaqStatusRequest\x1a#.api.cms.v1.ToogleFaqStatusResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12W\n" +
	"\x10GetAllActiveFaqs\x12#.api.cms.v1.GetAllActiveFaqsRequest\x1a\x1e.api.cms.v1.GetAllFaqsResponseB,Z*github.com/nsp-inc/vtuber/api/cms/v1;cmsv1b\x06proto3"

var (
	file_cms_v1_faqs_proto_rawDescOnce sync.Once
	file_cms_v1_faqs_proto_rawDescData []byte
)

func file_cms_v1_faqs_proto_rawDescGZIP() []byte {
	file_cms_v1_faqs_proto_rawDescOnce.Do(func() {
		file_cms_v1_faqs_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cms_v1_faqs_proto_rawDesc), len(file_cms_v1_faqs_proto_rawDesc)))
	})
	return file_cms_v1_faqs_proto_rawDescData
}

var file_cms_v1_faqs_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_cms_v1_faqs_proto_goTypes = []any{
	(*AddFaqRequest)(nil),           // 0: api.cms.v1.AddFaqRequest
	(*AddFaqResponse)(nil),          // 1: api.cms.v1.AddFaqResponse
	(*Faq)(nil),                     // 2: api.cms.v1.Faq
	(*GetAllFaqsRequest)(nil),       // 3: api.cms.v1.GetAllFaqsRequest
	(*GetAllFaqsResponse)(nil),      // 4: api.cms.v1.GetAllFaqsResponse
	(*GetFaqRequest)(nil),           // 5: api.cms.v1.GetFaqRequest
	(*GetFaqResponse)(nil),          // 6: api.cms.v1.GetFaqResponse
	(*UpdateFaqRequest)(nil),        // 7: api.cms.v1.UpdateFaqRequest
	(*DeleteFaqRequest)(nil),        // 8: api.cms.v1.DeleteFaqRequest
	(*ToogleFaqStatusRequest)(nil),  // 9: api.cms.v1.ToogleFaqStatusRequest
	(*GetAllActiveFaqsRequest)(nil), // 10: api.cms.v1.GetAllActiveFaqsRequest
	(*UpdateFaqResponse)(nil),       // 11: api.cms.v1.UpdateFaqResponse
	(*DeleteFaqResponse)(nil),       // 12: api.cms.v1.DeleteFaqResponse
	(*ToogleFaqStatusResponse)(nil), // 13: api.cms.v1.ToogleFaqStatusResponse
	(*timestamppb.Timestamp)(nil),   // 14: google.protobuf.Timestamp
}
var file_cms_v1_faqs_proto_depIdxs = []int32{
	2,  // 0: api.cms.v1.AddFaqResponse.data:type_name -> api.cms.v1.Faq
	14, // 1: api.cms.v1.Faq.created_at:type_name -> google.protobuf.Timestamp
	2,  // 2: api.cms.v1.GetAllFaqsResponse.data:type_name -> api.cms.v1.Faq
	2,  // 3: api.cms.v1.GetFaqResponse.data:type_name -> api.cms.v1.Faq
	0,  // 4: api.cms.v1.FaqService.AddFaq:input_type -> api.cms.v1.AddFaqRequest
	3,  // 5: api.cms.v1.FaqService.GetAllFaqs:input_type -> api.cms.v1.GetAllFaqsRequest
	5,  // 6: api.cms.v1.FaqService.GetFaq:input_type -> api.cms.v1.GetFaqRequest
	7,  // 7: api.cms.v1.FaqService.UpdateFaq:input_type -> api.cms.v1.UpdateFaqRequest
	8,  // 8: api.cms.v1.FaqService.DeleteFaq:input_type -> api.cms.v1.DeleteFaqRequest
	9,  // 9: api.cms.v1.FaqService.ToogleFaqStatus:input_type -> api.cms.v1.ToogleFaqStatusRequest
	10, // 10: api.cms.v1.FaqService.GetAllActiveFaqs:input_type -> api.cms.v1.GetAllActiveFaqsRequest
	1,  // 11: api.cms.v1.FaqService.AddFaq:output_type -> api.cms.v1.AddFaqResponse
	4,  // 12: api.cms.v1.FaqService.GetAllFaqs:output_type -> api.cms.v1.GetAllFaqsResponse
	6,  // 13: api.cms.v1.FaqService.GetFaq:output_type -> api.cms.v1.GetFaqResponse
	11, // 14: api.cms.v1.FaqService.UpdateFaq:output_type -> api.cms.v1.UpdateFaqResponse
	12, // 15: api.cms.v1.FaqService.DeleteFaq:output_type -> api.cms.v1.DeleteFaqResponse
	13, // 16: api.cms.v1.FaqService.ToogleFaqStatus:output_type -> api.cms.v1.ToogleFaqStatusResponse
	4,  // 17: api.cms.v1.FaqService.GetAllActiveFaqs:output_type -> api.cms.v1.GetAllFaqsResponse
	11, // [11:18] is the sub-list for method output_type
	4,  // [4:11] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_cms_v1_faqs_proto_init() }
func file_cms_v1_faqs_proto_init() {
	if File_cms_v1_faqs_proto != nil {
		return
	}
	file_cms_v1_faqs_proto_msgTypes[0].OneofWrappers = []any{}
	file_cms_v1_faqs_proto_msgTypes[3].OneofWrappers = []any{}
	file_cms_v1_faqs_proto_msgTypes[10].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cms_v1_faqs_proto_rawDesc), len(file_cms_v1_faqs_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cms_v1_faqs_proto_goTypes,
		DependencyIndexes: file_cms_v1_faqs_proto_depIdxs,
		MessageInfos:      file_cms_v1_faqs_proto_msgTypes,
	}.Build()
	File_cms_v1_faqs_proto = out.File
	file_cms_v1_faqs_proto_goTypes = nil
	file_cms_v1_faqs_proto_depIdxs = nil
}
