// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: cms/v1/announcements.proto

package cmsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/cms/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// AnnouncementsServiceName is the fully-qualified name of the AnnouncementsService service.
	AnnouncementsServiceName = "api.cms.v1.AnnouncementsService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// AnnouncementsServiceAddAnnouncementProcedure is the fully-qualified name of the
	// AnnouncementsService's AddAnnouncement RPC.
	AnnouncementsServiceAddAnnouncementProcedure = "/api.cms.v1.AnnouncementsService/AddAnnouncement"
	// AnnouncementsServiceUpdateAnnouncementProcedure is the fully-qualified name of the
	// AnnouncementsService's UpdateAnnouncement RPC.
	AnnouncementsServiceUpdateAnnouncementProcedure = "/api.cms.v1.AnnouncementsService/UpdateAnnouncement"
	// AnnouncementsServiceToggleAnnouncementProcedure is the fully-qualified name of the
	// AnnouncementsService's ToggleAnnouncement RPC.
	AnnouncementsServiceToggleAnnouncementProcedure = "/api.cms.v1.AnnouncementsService/ToggleAnnouncement"
	// AnnouncementsServiceGetAnnouncementProcedure is the fully-qualified name of the
	// AnnouncementsService's GetAnnouncement RPC.
	AnnouncementsServiceGetAnnouncementProcedure = "/api.cms.v1.AnnouncementsService/GetAnnouncement"
)

// AnnouncementsServiceClient is a client for the api.cms.v1.AnnouncementsService service.
type AnnouncementsServiceClient interface {
	AddAnnouncement(context.Context, *connect.Request[v1.AddAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error)
	UpdateAnnouncement(context.Context, *connect.Request[v1.UpdateAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error)
	ToggleAnnouncement(context.Context, *connect.Request[v1.ToggleAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error)
	GetAnnouncement(context.Context, *connect.Request[v1.GetAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error)
}

// NewAnnouncementsServiceClient constructs a client for the api.cms.v1.AnnouncementsService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewAnnouncementsServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) AnnouncementsServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	announcementsServiceMethods := v1.File_cms_v1_announcements_proto.Services().ByName("AnnouncementsService").Methods()
	return &announcementsServiceClient{
		addAnnouncement: connect.NewClient[v1.AddAnnouncementRequest, v1.AnnouncementResponse](
			httpClient,
			baseURL+AnnouncementsServiceAddAnnouncementProcedure,
			connect.WithSchema(announcementsServiceMethods.ByName("AddAnnouncement")),
			connect.WithClientOptions(opts...),
		),
		updateAnnouncement: connect.NewClient[v1.UpdateAnnouncementRequest, v1.AnnouncementResponse](
			httpClient,
			baseURL+AnnouncementsServiceUpdateAnnouncementProcedure,
			connect.WithSchema(announcementsServiceMethods.ByName("UpdateAnnouncement")),
			connect.WithClientOptions(opts...),
		),
		toggleAnnouncement: connect.NewClient[v1.ToggleAnnouncementRequest, v1.AnnouncementResponse](
			httpClient,
			baseURL+AnnouncementsServiceToggleAnnouncementProcedure,
			connect.WithSchema(announcementsServiceMethods.ByName("ToggleAnnouncement")),
			connect.WithClientOptions(opts...),
		),
		getAnnouncement: connect.NewClient[v1.GetAnnouncementRequest, v1.AnnouncementResponse](
			httpClient,
			baseURL+AnnouncementsServiceGetAnnouncementProcedure,
			connect.WithSchema(announcementsServiceMethods.ByName("GetAnnouncement")),
			connect.WithClientOptions(opts...),
		),
	}
}

// announcementsServiceClient implements AnnouncementsServiceClient.
type announcementsServiceClient struct {
	addAnnouncement    *connect.Client[v1.AddAnnouncementRequest, v1.AnnouncementResponse]
	updateAnnouncement *connect.Client[v1.UpdateAnnouncementRequest, v1.AnnouncementResponse]
	toggleAnnouncement *connect.Client[v1.ToggleAnnouncementRequest, v1.AnnouncementResponse]
	getAnnouncement    *connect.Client[v1.GetAnnouncementRequest, v1.AnnouncementResponse]
}

// AddAnnouncement calls api.cms.v1.AnnouncementsService.AddAnnouncement.
func (c *announcementsServiceClient) AddAnnouncement(ctx context.Context, req *connect.Request[v1.AddAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error) {
	return c.addAnnouncement.CallUnary(ctx, req)
}

// UpdateAnnouncement calls api.cms.v1.AnnouncementsService.UpdateAnnouncement.
func (c *announcementsServiceClient) UpdateAnnouncement(ctx context.Context, req *connect.Request[v1.UpdateAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error) {
	return c.updateAnnouncement.CallUnary(ctx, req)
}

// ToggleAnnouncement calls api.cms.v1.AnnouncementsService.ToggleAnnouncement.
func (c *announcementsServiceClient) ToggleAnnouncement(ctx context.Context, req *connect.Request[v1.ToggleAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error) {
	return c.toggleAnnouncement.CallUnary(ctx, req)
}

// GetAnnouncement calls api.cms.v1.AnnouncementsService.GetAnnouncement.
func (c *announcementsServiceClient) GetAnnouncement(ctx context.Context, req *connect.Request[v1.GetAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error) {
	return c.getAnnouncement.CallUnary(ctx, req)
}

// AnnouncementsServiceHandler is an implementation of the api.cms.v1.AnnouncementsService service.
type AnnouncementsServiceHandler interface {
	AddAnnouncement(context.Context, *connect.Request[v1.AddAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error)
	UpdateAnnouncement(context.Context, *connect.Request[v1.UpdateAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error)
	ToggleAnnouncement(context.Context, *connect.Request[v1.ToggleAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error)
	GetAnnouncement(context.Context, *connect.Request[v1.GetAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error)
}

// NewAnnouncementsServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewAnnouncementsServiceHandler(svc AnnouncementsServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	announcementsServiceMethods := v1.File_cms_v1_announcements_proto.Services().ByName("AnnouncementsService").Methods()
	announcementsServiceAddAnnouncementHandler := connect.NewUnaryHandler(
		AnnouncementsServiceAddAnnouncementProcedure,
		svc.AddAnnouncement,
		connect.WithSchema(announcementsServiceMethods.ByName("AddAnnouncement")),
		connect.WithHandlerOptions(opts...),
	)
	announcementsServiceUpdateAnnouncementHandler := connect.NewUnaryHandler(
		AnnouncementsServiceUpdateAnnouncementProcedure,
		svc.UpdateAnnouncement,
		connect.WithSchema(announcementsServiceMethods.ByName("UpdateAnnouncement")),
		connect.WithHandlerOptions(opts...),
	)
	announcementsServiceToggleAnnouncementHandler := connect.NewUnaryHandler(
		AnnouncementsServiceToggleAnnouncementProcedure,
		svc.ToggleAnnouncement,
		connect.WithSchema(announcementsServiceMethods.ByName("ToggleAnnouncement")),
		connect.WithHandlerOptions(opts...),
	)
	announcementsServiceGetAnnouncementHandler := connect.NewUnaryHandler(
		AnnouncementsServiceGetAnnouncementProcedure,
		svc.GetAnnouncement,
		connect.WithSchema(announcementsServiceMethods.ByName("GetAnnouncement")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.cms.v1.AnnouncementsService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case AnnouncementsServiceAddAnnouncementProcedure:
			announcementsServiceAddAnnouncementHandler.ServeHTTP(w, r)
		case AnnouncementsServiceUpdateAnnouncementProcedure:
			announcementsServiceUpdateAnnouncementHandler.ServeHTTP(w, r)
		case AnnouncementsServiceToggleAnnouncementProcedure:
			announcementsServiceToggleAnnouncementHandler.ServeHTTP(w, r)
		case AnnouncementsServiceGetAnnouncementProcedure:
			announcementsServiceGetAnnouncementHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedAnnouncementsServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedAnnouncementsServiceHandler struct{}

func (UnimplementedAnnouncementsServiceHandler) AddAnnouncement(context.Context, *connect.Request[v1.AddAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.AnnouncementsService.AddAnnouncement is not implemented"))
}

func (UnimplementedAnnouncementsServiceHandler) UpdateAnnouncement(context.Context, *connect.Request[v1.UpdateAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.AnnouncementsService.UpdateAnnouncement is not implemented"))
}

func (UnimplementedAnnouncementsServiceHandler) ToggleAnnouncement(context.Context, *connect.Request[v1.ToggleAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.AnnouncementsService.ToggleAnnouncement is not implemented"))
}

func (UnimplementedAnnouncementsServiceHandler) GetAnnouncement(context.Context, *connect.Request[v1.GetAnnouncementRequest]) (*connect.Response[v1.AnnouncementResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.AnnouncementsService.GetAnnouncement is not implemented"))
}
