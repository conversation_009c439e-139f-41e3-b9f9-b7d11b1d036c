// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: cms/v1/faqs.proto

package cmsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/cms/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// FaqServiceName is the fully-qualified name of the FaqService service.
	FaqServiceName = "api.cms.v1.FaqService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// FaqServiceAddFaqProcedure is the fully-qualified name of the FaqService's AddFaq RPC.
	FaqServiceAddFaqProcedure = "/api.cms.v1.FaqService/AddFaq"
	// FaqServiceGetAllFaqsProcedure is the fully-qualified name of the FaqService's GetAllFaqs RPC.
	FaqServiceGetAllFaqsProcedure = "/api.cms.v1.FaqService/GetAllFaqs"
	// FaqServiceGetFaqProcedure is the fully-qualified name of the FaqService's GetFaq RPC.
	FaqServiceGetFaqProcedure = "/api.cms.v1.FaqService/GetFaq"
	// FaqServiceUpdateFaqProcedure is the fully-qualified name of the FaqService's UpdateFaq RPC.
	FaqServiceUpdateFaqProcedure = "/api.cms.v1.FaqService/UpdateFaq"
	// FaqServiceDeleteFaqProcedure is the fully-qualified name of the FaqService's DeleteFaq RPC.
	FaqServiceDeleteFaqProcedure = "/api.cms.v1.FaqService/DeleteFaq"
	// FaqServiceToogleFaqStatusProcedure is the fully-qualified name of the FaqService's
	// ToogleFaqStatus RPC.
	FaqServiceToogleFaqStatusProcedure = "/api.cms.v1.FaqService/ToogleFaqStatus"
	// FaqServiceGetAllActiveFaqsProcedure is the fully-qualified name of the FaqService's
	// GetAllActiveFaqs RPC.
	FaqServiceGetAllActiveFaqsProcedure = "/api.cms.v1.FaqService/GetAllActiveFaqs"
)

// FaqServiceClient is a client for the api.cms.v1.FaqService service.
type FaqServiceClient interface {
	AddFaq(context.Context, *connect.Request[v1.AddFaqRequest]) (*connect.Response[v1.AddFaqResponse], error)
	GetAllFaqs(context.Context, *connect.Request[v1.GetAllFaqsRequest]) (*connect.Response[v1.GetAllFaqsResponse], error)
	GetFaq(context.Context, *connect.Request[v1.GetFaqRequest]) (*connect.Response[v1.GetFaqResponse], error)
	UpdateFaq(context.Context, *connect.Request[v1.UpdateFaqRequest]) (*connect.Response[v1.UpdateFaqResponse], error)
	DeleteFaq(context.Context, *connect.Request[v1.DeleteFaqRequest]) (*connect.Response[v1.DeleteFaqResponse], error)
	ToogleFaqStatus(context.Context, *connect.Request[v1.ToogleFaqStatusRequest]) (*connect.Response[v1.ToogleFaqStatusResponse], error)
	GetAllActiveFaqs(context.Context, *connect.Request[v1.GetAllActiveFaqsRequest]) (*connect.Response[v1.GetAllFaqsResponse], error)
}

// NewFaqServiceClient constructs a client for the api.cms.v1.FaqService service. By default, it
// uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewFaqServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) FaqServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	faqServiceMethods := v1.File_cms_v1_faqs_proto.Services().ByName("FaqService").Methods()
	return &faqServiceClient{
		addFaq: connect.NewClient[v1.AddFaqRequest, v1.AddFaqResponse](
			httpClient,
			baseURL+FaqServiceAddFaqProcedure,
			connect.WithSchema(faqServiceMethods.ByName("AddFaq")),
			connect.WithClientOptions(opts...),
		),
		getAllFaqs: connect.NewClient[v1.GetAllFaqsRequest, v1.GetAllFaqsResponse](
			httpClient,
			baseURL+FaqServiceGetAllFaqsProcedure,
			connect.WithSchema(faqServiceMethods.ByName("GetAllFaqs")),
			connect.WithClientOptions(opts...),
		),
		getFaq: connect.NewClient[v1.GetFaqRequest, v1.GetFaqResponse](
			httpClient,
			baseURL+FaqServiceGetFaqProcedure,
			connect.WithSchema(faqServiceMethods.ByName("GetFaq")),
			connect.WithClientOptions(opts...),
		),
		updateFaq: connect.NewClient[v1.UpdateFaqRequest, v1.UpdateFaqResponse](
			httpClient,
			baseURL+FaqServiceUpdateFaqProcedure,
			connect.WithSchema(faqServiceMethods.ByName("UpdateFaq")),
			connect.WithClientOptions(opts...),
		),
		deleteFaq: connect.NewClient[v1.DeleteFaqRequest, v1.DeleteFaqResponse](
			httpClient,
			baseURL+FaqServiceDeleteFaqProcedure,
			connect.WithSchema(faqServiceMethods.ByName("DeleteFaq")),
			connect.WithClientOptions(opts...),
		),
		toogleFaqStatus: connect.NewClient[v1.ToogleFaqStatusRequest, v1.ToogleFaqStatusResponse](
			httpClient,
			baseURL+FaqServiceToogleFaqStatusProcedure,
			connect.WithSchema(faqServiceMethods.ByName("ToogleFaqStatus")),
			connect.WithClientOptions(opts...),
		),
		getAllActiveFaqs: connect.NewClient[v1.GetAllActiveFaqsRequest, v1.GetAllFaqsResponse](
			httpClient,
			baseURL+FaqServiceGetAllActiveFaqsProcedure,
			connect.WithSchema(faqServiceMethods.ByName("GetAllActiveFaqs")),
			connect.WithClientOptions(opts...),
		),
	}
}

// faqServiceClient implements FaqServiceClient.
type faqServiceClient struct {
	addFaq           *connect.Client[v1.AddFaqRequest, v1.AddFaqResponse]
	getAllFaqs       *connect.Client[v1.GetAllFaqsRequest, v1.GetAllFaqsResponse]
	getFaq           *connect.Client[v1.GetFaqRequest, v1.GetFaqResponse]
	updateFaq        *connect.Client[v1.UpdateFaqRequest, v1.UpdateFaqResponse]
	deleteFaq        *connect.Client[v1.DeleteFaqRequest, v1.DeleteFaqResponse]
	toogleFaqStatus  *connect.Client[v1.ToogleFaqStatusRequest, v1.ToogleFaqStatusResponse]
	getAllActiveFaqs *connect.Client[v1.GetAllActiveFaqsRequest, v1.GetAllFaqsResponse]
}

// AddFaq calls api.cms.v1.FaqService.AddFaq.
func (c *faqServiceClient) AddFaq(ctx context.Context, req *connect.Request[v1.AddFaqRequest]) (*connect.Response[v1.AddFaqResponse], error) {
	return c.addFaq.CallUnary(ctx, req)
}

// GetAllFaqs calls api.cms.v1.FaqService.GetAllFaqs.
func (c *faqServiceClient) GetAllFaqs(ctx context.Context, req *connect.Request[v1.GetAllFaqsRequest]) (*connect.Response[v1.GetAllFaqsResponse], error) {
	return c.getAllFaqs.CallUnary(ctx, req)
}

// GetFaq calls api.cms.v1.FaqService.GetFaq.
func (c *faqServiceClient) GetFaq(ctx context.Context, req *connect.Request[v1.GetFaqRequest]) (*connect.Response[v1.GetFaqResponse], error) {
	return c.getFaq.CallUnary(ctx, req)
}

// UpdateFaq calls api.cms.v1.FaqService.UpdateFaq.
func (c *faqServiceClient) UpdateFaq(ctx context.Context, req *connect.Request[v1.UpdateFaqRequest]) (*connect.Response[v1.UpdateFaqResponse], error) {
	return c.updateFaq.CallUnary(ctx, req)
}

// DeleteFaq calls api.cms.v1.FaqService.DeleteFaq.
func (c *faqServiceClient) DeleteFaq(ctx context.Context, req *connect.Request[v1.DeleteFaqRequest]) (*connect.Response[v1.DeleteFaqResponse], error) {
	return c.deleteFaq.CallUnary(ctx, req)
}

// ToogleFaqStatus calls api.cms.v1.FaqService.ToogleFaqStatus.
func (c *faqServiceClient) ToogleFaqStatus(ctx context.Context, req *connect.Request[v1.ToogleFaqStatusRequest]) (*connect.Response[v1.ToogleFaqStatusResponse], error) {
	return c.toogleFaqStatus.CallUnary(ctx, req)
}

// GetAllActiveFaqs calls api.cms.v1.FaqService.GetAllActiveFaqs.
func (c *faqServiceClient) GetAllActiveFaqs(ctx context.Context, req *connect.Request[v1.GetAllActiveFaqsRequest]) (*connect.Response[v1.GetAllFaqsResponse], error) {
	return c.getAllActiveFaqs.CallUnary(ctx, req)
}

// FaqServiceHandler is an implementation of the api.cms.v1.FaqService service.
type FaqServiceHandler interface {
	AddFaq(context.Context, *connect.Request[v1.AddFaqRequest]) (*connect.Response[v1.AddFaqResponse], error)
	GetAllFaqs(context.Context, *connect.Request[v1.GetAllFaqsRequest]) (*connect.Response[v1.GetAllFaqsResponse], error)
	GetFaq(context.Context, *connect.Request[v1.GetFaqRequest]) (*connect.Response[v1.GetFaqResponse], error)
	UpdateFaq(context.Context, *connect.Request[v1.UpdateFaqRequest]) (*connect.Response[v1.UpdateFaqResponse], error)
	DeleteFaq(context.Context, *connect.Request[v1.DeleteFaqRequest]) (*connect.Response[v1.DeleteFaqResponse], error)
	ToogleFaqStatus(context.Context, *connect.Request[v1.ToogleFaqStatusRequest]) (*connect.Response[v1.ToogleFaqStatusResponse], error)
	GetAllActiveFaqs(context.Context, *connect.Request[v1.GetAllActiveFaqsRequest]) (*connect.Response[v1.GetAllFaqsResponse], error)
}

// NewFaqServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewFaqServiceHandler(svc FaqServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	faqServiceMethods := v1.File_cms_v1_faqs_proto.Services().ByName("FaqService").Methods()
	faqServiceAddFaqHandler := connect.NewUnaryHandler(
		FaqServiceAddFaqProcedure,
		svc.AddFaq,
		connect.WithSchema(faqServiceMethods.ByName("AddFaq")),
		connect.WithHandlerOptions(opts...),
	)
	faqServiceGetAllFaqsHandler := connect.NewUnaryHandler(
		FaqServiceGetAllFaqsProcedure,
		svc.GetAllFaqs,
		connect.WithSchema(faqServiceMethods.ByName("GetAllFaqs")),
		connect.WithHandlerOptions(opts...),
	)
	faqServiceGetFaqHandler := connect.NewUnaryHandler(
		FaqServiceGetFaqProcedure,
		svc.GetFaq,
		connect.WithSchema(faqServiceMethods.ByName("GetFaq")),
		connect.WithHandlerOptions(opts...),
	)
	faqServiceUpdateFaqHandler := connect.NewUnaryHandler(
		FaqServiceUpdateFaqProcedure,
		svc.UpdateFaq,
		connect.WithSchema(faqServiceMethods.ByName("UpdateFaq")),
		connect.WithHandlerOptions(opts...),
	)
	faqServiceDeleteFaqHandler := connect.NewUnaryHandler(
		FaqServiceDeleteFaqProcedure,
		svc.DeleteFaq,
		connect.WithSchema(faqServiceMethods.ByName("DeleteFaq")),
		connect.WithHandlerOptions(opts...),
	)
	faqServiceToogleFaqStatusHandler := connect.NewUnaryHandler(
		FaqServiceToogleFaqStatusProcedure,
		svc.ToogleFaqStatus,
		connect.WithSchema(faqServiceMethods.ByName("ToogleFaqStatus")),
		connect.WithHandlerOptions(opts...),
	)
	faqServiceGetAllActiveFaqsHandler := connect.NewUnaryHandler(
		FaqServiceGetAllActiveFaqsProcedure,
		svc.GetAllActiveFaqs,
		connect.WithSchema(faqServiceMethods.ByName("GetAllActiveFaqs")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.cms.v1.FaqService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case FaqServiceAddFaqProcedure:
			faqServiceAddFaqHandler.ServeHTTP(w, r)
		case FaqServiceGetAllFaqsProcedure:
			faqServiceGetAllFaqsHandler.ServeHTTP(w, r)
		case FaqServiceGetFaqProcedure:
			faqServiceGetFaqHandler.ServeHTTP(w, r)
		case FaqServiceUpdateFaqProcedure:
			faqServiceUpdateFaqHandler.ServeHTTP(w, r)
		case FaqServiceDeleteFaqProcedure:
			faqServiceDeleteFaqHandler.ServeHTTP(w, r)
		case FaqServiceToogleFaqStatusProcedure:
			faqServiceToogleFaqStatusHandler.ServeHTTP(w, r)
		case FaqServiceGetAllActiveFaqsProcedure:
			faqServiceGetAllActiveFaqsHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedFaqServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedFaqServiceHandler struct{}

func (UnimplementedFaqServiceHandler) AddFaq(context.Context, *connect.Request[v1.AddFaqRequest]) (*connect.Response[v1.AddFaqResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.FaqService.AddFaq is not implemented"))
}

func (UnimplementedFaqServiceHandler) GetAllFaqs(context.Context, *connect.Request[v1.GetAllFaqsRequest]) (*connect.Response[v1.GetAllFaqsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.FaqService.GetAllFaqs is not implemented"))
}

func (UnimplementedFaqServiceHandler) GetFaq(context.Context, *connect.Request[v1.GetFaqRequest]) (*connect.Response[v1.GetFaqResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.FaqService.GetFaq is not implemented"))
}

func (UnimplementedFaqServiceHandler) UpdateFaq(context.Context, *connect.Request[v1.UpdateFaqRequest]) (*connect.Response[v1.UpdateFaqResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.FaqService.UpdateFaq is not implemented"))
}

func (UnimplementedFaqServiceHandler) DeleteFaq(context.Context, *connect.Request[v1.DeleteFaqRequest]) (*connect.Response[v1.DeleteFaqResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.FaqService.DeleteFaq is not implemented"))
}

func (UnimplementedFaqServiceHandler) ToogleFaqStatus(context.Context, *connect.Request[v1.ToogleFaqStatusRequest]) (*connect.Response[v1.ToogleFaqStatusResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.FaqService.ToogleFaqStatus is not implemented"))
}

func (UnimplementedFaqServiceHandler) GetAllActiveFaqs(context.Context, *connect.Request[v1.GetAllActiveFaqsRequest]) (*connect.Response[v1.GetAllFaqsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.FaqService.GetAllActiveFaqs is not implemented"))
}
