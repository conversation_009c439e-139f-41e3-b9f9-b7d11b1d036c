// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: cms/v1/announcements.proto

package cmsv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddAnnouncementRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Image         string                 `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty" validate:"required"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddAnnouncementRequest) Reset() {
	*x = AddAnnouncementRequest{}
	mi := &file_cms_v1_announcements_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAnnouncementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAnnouncementRequest) ProtoMessage() {}

func (x *AddAnnouncementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_announcements_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAnnouncementRequest.ProtoReflect.Descriptor instead.
func (*AddAnnouncementRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_announcements_proto_rawDescGZIP(), []int{0}
}

func (x *AddAnnouncementRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *AddAnnouncementRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type AnnouncementResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Announcement          `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnnouncementResponse) Reset() {
	*x = AnnouncementResponse{}
	mi := &file_cms_v1_announcements_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnouncementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnouncementResponse) ProtoMessage() {}

func (x *AnnouncementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_announcements_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnouncementResponse.ProtoReflect.Descriptor instead.
func (*AnnouncementResponse) Descriptor() ([]byte, []int) {
	return file_cms_v1_announcements_proto_rawDescGZIP(), []int{1}
}

func (x *AnnouncementResponse) GetData() *Announcement {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetAnnouncementRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAnnouncementRequest) Reset() {
	*x = GetAnnouncementRequest{}
	mi := &file_cms_v1_announcements_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAnnouncementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnnouncementRequest) ProtoMessage() {}

func (x *GetAnnouncementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_announcements_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnnouncementRequest.ProtoReflect.Descriptor instead.
func (*GetAnnouncementRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_announcements_proto_rawDescGZIP(), []int{2}
}

type Announcement struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Image         string                 `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Active        bool                   `protobuf:"varint,4,opt,name=active,proto3" json:"active,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Announcement) Reset() {
	*x = Announcement{}
	mi := &file_cms_v1_announcements_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Announcement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Announcement) ProtoMessage() {}

func (x *Announcement) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_announcements_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Announcement.ProtoReflect.Descriptor instead.
func (*Announcement) Descriptor() ([]byte, []int) {
	return file_cms_v1_announcements_proto_rawDescGZIP(), []int{3}
}

func (x *Announcement) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Announcement) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Announcement) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Announcement) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *Announcement) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type UpdateAnnouncementRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Image         string                 `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty" validate:"required"`
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAnnouncementRequest) Reset() {
	*x = UpdateAnnouncementRequest{}
	mi := &file_cms_v1_announcements_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAnnouncementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAnnouncementRequest) ProtoMessage() {}

func (x *UpdateAnnouncementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_announcements_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAnnouncementRequest.ProtoReflect.Descriptor instead.
func (*UpdateAnnouncementRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_announcements_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAnnouncementRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateAnnouncementRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdateAnnouncementRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ToggleAnnouncementRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ToggleAnnouncementRequest) Reset() {
	*x = ToggleAnnouncementRequest{}
	mi := &file_cms_v1_announcements_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ToggleAnnouncementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToggleAnnouncementRequest) ProtoMessage() {}

func (x *ToggleAnnouncementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cms_v1_announcements_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToggleAnnouncementRequest.ProtoReflect.Descriptor instead.
func (*ToggleAnnouncementRequest) Descriptor() ([]byte, []int) {
	return file_cms_v1_announcements_proto_rawDescGZIP(), []int{5}
}

func (x *ToggleAnnouncementRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_cms_v1_announcements_proto protoreflect.FileDescriptor

const file_cms_v1_announcements_proto_rawDesc = "" +
	"\n" +
	"\x1acms/v1/announcements.proto\x12\n" +
	"api.cms.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"P\n" +
	"\x16AddAnnouncementRequest\x12\x14\n" +
	"\x05image\x18\x01 \x01(\tR\x05image\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\"D\n" +
	"\x14AnnouncementResponse\x12,\n" +
	"\x04data\x18\x01 \x01(\v2\x18.api.cms.v1.AnnouncementR\x04data\"\x18\n" +
	"\x16GetAnnouncementRequest\"\xa1\x01\n" +
	"\fAnnouncement\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05image\x18\x02 \x01(\tR\x05image\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\x12\x16\n" +
	"\x06active\x18\x04 \x01(\bR\x06active\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"[\n" +
	"\x19UpdateAnnouncementRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05image\x18\x02 \x01(\tR\x05image\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\"+\n" +
	"\x19ToggleAnnouncementRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id2\xa6\x03\n" +
	"\x14AnnouncementsService\x12a\n" +
	"\x0fAddAnnouncement\x12\".api.cms.v1.AddAnnouncementRequest\x1a .api.cms.v1.AnnouncementResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12g\n" +
	"\x12UpdateAnnouncement\x12%.api.cms.v1.UpdateAnnouncementRequest\x1a .api.cms.v1.AnnouncementResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12g\n" +
	"\x12ToggleAnnouncement\x12%.api.cms.v1.ToggleAnnouncementRequest\x1a .api.cms.v1.AnnouncementResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12Y\n" +
	"\x0fGetAnnouncement\x12\".api.cms.v1.GetAnnouncementRequest\x1a .api.cms.v1.AnnouncementResponse\"\x00B,Z*github.com/nsp-inc/vtuber/api/cms/v1;cmsv1b\x06proto3"

var (
	file_cms_v1_announcements_proto_rawDescOnce sync.Once
	file_cms_v1_announcements_proto_rawDescData []byte
)

func file_cms_v1_announcements_proto_rawDescGZIP() []byte {
	file_cms_v1_announcements_proto_rawDescOnce.Do(func() {
		file_cms_v1_announcements_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cms_v1_announcements_proto_rawDesc), len(file_cms_v1_announcements_proto_rawDesc)))
	})
	return file_cms_v1_announcements_proto_rawDescData
}

var file_cms_v1_announcements_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_cms_v1_announcements_proto_goTypes = []any{
	(*AddAnnouncementRequest)(nil),    // 0: api.cms.v1.AddAnnouncementRequest
	(*AnnouncementResponse)(nil),      // 1: api.cms.v1.AnnouncementResponse
	(*GetAnnouncementRequest)(nil),    // 2: api.cms.v1.GetAnnouncementRequest
	(*Announcement)(nil),              // 3: api.cms.v1.Announcement
	(*UpdateAnnouncementRequest)(nil), // 4: api.cms.v1.UpdateAnnouncementRequest
	(*ToggleAnnouncementRequest)(nil), // 5: api.cms.v1.ToggleAnnouncementRequest
	(*timestamppb.Timestamp)(nil),     // 6: google.protobuf.Timestamp
}
var file_cms_v1_announcements_proto_depIdxs = []int32{
	3, // 0: api.cms.v1.AnnouncementResponse.data:type_name -> api.cms.v1.Announcement
	6, // 1: api.cms.v1.Announcement.created_at:type_name -> google.protobuf.Timestamp
	0, // 2: api.cms.v1.AnnouncementsService.AddAnnouncement:input_type -> api.cms.v1.AddAnnouncementRequest
	4, // 3: api.cms.v1.AnnouncementsService.UpdateAnnouncement:input_type -> api.cms.v1.UpdateAnnouncementRequest
	5, // 4: api.cms.v1.AnnouncementsService.ToggleAnnouncement:input_type -> api.cms.v1.ToggleAnnouncementRequest
	2, // 5: api.cms.v1.AnnouncementsService.GetAnnouncement:input_type -> api.cms.v1.GetAnnouncementRequest
	1, // 6: api.cms.v1.AnnouncementsService.AddAnnouncement:output_type -> api.cms.v1.AnnouncementResponse
	1, // 7: api.cms.v1.AnnouncementsService.UpdateAnnouncement:output_type -> api.cms.v1.AnnouncementResponse
	1, // 8: api.cms.v1.AnnouncementsService.ToggleAnnouncement:output_type -> api.cms.v1.AnnouncementResponse
	1, // 9: api.cms.v1.AnnouncementsService.GetAnnouncement:output_type -> api.cms.v1.AnnouncementResponse
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_cms_v1_announcements_proto_init() }
func file_cms_v1_announcements_proto_init() {
	if File_cms_v1_announcements_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cms_v1_announcements_proto_rawDesc), len(file_cms_v1_announcements_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cms_v1_announcements_proto_goTypes,
		DependencyIndexes: file_cms_v1_announcements_proto_depIdxs,
		MessageInfos:      file_cms_v1_announcements_proto_msgTypes,
	}.Build()
	File_cms_v1_announcements_proto = out.File
	file_cms_v1_announcements_proto_goTypes = nil
	file_cms_v1_announcements_proto_depIdxs = nil
}
