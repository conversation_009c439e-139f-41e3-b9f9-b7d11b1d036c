// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: socials/v1/favoriitevtuber.proto

package socialsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/socials/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// FavoriteVtuberServiceName is the fully-qualified name of the FavoriteVtuberService service.
	FavoriteVtuberServiceName = "api.socials.v1.FavoriteVtuberService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// FavoriteVtuberServiceAddFavoriteVtuberProcedure is the fully-qualified name of the
	// FavoriteVtuberService's AddFavoriteVtuber RPC.
	FavoriteVtuberServiceAddFavoriteVtuberProcedure = "/api.socials.v1.FavoriteVtuberService/AddFavoriteVtuber"
	// FavoriteVtuberServiceGetAllFavoriteVtuberProcedure is the fully-qualified name of the
	// FavoriteVtuberService's GetAllFavoriteVtuber RPC.
	FavoriteVtuberServiceGetAllFavoriteVtuberProcedure = "/api.socials.v1.FavoriteVtuberService/GetAllFavoriteVtuber"
	// FavoriteVtuberServiceDeleteFavoriteVtuberProcedure is the fully-qualified name of the
	// FavoriteVtuberService's DeleteFavoriteVtuber RPC.
	FavoriteVtuberServiceDeleteFavoriteVtuberProcedure = "/api.socials.v1.FavoriteVtuberService/DeleteFavoriteVtuber"
)

// FavoriteVtuberServiceClient is a client for the api.socials.v1.FavoriteVtuberService service.
type FavoriteVtuberServiceClient interface {
	AddFavoriteVtuber(context.Context, *connect.Request[v1.AddFavoriteVtuberRequest]) (*connect.Response[v1.AddFavoriteVtuberResponse], error)
	GetAllFavoriteVtuber(context.Context, *connect.Request[v1.GetAllFavoriteVtuberRequest]) (*connect.Response[v1.GetAllFavoriteVtuberResponse], error)
	DeleteFavoriteVtuber(context.Context, *connect.Request[v1.DeleteFavoriteVtuberRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewFavoriteVtuberServiceClient constructs a client for the api.socials.v1.FavoriteVtuberService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewFavoriteVtuberServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) FavoriteVtuberServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	favoriteVtuberServiceMethods := v1.File_socials_v1_favoriitevtuber_proto.Services().ByName("FavoriteVtuberService").Methods()
	return &favoriteVtuberServiceClient{
		addFavoriteVtuber: connect.NewClient[v1.AddFavoriteVtuberRequest, v1.AddFavoriteVtuberResponse](
			httpClient,
			baseURL+FavoriteVtuberServiceAddFavoriteVtuberProcedure,
			connect.WithSchema(favoriteVtuberServiceMethods.ByName("AddFavoriteVtuber")),
			connect.WithClientOptions(opts...),
		),
		getAllFavoriteVtuber: connect.NewClient[v1.GetAllFavoriteVtuberRequest, v1.GetAllFavoriteVtuberResponse](
			httpClient,
			baseURL+FavoriteVtuberServiceGetAllFavoriteVtuberProcedure,
			connect.WithSchema(favoriteVtuberServiceMethods.ByName("GetAllFavoriteVtuber")),
			connect.WithClientOptions(opts...),
		),
		deleteFavoriteVtuber: connect.NewClient[v1.DeleteFavoriteVtuberRequest, v11.GenericResponse](
			httpClient,
			baseURL+FavoriteVtuberServiceDeleteFavoriteVtuberProcedure,
			connect.WithSchema(favoriteVtuberServiceMethods.ByName("DeleteFavoriteVtuber")),
			connect.WithClientOptions(opts...),
		),
	}
}

// favoriteVtuberServiceClient implements FavoriteVtuberServiceClient.
type favoriteVtuberServiceClient struct {
	addFavoriteVtuber    *connect.Client[v1.AddFavoriteVtuberRequest, v1.AddFavoriteVtuberResponse]
	getAllFavoriteVtuber *connect.Client[v1.GetAllFavoriteVtuberRequest, v1.GetAllFavoriteVtuberResponse]
	deleteFavoriteVtuber *connect.Client[v1.DeleteFavoriteVtuberRequest, v11.GenericResponse]
}

// AddFavoriteVtuber calls api.socials.v1.FavoriteVtuberService.AddFavoriteVtuber.
func (c *favoriteVtuberServiceClient) AddFavoriteVtuber(ctx context.Context, req *connect.Request[v1.AddFavoriteVtuberRequest]) (*connect.Response[v1.AddFavoriteVtuberResponse], error) {
	return c.addFavoriteVtuber.CallUnary(ctx, req)
}

// GetAllFavoriteVtuber calls api.socials.v1.FavoriteVtuberService.GetAllFavoriteVtuber.
func (c *favoriteVtuberServiceClient) GetAllFavoriteVtuber(ctx context.Context, req *connect.Request[v1.GetAllFavoriteVtuberRequest]) (*connect.Response[v1.GetAllFavoriteVtuberResponse], error) {
	return c.getAllFavoriteVtuber.CallUnary(ctx, req)
}

// DeleteFavoriteVtuber calls api.socials.v1.FavoriteVtuberService.DeleteFavoriteVtuber.
func (c *favoriteVtuberServiceClient) DeleteFavoriteVtuber(ctx context.Context, req *connect.Request[v1.DeleteFavoriteVtuberRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.deleteFavoriteVtuber.CallUnary(ctx, req)
}

// FavoriteVtuberServiceHandler is an implementation of the api.socials.v1.FavoriteVtuberService
// service.
type FavoriteVtuberServiceHandler interface {
	AddFavoriteVtuber(context.Context, *connect.Request[v1.AddFavoriteVtuberRequest]) (*connect.Response[v1.AddFavoriteVtuberResponse], error)
	GetAllFavoriteVtuber(context.Context, *connect.Request[v1.GetAllFavoriteVtuberRequest]) (*connect.Response[v1.GetAllFavoriteVtuberResponse], error)
	DeleteFavoriteVtuber(context.Context, *connect.Request[v1.DeleteFavoriteVtuberRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewFavoriteVtuberServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewFavoriteVtuberServiceHandler(svc FavoriteVtuberServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	favoriteVtuberServiceMethods := v1.File_socials_v1_favoriitevtuber_proto.Services().ByName("FavoriteVtuberService").Methods()
	favoriteVtuberServiceAddFavoriteVtuberHandler := connect.NewUnaryHandler(
		FavoriteVtuberServiceAddFavoriteVtuberProcedure,
		svc.AddFavoriteVtuber,
		connect.WithSchema(favoriteVtuberServiceMethods.ByName("AddFavoriteVtuber")),
		connect.WithHandlerOptions(opts...),
	)
	favoriteVtuberServiceGetAllFavoriteVtuberHandler := connect.NewUnaryHandler(
		FavoriteVtuberServiceGetAllFavoriteVtuberProcedure,
		svc.GetAllFavoriteVtuber,
		connect.WithSchema(favoriteVtuberServiceMethods.ByName("GetAllFavoriteVtuber")),
		connect.WithHandlerOptions(opts...),
	)
	favoriteVtuberServiceDeleteFavoriteVtuberHandler := connect.NewUnaryHandler(
		FavoriteVtuberServiceDeleteFavoriteVtuberProcedure,
		svc.DeleteFavoriteVtuber,
		connect.WithSchema(favoriteVtuberServiceMethods.ByName("DeleteFavoriteVtuber")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.socials.v1.FavoriteVtuberService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case FavoriteVtuberServiceAddFavoriteVtuberProcedure:
			favoriteVtuberServiceAddFavoriteVtuberHandler.ServeHTTP(w, r)
		case FavoriteVtuberServiceGetAllFavoriteVtuberProcedure:
			favoriteVtuberServiceGetAllFavoriteVtuberHandler.ServeHTTP(w, r)
		case FavoriteVtuberServiceDeleteFavoriteVtuberProcedure:
			favoriteVtuberServiceDeleteFavoriteVtuberHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedFavoriteVtuberServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedFavoriteVtuberServiceHandler struct{}

func (UnimplementedFavoriteVtuberServiceHandler) AddFavoriteVtuber(context.Context, *connect.Request[v1.AddFavoriteVtuberRequest]) (*connect.Response[v1.AddFavoriteVtuberResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.socials.v1.FavoriteVtuberService.AddFavoriteVtuber is not implemented"))
}

func (UnimplementedFavoriteVtuberServiceHandler) GetAllFavoriteVtuber(context.Context, *connect.Request[v1.GetAllFavoriteVtuberRequest]) (*connect.Response[v1.GetAllFavoriteVtuberResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.socials.v1.FavoriteVtuberService.GetAllFavoriteVtuber is not implemented"))
}

func (UnimplementedFavoriteVtuberServiceHandler) DeleteFavoriteVtuber(context.Context, *connect.Request[v1.DeleteFavoriteVtuberRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.socials.v1.FavoriteVtuberService.DeleteFavoriteVtuber is not implemented"))
}
