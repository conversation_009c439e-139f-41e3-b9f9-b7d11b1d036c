// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: socials/v1/favoritecampaign.proto

package socialsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/socials/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// FavoriteCampaignServiceName is the fully-qualified name of the FavoriteCampaignService service.
	FavoriteCampaignServiceName = "api.socials.v1.FavoriteCampaignService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// FavoriteCampaignServiceAddFavoriteCampaignProcedure is the fully-qualified name of the
	// FavoriteCampaignService's AddFavoriteCampaign RPC.
	FavoriteCampaignServiceAddFavoriteCampaignProcedure = "/api.socials.v1.FavoriteCampaignService/AddFavoriteCampaign"
	// FavoriteCampaignServiceGetAllFavoriteCampaignProcedure is the fully-qualified name of the
	// FavoriteCampaignService's GetAllFavoriteCampaign RPC.
	FavoriteCampaignServiceGetAllFavoriteCampaignProcedure = "/api.socials.v1.FavoriteCampaignService/GetAllFavoriteCampaign"
	// FavoriteCampaignServiceDeleteFavoriteCampaignProcedure is the fully-qualified name of the
	// FavoriteCampaignService's DeleteFavoriteCampaign RPC.
	FavoriteCampaignServiceDeleteFavoriteCampaignProcedure = "/api.socials.v1.FavoriteCampaignService/DeleteFavoriteCampaign"
)

// FavoriteCampaignServiceClient is a client for the api.socials.v1.FavoriteCampaignService service.
type FavoriteCampaignServiceClient interface {
	AddFavoriteCampaign(context.Context, *connect.Request[v1.AddFavoriteCampaignRequest]) (*connect.Response[v1.AddFavoriteCampaignResponse], error)
	GetAllFavoriteCampaign(context.Context, *connect.Request[v1.GetAllFavoriteCampaignRequest]) (*connect.Response[v1.GetAllFavoriteCampaignResponse], error)
	DeleteFavoriteCampaign(context.Context, *connect.Request[v1.DeleteFavoriteCampaignRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewFavoriteCampaignServiceClient constructs a client for the
// api.socials.v1.FavoriteCampaignService service. By default, it uses the Connect protocol with the
// binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To use the
// gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewFavoriteCampaignServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) FavoriteCampaignServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	favoriteCampaignServiceMethods := v1.File_socials_v1_favoritecampaign_proto.Services().ByName("FavoriteCampaignService").Methods()
	return &favoriteCampaignServiceClient{
		addFavoriteCampaign: connect.NewClient[v1.AddFavoriteCampaignRequest, v1.AddFavoriteCampaignResponse](
			httpClient,
			baseURL+FavoriteCampaignServiceAddFavoriteCampaignProcedure,
			connect.WithSchema(favoriteCampaignServiceMethods.ByName("AddFavoriteCampaign")),
			connect.WithClientOptions(opts...),
		),
		getAllFavoriteCampaign: connect.NewClient[v1.GetAllFavoriteCampaignRequest, v1.GetAllFavoriteCampaignResponse](
			httpClient,
			baseURL+FavoriteCampaignServiceGetAllFavoriteCampaignProcedure,
			connect.WithSchema(favoriteCampaignServiceMethods.ByName("GetAllFavoriteCampaign")),
			connect.WithClientOptions(opts...),
		),
		deleteFavoriteCampaign: connect.NewClient[v1.DeleteFavoriteCampaignRequest, v11.GenericResponse](
			httpClient,
			baseURL+FavoriteCampaignServiceDeleteFavoriteCampaignProcedure,
			connect.WithSchema(favoriteCampaignServiceMethods.ByName("DeleteFavoriteCampaign")),
			connect.WithClientOptions(opts...),
		),
	}
}

// favoriteCampaignServiceClient implements FavoriteCampaignServiceClient.
type favoriteCampaignServiceClient struct {
	addFavoriteCampaign    *connect.Client[v1.AddFavoriteCampaignRequest, v1.AddFavoriteCampaignResponse]
	getAllFavoriteCampaign *connect.Client[v1.GetAllFavoriteCampaignRequest, v1.GetAllFavoriteCampaignResponse]
	deleteFavoriteCampaign *connect.Client[v1.DeleteFavoriteCampaignRequest, v11.GenericResponse]
}

// AddFavoriteCampaign calls api.socials.v1.FavoriteCampaignService.AddFavoriteCampaign.
func (c *favoriteCampaignServiceClient) AddFavoriteCampaign(ctx context.Context, req *connect.Request[v1.AddFavoriteCampaignRequest]) (*connect.Response[v1.AddFavoriteCampaignResponse], error) {
	return c.addFavoriteCampaign.CallUnary(ctx, req)
}

// GetAllFavoriteCampaign calls api.socials.v1.FavoriteCampaignService.GetAllFavoriteCampaign.
func (c *favoriteCampaignServiceClient) GetAllFavoriteCampaign(ctx context.Context, req *connect.Request[v1.GetAllFavoriteCampaignRequest]) (*connect.Response[v1.GetAllFavoriteCampaignResponse], error) {
	return c.getAllFavoriteCampaign.CallUnary(ctx, req)
}

// DeleteFavoriteCampaign calls api.socials.v1.FavoriteCampaignService.DeleteFavoriteCampaign.
func (c *favoriteCampaignServiceClient) DeleteFavoriteCampaign(ctx context.Context, req *connect.Request[v1.DeleteFavoriteCampaignRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.deleteFavoriteCampaign.CallUnary(ctx, req)
}

// FavoriteCampaignServiceHandler is an implementation of the api.socials.v1.FavoriteCampaignService
// service.
type FavoriteCampaignServiceHandler interface {
	AddFavoriteCampaign(context.Context, *connect.Request[v1.AddFavoriteCampaignRequest]) (*connect.Response[v1.AddFavoriteCampaignResponse], error)
	GetAllFavoriteCampaign(context.Context, *connect.Request[v1.GetAllFavoriteCampaignRequest]) (*connect.Response[v1.GetAllFavoriteCampaignResponse], error)
	DeleteFavoriteCampaign(context.Context, *connect.Request[v1.DeleteFavoriteCampaignRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewFavoriteCampaignServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewFavoriteCampaignServiceHandler(svc FavoriteCampaignServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	favoriteCampaignServiceMethods := v1.File_socials_v1_favoritecampaign_proto.Services().ByName("FavoriteCampaignService").Methods()
	favoriteCampaignServiceAddFavoriteCampaignHandler := connect.NewUnaryHandler(
		FavoriteCampaignServiceAddFavoriteCampaignProcedure,
		svc.AddFavoriteCampaign,
		connect.WithSchema(favoriteCampaignServiceMethods.ByName("AddFavoriteCampaign")),
		connect.WithHandlerOptions(opts...),
	)
	favoriteCampaignServiceGetAllFavoriteCampaignHandler := connect.NewUnaryHandler(
		FavoriteCampaignServiceGetAllFavoriteCampaignProcedure,
		svc.GetAllFavoriteCampaign,
		connect.WithSchema(favoriteCampaignServiceMethods.ByName("GetAllFavoriteCampaign")),
		connect.WithHandlerOptions(opts...),
	)
	favoriteCampaignServiceDeleteFavoriteCampaignHandler := connect.NewUnaryHandler(
		FavoriteCampaignServiceDeleteFavoriteCampaignProcedure,
		svc.DeleteFavoriteCampaign,
		connect.WithSchema(favoriteCampaignServiceMethods.ByName("DeleteFavoriteCampaign")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.socials.v1.FavoriteCampaignService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case FavoriteCampaignServiceAddFavoriteCampaignProcedure:
			favoriteCampaignServiceAddFavoriteCampaignHandler.ServeHTTP(w, r)
		case FavoriteCampaignServiceGetAllFavoriteCampaignProcedure:
			favoriteCampaignServiceGetAllFavoriteCampaignHandler.ServeHTTP(w, r)
		case FavoriteCampaignServiceDeleteFavoriteCampaignProcedure:
			favoriteCampaignServiceDeleteFavoriteCampaignHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedFavoriteCampaignServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedFavoriteCampaignServiceHandler struct{}

func (UnimplementedFavoriteCampaignServiceHandler) AddFavoriteCampaign(context.Context, *connect.Request[v1.AddFavoriteCampaignRequest]) (*connect.Response[v1.AddFavoriteCampaignResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.socials.v1.FavoriteCampaignService.AddFavoriteCampaign is not implemented"))
}

func (UnimplementedFavoriteCampaignServiceHandler) GetAllFavoriteCampaign(context.Context, *connect.Request[v1.GetAllFavoriteCampaignRequest]) (*connect.Response[v1.GetAllFavoriteCampaignResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.socials.v1.FavoriteCampaignService.GetAllFavoriteCampaign is not implemented"))
}

func (UnimplementedFavoriteCampaignServiceHandler) DeleteFavoriteCampaign(context.Context, *connect.Request[v1.DeleteFavoriteCampaignRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.socials.v1.FavoriteCampaignService.DeleteFavoriteCampaign is not implemented"))
}
