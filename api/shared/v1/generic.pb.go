// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: shared/v1/generic.proto

package sharedv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenericResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        int32                  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenericResponse) Reset() {
	*x = GenericResponse{}
	mi := &file_shared_v1_generic_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenericResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenericResponse) ProtoMessage() {}

func (x *GenericResponse) ProtoReflect() protoreflect.Message {
	mi := &file_shared_v1_generic_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenericResponse.ProtoReflect.Descriptor instead.
func (*GenericResponse) Descriptor() ([]byte, []int) {
	return file_shared_v1_generic_proto_rawDescGZIP(), []int{0}
}

func (x *GenericResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GenericResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GenericResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_shared_v1_generic_proto protoreflect.FileDescriptor

const file_shared_v1_generic_proto_rawDesc = "" +
	"\n" +
	"\x17shared/v1/generic.proto\x12\rapi.shared.v1\"]\n" +
	"\x0fGenericResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccessB2Z0github.com/nsp-inc/vtuber/api/shared/v1;sharedv1b\x06proto3"

var (
	file_shared_v1_generic_proto_rawDescOnce sync.Once
	file_shared_v1_generic_proto_rawDescData []byte
)

func file_shared_v1_generic_proto_rawDescGZIP() []byte {
	file_shared_v1_generic_proto_rawDescOnce.Do(func() {
		file_shared_v1_generic_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_shared_v1_generic_proto_rawDesc), len(file_shared_v1_generic_proto_rawDesc)))
	})
	return file_shared_v1_generic_proto_rawDescData
}

var file_shared_v1_generic_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_shared_v1_generic_proto_goTypes = []any{
	(*GenericResponse)(nil), // 0: api.shared.v1.GenericResponse
}
var file_shared_v1_generic_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_shared_v1_generic_proto_init() }
func file_shared_v1_generic_proto_init() {
	if File_shared_v1_generic_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_shared_v1_generic_proto_rawDesc), len(file_shared_v1_generic_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_shared_v1_generic_proto_goTypes,
		DependencyIndexes: file_shared_v1_generic_proto_depIdxs,
		MessageInfos:      file_shared_v1_generic_proto_msgTypes,
	}.Build()
	File_shared_v1_generic_proto = out.File
	file_shared_v1_generic_proto_goTypes = nil
	file_shared_v1_generic_proto_depIdxs = nil
}
