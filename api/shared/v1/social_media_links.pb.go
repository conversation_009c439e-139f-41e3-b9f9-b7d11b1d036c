// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: shared/v1/social_media_links.proto

package sharedv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SocialMediaLinks struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Twitter       *string                `protobuf:"bytes,1,opt,name=twitter,proto3,oneof" json:"twitter,omitempty" validate:"omitempty,url"`
	Youtube       *string                `protobuf:"bytes,2,opt,name=youtube,proto3,oneof" json:"youtube,omitempty" validate:"omitempty,url"`
	Twitch        *string                `protobuf:"bytes,3,opt,name=twitch,proto3,oneof" json:"twitch,omitempty" validate:"omitempty,url"`
	Tiktok        *string                `protobuf:"bytes,4,opt,name=tiktok,proto3,oneof" json:"tiktok,omitempty" validate:"omitempty,url"`
	Instagram     *string                `protobuf:"bytes,5,opt,name=instagram,proto3,oneof" json:"instagram,omitempty" validate:"omitempty,url"`
	Discord       *string                `protobuf:"bytes,6,opt,name=discord,proto3,oneof" json:"discord,omitempty" validate:"omitempty,url"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SocialMediaLinks) Reset() {
	*x = SocialMediaLinks{}
	mi := &file_shared_v1_social_media_links_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SocialMediaLinks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocialMediaLinks) ProtoMessage() {}

func (x *SocialMediaLinks) ProtoReflect() protoreflect.Message {
	mi := &file_shared_v1_social_media_links_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocialMediaLinks.ProtoReflect.Descriptor instead.
func (*SocialMediaLinks) Descriptor() ([]byte, []int) {
	return file_shared_v1_social_media_links_proto_rawDescGZIP(), []int{0}
}

func (x *SocialMediaLinks) GetTwitter() string {
	if x != nil && x.Twitter != nil {
		return *x.Twitter
	}
	return ""
}

func (x *SocialMediaLinks) GetYoutube() string {
	if x != nil && x.Youtube != nil {
		return *x.Youtube
	}
	return ""
}

func (x *SocialMediaLinks) GetTwitch() string {
	if x != nil && x.Twitch != nil {
		return *x.Twitch
	}
	return ""
}

func (x *SocialMediaLinks) GetTiktok() string {
	if x != nil && x.Tiktok != nil {
		return *x.Tiktok
	}
	return ""
}

func (x *SocialMediaLinks) GetInstagram() string {
	if x != nil && x.Instagram != nil {
		return *x.Instagram
	}
	return ""
}

func (x *SocialMediaLinks) GetDiscord() string {
	if x != nil && x.Discord != nil {
		return *x.Discord
	}
	return ""
}

var File_shared_v1_social_media_links_proto protoreflect.FileDescriptor

const file_shared_v1_social_media_links_proto_rawDesc = "" +
	"\n" +
	"\"shared/v1/social_media_links.proto\x12\rapi.shared.v1\"\x94\x02\n" +
	"\x10SocialMediaLinks\x12\x1d\n" +
	"\atwitter\x18\x01 \x01(\tH\x00R\atwitter\x88\x01\x01\x12\x1d\n" +
	"\ayoutube\x18\x02 \x01(\tH\x01R\ayoutube\x88\x01\x01\x12\x1b\n" +
	"\x06twitch\x18\x03 \x01(\tH\x02R\x06twitch\x88\x01\x01\x12\x1b\n" +
	"\x06tiktok\x18\x04 \x01(\tH\x03R\x06tiktok\x88\x01\x01\x12!\n" +
	"\tinstagram\x18\x05 \x01(\tH\x04R\tinstagram\x88\x01\x01\x12\x1d\n" +
	"\adiscord\x18\x06 \x01(\tH\x05R\adiscord\x88\x01\x01B\n" +
	"\n" +
	"\b_twitterB\n" +
	"\n" +
	"\b_youtubeB\t\n" +
	"\a_twitchB\t\n" +
	"\a_tiktokB\f\n" +
	"\n" +
	"_instagramB\n" +
	"\n" +
	"\b_discordB2Z0github.com/nsp-inc/vtuber/api/shared/v1;sharedv1b\x06proto3"

var (
	file_shared_v1_social_media_links_proto_rawDescOnce sync.Once
	file_shared_v1_social_media_links_proto_rawDescData []byte
)

func file_shared_v1_social_media_links_proto_rawDescGZIP() []byte {
	file_shared_v1_social_media_links_proto_rawDescOnce.Do(func() {
		file_shared_v1_social_media_links_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_shared_v1_social_media_links_proto_rawDesc), len(file_shared_v1_social_media_links_proto_rawDesc)))
	})
	return file_shared_v1_social_media_links_proto_rawDescData
}

var file_shared_v1_social_media_links_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_shared_v1_social_media_links_proto_goTypes = []any{
	(*SocialMediaLinks)(nil), // 0: api.shared.v1.SocialMediaLinks
}
var file_shared_v1_social_media_links_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_shared_v1_social_media_links_proto_init() }
func file_shared_v1_social_media_links_proto_init() {
	if File_shared_v1_social_media_links_proto != nil {
		return
	}
	file_shared_v1_social_media_links_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_shared_v1_social_media_links_proto_rawDesc), len(file_shared_v1_social_media_links_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_shared_v1_social_media_links_proto_goTypes,
		DependencyIndexes: file_shared_v1_social_media_links_proto_depIdxs,
		MessageInfos:      file_shared_v1_social_media_links_proto_msgTypes,
	}.Build()
	File_shared_v1_social_media_links_proto = out.File
	file_shared_v1_social_media_links_proto_goTypes = nil
	file_shared_v1_social_media_links_proto_depIdxs = nil
}
