// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: shared/v1/pagination.proto

package sharedv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PaginationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Size          *int32                 `protobuf:"varint,1,opt,name=size,proto3,oneof" json:"size,omitempty"`
	Page          *int32                 `protobuf:"varint,2,opt,name=page,proto3,oneof" json:"page,omitempty"`
	Sort          *string                `protobuf:"bytes,3,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	Order         *string                `protobuf:"bytes,4,opt,name=order,proto3,oneof" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRequest) Reset() {
	*x = PaginationRequest{}
	mi := &file_shared_v1_pagination_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRequest) ProtoMessage() {}

func (x *PaginationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_shared_v1_pagination_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRequest.ProtoReflect.Descriptor instead.
func (*PaginationRequest) Descriptor() ([]byte, []int) {
	return file_shared_v1_pagination_proto_rawDescGZIP(), []int{0}
}

func (x *PaginationRequest) GetSize() int32 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *PaginationRequest) GetPage() int32 {
	if x != nil && x.Page != nil {
		return *x.Page
	}
	return 0
}

func (x *PaginationRequest) GetSort() string {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return ""
}

func (x *PaginationRequest) GetOrder() string {
	if x != nil && x.Order != nil {
		return *x.Order
	}
	return ""
}

type PaginationDetails struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TotalItems    int32                  `protobuf:"varint,1,opt,name=total_items,json=totalItems,proto3" json:"total_items,omitempty"`
	TotalPages    int32                  `protobuf:"varint,2,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	CurrentPage   int32                  `protobuf:"varint,3,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	NextPage      int32                  `protobuf:"varint,5,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	PrevPage      int32                  `protobuf:"varint,6,opt,name=prev_page,json=prevPage,proto3" json:"prev_page,omitempty"`
	Links         []*PageLinks           `protobuf:"bytes,7,rep,name=links,proto3" json:"links,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationDetails) Reset() {
	*x = PaginationDetails{}
	mi := &file_shared_v1_pagination_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationDetails) ProtoMessage() {}

func (x *PaginationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_shared_v1_pagination_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationDetails.ProtoReflect.Descriptor instead.
func (*PaginationDetails) Descriptor() ([]byte, []int) {
	return file_shared_v1_pagination_proto_rawDescGZIP(), []int{1}
}

func (x *PaginationDetails) GetTotalItems() int32 {
	if x != nil {
		return x.TotalItems
	}
	return 0
}

func (x *PaginationDetails) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

func (x *PaginationDetails) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *PaginationDetails) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PaginationDetails) GetNextPage() int32 {
	if x != nil {
		return x.NextPage
	}
	return 0
}

func (x *PaginationDetails) GetPrevPage() int32 {
	if x != nil {
		return x.PrevPage
	}
	return 0
}

func (x *PaginationDetails) GetLinks() []*PageLinks {
	if x != nil {
		return x.Links
	}
	return nil
}

type PageLinks struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsDot         *bool                  `protobuf:"varint,1,opt,name=is_dot,json=isDot,proto3,oneof" json:"is_dot,omitempty"`
	Page          *int32                 `protobuf:"varint,2,opt,name=page,proto3,oneof" json:"page,omitempty"`
	IsNext        *bool                  `protobuf:"varint,3,opt,name=is_next,json=isNext,proto3,oneof" json:"is_next,omitempty"`
	IsActive      *bool                  `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	IsPrevious    *bool                  `protobuf:"varint,5,opt,name=is_previous,json=isPrevious,proto3,oneof" json:"is_previous,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PageLinks) Reset() {
	*x = PageLinks{}
	mi := &file_shared_v1_pagination_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageLinks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageLinks) ProtoMessage() {}

func (x *PageLinks) ProtoReflect() protoreflect.Message {
	mi := &file_shared_v1_pagination_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageLinks.ProtoReflect.Descriptor instead.
func (*PageLinks) Descriptor() ([]byte, []int) {
	return file_shared_v1_pagination_proto_rawDescGZIP(), []int{2}
}

func (x *PageLinks) GetIsDot() bool {
	if x != nil && x.IsDot != nil {
		return *x.IsDot
	}
	return false
}

func (x *PageLinks) GetPage() int32 {
	if x != nil && x.Page != nil {
		return *x.Page
	}
	return 0
}

func (x *PageLinks) GetIsNext() bool {
	if x != nil && x.IsNext != nil {
		return *x.IsNext
	}
	return false
}

func (x *PageLinks) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *PageLinks) GetIsPrevious() bool {
	if x != nil && x.IsPrevious != nil {
		return *x.IsPrevious
	}
	return false
}

var File_shared_v1_pagination_proto protoreflect.FileDescriptor

const file_shared_v1_pagination_proto_rawDesc = "" +
	"\n" +
	"\x1ashared/v1/pagination.proto\x12\rapi.shared.v1\"\x9e\x01\n" +
	"\x11PaginationRequest\x12\x17\n" +
	"\x04size\x18\x01 \x01(\x05H\x00R\x04size\x88\x01\x01\x12\x17\n" +
	"\x04page\x18\x02 \x01(\x05H\x01R\x04page\x88\x01\x01\x12\x17\n" +
	"\x04sort\x18\x03 \x01(\tH\x02R\x04sort\x88\x01\x01\x12\x19\n" +
	"\x05order\x18\x04 \x01(\tH\x03R\x05order\x88\x01\x01B\a\n" +
	"\x05_sizeB\a\n" +
	"\x05_pageB\a\n" +
	"\x05_sortB\b\n" +
	"\x06_order\"\xff\x01\n" +
	"\x11PaginationDetails\x12\x1f\n" +
	"\vtotal_items\x18\x01 \x01(\x05R\n" +
	"totalItems\x12\x1f\n" +
	"\vtotal_pages\x18\x02 \x01(\x05R\n" +
	"totalPages\x12!\n" +
	"\fcurrent_page\x18\x03 \x01(\x05R\vcurrentPage\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12\x1b\n" +
	"\tnext_page\x18\x05 \x01(\x05R\bnextPage\x12\x1b\n" +
	"\tprev_page\x18\x06 \x01(\x05R\bprevPage\x12.\n" +
	"\x05links\x18\a \x03(\v2\x18.api.shared.v1.PageLinksR\x05links\"\xe4\x01\n" +
	"\tPageLinks\x12\x1a\n" +
	"\x06is_dot\x18\x01 \x01(\bH\x00R\x05isDot\x88\x01\x01\x12\x17\n" +
	"\x04page\x18\x02 \x01(\x05H\x01R\x04page\x88\x01\x01\x12\x1c\n" +
	"\ais_next\x18\x03 \x01(\bH\x02R\x06isNext\x88\x01\x01\x12 \n" +
	"\tis_active\x18\x04 \x01(\bH\x03R\bisActive\x88\x01\x01\x12$\n" +
	"\vis_previous\x18\x05 \x01(\bH\x04R\n" +
	"isPrevious\x88\x01\x01B\t\n" +
	"\a_is_dotB\a\n" +
	"\x05_pageB\n" +
	"\n" +
	"\b_is_nextB\f\n" +
	"\n" +
	"_is_activeB\x0e\n" +
	"\f_is_previousB2Z0github.com/nsp-inc/vtuber/api/shared/v1;sharedv1b\x06proto3"

var (
	file_shared_v1_pagination_proto_rawDescOnce sync.Once
	file_shared_v1_pagination_proto_rawDescData []byte
)

func file_shared_v1_pagination_proto_rawDescGZIP() []byte {
	file_shared_v1_pagination_proto_rawDescOnce.Do(func() {
		file_shared_v1_pagination_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_shared_v1_pagination_proto_rawDesc), len(file_shared_v1_pagination_proto_rawDesc)))
	})
	return file_shared_v1_pagination_proto_rawDescData
}

var file_shared_v1_pagination_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_shared_v1_pagination_proto_goTypes = []any{
	(*PaginationRequest)(nil), // 0: api.shared.v1.PaginationRequest
	(*PaginationDetails)(nil), // 1: api.shared.v1.PaginationDetails
	(*PageLinks)(nil),         // 2: api.shared.v1.PageLinks
}
var file_shared_v1_pagination_proto_depIdxs = []int32{
	2, // 0: api.shared.v1.PaginationDetails.links:type_name -> api.shared.v1.PageLinks
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_shared_v1_pagination_proto_init() }
func file_shared_v1_pagination_proto_init() {
	if File_shared_v1_pagination_proto != nil {
		return
	}
	file_shared_v1_pagination_proto_msgTypes[0].OneofWrappers = []any{}
	file_shared_v1_pagination_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_shared_v1_pagination_proto_rawDesc), len(file_shared_v1_pagination_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_shared_v1_pagination_proto_goTypes,
		DependencyIndexes: file_shared_v1_pagination_proto_depIdxs,
		MessageInfos:      file_shared_v1_pagination_proto_msgTypes,
	}.Build()
	File_shared_v1_pagination_proto = out.File
	file_shared_v1_pagination_proto_goTypes = nil
	file_shared_v1_pagination_proto_depIdxs = nil
}
