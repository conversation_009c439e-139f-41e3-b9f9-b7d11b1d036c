// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: shared/v1/errors.proto

package sharedv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ValidationErrorDetails struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidationErrorDetails) Reset() {
	*x = ValidationErrorDetails{}
	mi := &file_shared_v1_errors_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidationErrorDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidationErrorDetails) ProtoMessage() {}

func (x *ValidationErrorDetails) ProtoReflect() protoreflect.Message {
	mi := &file_shared_v1_errors_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidationErrorDetails.ProtoReflect.Descriptor instead.
func (*ValidationErrorDetails) Descriptor() ([]byte, []int) {
	return file_shared_v1_errors_proto_rawDescGZIP(), []int{0}
}

func (x *ValidationErrorDetails) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *ValidationErrorDetails) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ValidationError struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Details       []*ValidationErrorDetails `protobuf:"bytes,1,rep,name=details,proto3" json:"details,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidationError) Reset() {
	*x = ValidationError{}
	mi := &file_shared_v1_errors_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidationError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidationError) ProtoMessage() {}

func (x *ValidationError) ProtoReflect() protoreflect.Message {
	mi := &file_shared_v1_errors_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidationError.ProtoReflect.Descriptor instead.
func (*ValidationError) Descriptor() ([]byte, []int) {
	return file_shared_v1_errors_proto_rawDescGZIP(), []int{1}
}

func (x *ValidationError) GetDetails() []*ValidationErrorDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

var File_shared_v1_errors_proto protoreflect.FileDescriptor

const file_shared_v1_errors_proto_rawDesc = "" +
	"\n" +
	"\x16shared/v1/errors.proto\x12\rapi.shared.v1\"H\n" +
	"\x16ValidationErrorDetails\x12\x14\n" +
	"\x05field\x18\x01 \x01(\tR\x05field\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"R\n" +
	"\x0fValidationError\x12?\n" +
	"\adetails\x18\x01 \x03(\v2%.api.shared.v1.ValidationErrorDetailsR\adetailsB2Z0github.com/nsp-inc/vtuber/api/shared/v1;sharedv1b\x06proto3"

var (
	file_shared_v1_errors_proto_rawDescOnce sync.Once
	file_shared_v1_errors_proto_rawDescData []byte
)

func file_shared_v1_errors_proto_rawDescGZIP() []byte {
	file_shared_v1_errors_proto_rawDescOnce.Do(func() {
		file_shared_v1_errors_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_shared_v1_errors_proto_rawDesc), len(file_shared_v1_errors_proto_rawDesc)))
	})
	return file_shared_v1_errors_proto_rawDescData
}

var file_shared_v1_errors_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_shared_v1_errors_proto_goTypes = []any{
	(*ValidationErrorDetails)(nil), // 0: api.shared.v1.ValidationErrorDetails
	(*ValidationError)(nil),        // 1: api.shared.v1.ValidationError
}
var file_shared_v1_errors_proto_depIdxs = []int32{
	0, // 0: api.shared.v1.ValidationError.details:type_name -> api.shared.v1.ValidationErrorDetails
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_shared_v1_errors_proto_init() }
func file_shared_v1_errors_proto_init() {
	if File_shared_v1_errors_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_shared_v1_errors_proto_rawDesc), len(file_shared_v1_errors_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_shared_v1_errors_proto_goTypes,
		DependencyIndexes: file_shared_v1_errors_proto_depIdxs,
		MessageInfos:      file_shared_v1_errors_proto_msgTypes,
	}.Build()
	File_shared_v1_errors_proto = out.File
	file_shared_v1_errors_proto_goTypes = nil
	file_shared_v1_errors_proto_depIdxs = nil
}
