package sharedv1

import (
	"math"
	"slices"
	"strings"

	"github.com/nsp-inc/vtuber/packages/helpers"
)

func NewGenericResponse(message string, statusCode int32, success bool) *GenericResponse {
	return &GenericResponse{
		Message: message,
		Status:  statusCode,
		Success: success,
	}

}

type PaginationRequestInfo struct {
	Page           int32
	Offset         int32
	PageSize       int32
	OrderBy        string
	OrderDirection string
}

type CountedRow interface {
	Count() int32
}

func GetPaginationRequestInfo(paginationRequest *PaginationRequest, availableOrdering []string) PaginationRequestInfo {
	var page int32 = 1
	var pageSize int32 = 10
	var orderBy string = "id"
	var orderDirection string = "ASC"
	if paginationRequest != nil {
		if paginationRequest.Page != nil && *paginationRequest.Page > 0 {
			page = *paginationRequest.Page
		}
		if paginationRequest.Size != nil && *paginationRequest.Size > 0 {
			pageSize = *paginationRequest.Size
		}
		if paginationRequest.Sort != nil && *paginationRequest.Sort != "" {
			if slices.Contains(availableOrdering, *paginationRequest.Sort) {
				orderBy = *paginationRequest.Sort
			}
		}
		if paginationRequest.Order != nil && *paginationRequest.Order != "" {
			upper := strings.ToUpper(*paginationRequest.Order)
			if upper == "DESC" || upper == "ASC" {
				orderDirection = upper
			}
		}
	}
	return PaginationRequestInfo{
		Page:           page,
		PageSize:       pageSize,
		OrderBy:        orderBy,
		OrderDirection: orderDirection,
		Offset:         (page - 1) * pageSize,
	}
}

func GetPaginationResponseInfo(request PaginationRequestInfo, row CountedRow) *PaginationDetails {
	isLastPage := false
	totalCount := int32(0)
	if !helpers.IsNilInterface(row) {
		totalCount = (row).Count()
	}
	if totalCount <= request.Offset+request.PageSize {
		isLastPage = true
	}

	isPrevPage := false
	if request.Page > 1 {
		isPrevPage = true
	}
	nextPage := request.Page
	if !isLastPage {
		nextPage++
	}
	prevPage := request.Page
	if isPrevPage {
		prevPage--
	}

	return &PaginationDetails{
		TotalItems:  totalCount,
		PageSize:    request.PageSize,
		CurrentPage: request.Page,
		NextPage:    nextPage,
		PrevPage:    prevPage,
		TotalPages:  int32(math.Ceil(float64(totalCount) / float64(request.PageSize))),
	}
}
