// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: shared/v1/profile.proto

package sharedv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Profile struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Image            *string                `protobuf:"bytes,3,opt,name=image,proto3,oneof" json:"image,omitempty"`
	Furigana         *string                `protobuf:"bytes,4,opt,name=furigana,proto3,oneof" json:"furigana,omitempty"`
	Introduction     *string                `protobuf:"bytes,5,opt,name=introduction,proto3,oneof" json:"introduction,omitempty"`
	SocialMediaLinks *SocialMediaLinks      `protobuf:"bytes,6,opt,name=social_media_links,json=socialMediaLinks,proto3,oneof" json:"social_media_links,omitempty"`
	Username         *string                `protobuf:"bytes,7,opt,name=username,proto3,oneof" json:"username,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Profile) Reset() {
	*x = Profile{}
	mi := &file_shared_v1_profile_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Profile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Profile) ProtoMessage() {}

func (x *Profile) ProtoReflect() protoreflect.Message {
	mi := &file_shared_v1_profile_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Profile.ProtoReflect.Descriptor instead.
func (*Profile) Descriptor() ([]byte, []int) {
	return file_shared_v1_profile_proto_rawDescGZIP(), []int{0}
}

func (x *Profile) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Profile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Profile) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

func (x *Profile) GetFurigana() string {
	if x != nil && x.Furigana != nil {
		return *x.Furigana
	}
	return ""
}

func (x *Profile) GetIntroduction() string {
	if x != nil && x.Introduction != nil {
		return *x.Introduction
	}
	return ""
}

func (x *Profile) GetSocialMediaLinks() *SocialMediaLinks {
	if x != nil {
		return x.SocialMediaLinks
	}
	return nil
}

func (x *Profile) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

var File_shared_v1_profile_proto protoreflect.FileDescriptor

const file_shared_v1_profile_proto_rawDesc = "" +
	"\n" +
	"\x17shared/v1/profile.proto\x12\rapi.shared.v1\x1a\"shared/v1/social_media_links.proto\"\xd3\x02\n" +
	"\aProfile\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x19\n" +
	"\x05image\x18\x03 \x01(\tH\x00R\x05image\x88\x01\x01\x12\x1f\n" +
	"\bfurigana\x18\x04 \x01(\tH\x01R\bfurigana\x88\x01\x01\x12'\n" +
	"\fintroduction\x18\x05 \x01(\tH\x02R\fintroduction\x88\x01\x01\x12R\n" +
	"\x12social_media_links\x18\x06 \x01(\v2\x1f.api.shared.v1.SocialMediaLinksH\x03R\x10socialMediaLinks\x88\x01\x01\x12\x1f\n" +
	"\busername\x18\a \x01(\tH\x04R\busername\x88\x01\x01B\b\n" +
	"\x06_imageB\v\n" +
	"\t_furiganaB\x0f\n" +
	"\r_introductionB\x15\n" +
	"\x13_social_media_linksB\v\n" +
	"\t_usernameB2Z0github.com/nsp-inc/vtuber/api/shared/v1;sharedv1b\x06proto3"

var (
	file_shared_v1_profile_proto_rawDescOnce sync.Once
	file_shared_v1_profile_proto_rawDescData []byte
)

func file_shared_v1_profile_proto_rawDescGZIP() []byte {
	file_shared_v1_profile_proto_rawDescOnce.Do(func() {
		file_shared_v1_profile_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_shared_v1_profile_proto_rawDesc), len(file_shared_v1_profile_proto_rawDesc)))
	})
	return file_shared_v1_profile_proto_rawDescData
}

var file_shared_v1_profile_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_shared_v1_profile_proto_goTypes = []any{
	(*Profile)(nil),          // 0: api.shared.v1.Profile
	(*SocialMediaLinks)(nil), // 1: api.shared.v1.SocialMediaLinks
}
var file_shared_v1_profile_proto_depIdxs = []int32{
	1, // 0: api.shared.v1.Profile.social_media_links:type_name -> api.shared.v1.SocialMediaLinks
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_shared_v1_profile_proto_init() }
func file_shared_v1_profile_proto_init() {
	if File_shared_v1_profile_proto != nil {
		return
	}
	file_shared_v1_social_media_links_proto_init()
	file_shared_v1_profile_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_shared_v1_profile_proto_rawDesc), len(file_shared_v1_profile_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_shared_v1_profile_proto_goTypes,
		DependencyIndexes: file_shared_v1_profile_proto_depIdxs,
		MessageInfos:      file_shared_v1_profile_proto_msgTypes,
	}.Build()
	File_shared_v1_profile_proto = out.File
	file_shared_v1_profile_proto_goTypes = nil
	file_shared_v1_profile_proto_depIdxs = nil
}
