<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="SqlDialectMappings">
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00001_add_enums.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00002_add_users_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00003_add_accounts_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00004_add_sessions_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00005_add_verification_codes_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00006_add_categories_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00007_add_vtuber_profiles_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00009_add_events_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00010_add_posts_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00011_add_event_comments_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00012_add_post_comments_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00013_add_post_likes_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00014_add_campaign_variants_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00015_add_vtuber_requests_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00016_add_campaign_banners_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00017_add_vtuber_plans_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00018_add_vtuber_user_subscription_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00019_add_faqs_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00020_add_notification_templates_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00021_add_notifications_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00022_add_event_participants_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00023_add_event_participants_vote_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00024_add_campaign_variant_subscriptions_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00025_add_user_billing_infos_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00026_add_transactions_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00027_add_user_points_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00028_add_static_data_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00029_add_favorite_vtubers_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00030_add_favorite_campaigns_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00031_add_files_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00032_add_announcements_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00033_add_vtuber_banners.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00034_add_vtuber_gallery.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00035_add_campaign_categories_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00036_add_event_categories_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00037_add_vtuber_categories_table.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/internal/db/migrations/v3/00038_add_user_delivery_address.sql" dialect="PostgreSQL" />
    <file url="PROJECT" dialect="PostgreSQL" />
  </component>
</project>