<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="GoDfaErrorMayBeNotNil" enabled="true" level="WARNING" enabled_by_default="true">
      <methods>
        <method importPath="github.com/nsp-inc/vtuber/internal/vtuberprofiles/v1/repo" receiver="*Queries" name="GetVtuberProfileByID" />
        <method importPath="github.com/nsp-inc/vtuber/internal/creatorusersubscription/v1/repo" receiver="*Queries" name="GetCreatorUserSubscriptionOfThisMonth" />
      </methods>
    </inspection_tool>
  </profile>
</component>