name: API Dev Build

on:
  push:
    branches: ["feature"]
    paths:
      - "internal/**"
      - "packages/**"
      - "cmd/main.go"
      - "deploy/backend/**"
      - "go.mod"
      - "go.sum"

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./deploy/backend/Dockerfile
          push: true
          tags: ${{ secrets.DOCKER_HUB_USERNAME }}/vtuberbackend:dev
          cache-from: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/vtuberbackend:buildcache
          cache-to: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/vtuberbackend:buildcache,mode=max
      - name: Deploy
        run: curl '${{ secrets.DEPLOY_API_DEV }}'